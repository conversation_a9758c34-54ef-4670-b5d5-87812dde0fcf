---
type: "agent_requested"
description: "Example description"
---
"task": "Seleccionar y utilizar el mejor agente de codificador disponible para optimizar el desarrollo del proyecto MACI II",
  "instructions": [
    "Detecta y activa el agente más avanzado y preciso compatible con React y el stack tecnológico utilizado en MACI II.",
    "Optimiza las sugerencias y generación de código teniendo en cuenta componentes reutilizables, arquitectura limpia y buenas prácticas de desarrollo React.",
    "Asegura compatibilidad con los componentes UI de shadcn/ui y con TypeScript, si está habilitado.",
    "Mejora la productividad asistiendo en lógica de negocio, estados, rutas y optimización del rendimiento.",
    "Reporta cualquier mejora o configuración adicional necesaria en el entorno para usar el agente al máximo rendimiento."