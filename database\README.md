# 🗄️ Configuración de Base de Datos BAT-7

Este directorio contiene los archivos necesarios para configurar la base de datos de Supabase para el proyecto BAT-7.

## 📋 Archivos

- **`schema.sql`** - Esquema completo de la base de datos con tablas, índices, funciones y políticas RLS
- **`seed_data.sql`** - Datos de ejemplo para desarrollo y testing
- **`README.md`** - Este archivo con instrucciones

## 🚀 Configuración Rápida

### Opción 1: Script Automático (Recomendado)

1. **Obtener Service Key de Supabase:**
   - Ve a [Supabase Dashboard](https://app.supabase.com/project/ydglduxhgwajqdseqzpy/settings/api)
   - Copia la "service_role" key (NO la anon key)

2. **Configurar variable de entorno:**
   ```bash
   # En Windows (PowerShell)
   $env:SUPABASE_SERVICE_KEY="tu_service_key_aqui"
   
   # En macOS/Linux
   export SUPABASE_SERVICE_KEY="tu_service_key_aqui"
   ```

3. **Ejecutar script de configuración:**
   ```bash
   cd scripts
   node setup-database.js
   ```

### Opción 2: Manual desde Supabase Dashboard

1. **Ir al SQL Editor:**
   - Abre [Supabase Dashboard](https://app.supabase.com/project/ydglduxhgwajqdseqzpy/sql)
   - Ve a la sección "SQL Editor"

2. **Ejecutar esquema:**
   - Copia todo el contenido de `schema.sql`
   - Pégalo en el editor SQL
   - Haz clic en "Run"

3. **Ejecutar datos de ejemplo:**
   - Copia todo el contenido de `seed_data.sql`
   - Pégalo en el editor SQL
   - Haz clic en "Run"

## 📊 Estructura de la Base de Datos

### Tablas Principales

| Tabla | Descripción |
|-------|-------------|
| `instituciones` | Universidades, colegios, empresas |
| `usuarios` | Candidatos, psicólogos, administradores |
| `cuestionarios` | Tests y evaluaciones |
| `respuestas` | Respuestas de candidatos a cuestionarios |
| `informes` | Reportes generados por psicólogos |
| `sesiones_evaluacion` | Sesiones programadas de evaluación |
| `auditoria` | Log de acciones del sistema |

### Tipos de Usuario

- **`administrador`** - Acceso completo al sistema
- **`psicologo`** - Puede crear cuestionarios, ver resultados y generar informes
- **`candidato`** - Puede responder cuestionarios y ver sus resultados

### Funciones Importantes

- **`get_dashboard_stats()`** - Obtiene estadísticas para el dashboard
- **`get_candidato_stats(candidato_uuid)`** - Estadísticas de un candidato específico
- **`get_candidatos_ranking(cuestionario_uuid)`** - Ranking de candidatos por puntuación

## 🔒 Seguridad (RLS)

El esquema incluye políticas de Row Level Security (RLS) que garantizan:

- Los candidatos solo ven sus propios datos
- Los psicólogos ven candidatos de su institución
- Los administradores tienen acceso completo
- Todas las operaciones están auditadas

## 👥 Usuarios de Ejemplo

Después de ejecutar `seed_data.sql`, tendrás estos usuarios disponibles:

### Administrador
- **Email:** <EMAIL>
- **Documento:** ADM001
- **Tipo:** administrador

### Psicólogos
- **Email:** <EMAIL>
- **Documento:** PSI001
- **Especialidad:** Psicología Clínica

- **Email:** <EMAIL>
- **Documento:** PSI002
- **Especialidad:** Psicología Organizacional

### Candidatos
- **Email:** <EMAIL>
- **Documento:** CAN001
- **Nombre:** Juan Pérez

- **Email:** <EMAIL>
- **Documento:** CAN002
- **Nombre:** Ana López

*(Y 3 candidatos más...)*

## 📝 Cuestionarios de Ejemplo

Se crean 2 cuestionarios de ejemplo:

1. **BAT-7 Evaluación Verbal** (45 min)
   - Vocabulario
   - Analogías
   - Gramática

2. **BAT-7 Evaluación Numérica** (60 min)
   - Porcentajes
   - Secuencias numéricas

## 🔧 Solución de Problemas

### Error: "Could not find the function get_dashboard_stats"

**Causa:** La función no se creó correctamente.

**Solución:**
1. Ve al SQL Editor de Supabase
2. Ejecuta solo la parte de funciones del `schema.sql`:
   ```sql
   CREATE OR REPLACE FUNCTION get_dashboard_stats()
   RETURNS JSON AS $$
   -- ... resto de la función
   ```

### Error: "relation 'usuarios' does not exist"

**Causa:** Las tablas no se crearon.

**Solución:**
1. Ejecuta primero la parte de creación de tablas del `schema.sql`
2. Luego ejecuta las funciones
3. Finalmente ejecuta `seed_data.sql`

### Error: "permission denied for table usuarios"

**Causa:** Problemas con las políticas RLS.

**Solución:**
1. Verifica que las políticas RLS se crearon correctamente
2. Asegúrate de estar usando la service_role key, no la anon key
3. Revisa que el usuario esté autenticado correctamente

## 📞 Soporte

Si tienes problemas con la configuración:

1. Verifica que la URL de Supabase sea correcta
2. Confirma que tienes la service_role key correcta
3. Revisa los logs en Supabase Dashboard > Logs
4. Ejecuta el script de verificación del sistema

## 🔄 Actualizar Esquema

Para actualizar el esquema en el futuro:

1. Modifica `schema.sql` con los cambios necesarios
2. Ejecuta solo las partes modificadas en SQL Editor
3. O ejecuta el script completo (sobrescribirá datos existentes)

## 📈 Monitoreo

Puedes monitorear el estado de la base de datos:

- **Dashboard de Supabase:** Métricas en tiempo real
- **Tabla de auditoría:** Log de todas las acciones
- **Función get_dashboard_stats():** Estadísticas del sistema
