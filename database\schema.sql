-- Esquema de base de datos para BAT-7
-- Sistema de Evaluación Psicométrica

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Crear tipos ENUM
CREATE TYPE user_type AS ENUM ('administrador', 'psicologo', 'candidato');
CREATE TYPE institution_type AS ENUM ('universidad', 'colegio', 'instituto', 'empresa', 'otro');
CREATE TYPE questionnaire_status AS ENUM ('draft', 'active', 'inactive', 'archived');
CREATE TYPE response_status AS ENUM ('started', 'completed', 'abandoned');
CREATE TYPE report_status AS ENUM ('draft', 'completed', 'shared');

-- Tabla de instituciones
CREATE TABLE instituciones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nombre VARCHAR(255) NOT NULL,
    tipo institution_type NOT NULL DEFAULT 'universidad',
    direccion TEXT,
    telefono VARCHAR(20),
    email VARCHAR(255),
    sitio_web VARCHAR(255),
    activa BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de usuarios (candidatos, psicólogos, administradores)
CREATE TABLE usuarios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_id UUID UNIQUE, -- ID de Supabase Auth
    email VARCHAR(255) UNIQUE NOT NULL,
    nombre VARCHAR(100) NOT NULL,
    apellido VARCHAR(100) NOT NULL,
    documento VARCHAR(50) UNIQUE NOT NULL,
    telefono VARCHAR(20),
    fecha_nacimiento DATE,
    tipo_usuario user_type NOT NULL DEFAULT 'candidato',
    institucion_id UUID REFERENCES instituciones(id),
    especialidad VARCHAR(255), -- Para psicólogos
    numero_licencia VARCHAR(100), -- Para psicólogos
    biografia TEXT,
    sitio_web VARCHAR(255),
    linkedin VARCHAR(255),
    activo BOOLEAN DEFAULT true,
    ultimo_acceso TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de cuestionarios
CREATE TABLE cuestionarios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    titulo VARCHAR(255) NOT NULL,
    descripcion TEXT,
    instrucciones TEXT,
    duracion_minutos INTEGER DEFAULT 60,
    categoria VARCHAR(100),
    dificultad VARCHAR(20) DEFAULT 'medium',
    version VARCHAR(10) DEFAULT '1.0',
    status questionnaire_status DEFAULT 'draft',
    preguntas JSONB NOT NULL DEFAULT '[]',
    configuracion JSONB DEFAULT '{}',
    creado_por UUID REFERENCES usuarios(id),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de respuestas a cuestionarios
CREATE TABLE respuestas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cuestionario_id UUID REFERENCES cuestionarios(id) NOT NULL,
    candidato_id UUID REFERENCES usuarios(id) NOT NULL,
    respuestas JSONB NOT NULL DEFAULT '{}',
    puntuacion_total DECIMAL(5,2),
    puntuaciones_categoria JSONB DEFAULT '{}',
    tiempo_empleado INTEGER, -- en segundos
    fecha_inicio TIMESTAMP WITH TIME ZONE,
    fecha_finalizacion TIMESTAMP WITH TIME ZONE,
    status response_status DEFAULT 'started',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(cuestionario_id, candidato_id)
);

-- Tabla de informes
CREATE TABLE informes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    titulo VARCHAR(255) NOT NULL,
    tipo VARCHAR(50) DEFAULT 'individual', -- individual, grupal
    cuestionario_id UUID REFERENCES cuestionarios(id),
    candidato_ids UUID[] NOT NULL,
    psicologo_id UUID REFERENCES usuarios(id),
    contenido JSONB NOT NULL DEFAULT '{}',
    configuracion JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    status report_status DEFAULT 'draft',
    compartido BOOLEAN DEFAULT false,
    fecha_compartido TIMESTAMP WITH TIME ZONE,
    archivo_url VARCHAR(500),
    tamaño_archivo BIGINT,
    formato VARCHAR(10) DEFAULT 'PDF',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de sesiones de evaluación
CREATE TABLE sesiones_evaluacion (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cuestionario_id UUID REFERENCES cuestionarios(id) NOT NULL,
    candidato_id UUID REFERENCES usuarios(id) NOT NULL,
    psicologo_id UUID REFERENCES usuarios(id),
    fecha_programada TIMESTAMP WITH TIME ZONE,
    fecha_inicio TIMESTAMP WITH TIME ZONE,
    fecha_fin TIMESTAMP WITH TIME ZONE,
    duracion_real INTEGER, -- en segundos
    observaciones TEXT,
    configuracion JSONB DEFAULT '{}',
    activa BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de auditoría
CREATE TABLE auditoria (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    usuario_id UUID REFERENCES usuarios(id),
    accion VARCHAR(100) NOT NULL,
    tabla_afectada VARCHAR(100),
    registro_id UUID,
    datos_anteriores JSONB,
    datos_nuevos JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para optimización
CREATE INDEX idx_usuarios_email ON usuarios(email);
CREATE INDEX idx_usuarios_documento ON usuarios(documento);
CREATE INDEX idx_usuarios_tipo ON usuarios(tipo_usuario);
CREATE INDEX idx_usuarios_institucion ON usuarios(institucion_id);
CREATE INDEX idx_cuestionarios_status ON cuestionarios(status);
CREATE INDEX idx_cuestionarios_categoria ON cuestionarios(categoria);
CREATE INDEX idx_respuestas_cuestionario ON respuestas(cuestionario_id);
CREATE INDEX idx_respuestas_candidato ON respuestas(candidato_id);
CREATE INDEX idx_respuestas_status ON respuestas(status);
CREATE INDEX idx_informes_psicologo ON informes(psicologo_id);
CREATE INDEX idx_informes_status ON informes(status);
CREATE INDEX idx_sesiones_candidato ON sesiones_evaluacion(candidato_id);
CREATE INDEX idx_sesiones_psicologo ON sesiones_evaluacion(psicologo_id);
CREATE INDEX idx_auditoria_usuario ON auditoria(usuario_id);
CREATE INDEX idx_auditoria_accion ON auditoria(accion);

-- Triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_instituciones_updated_at BEFORE UPDATE ON instituciones FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_usuarios_updated_at BEFORE UPDATE ON usuarios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cuestionarios_updated_at BEFORE UPDATE ON cuestionarios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_respuestas_updated_at BEFORE UPDATE ON respuestas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_informes_updated_at BEFORE UPDATE ON informes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sesiones_updated_at BEFORE UPDATE ON sesiones_evaluacion FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Función para obtener estadísticas del dashboard
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_candidatos', (SELECT COUNT(*) FROM usuarios WHERE tipo_usuario = 'candidato' AND activo = true),
        'total_psicologos', (SELECT COUNT(*) FROM usuarios WHERE tipo_usuario = 'psicologo' AND activo = true),
        'total_cuestionarios', (SELECT COUNT(*) FROM cuestionarios WHERE activo = true),
        'total_respuestas', (SELECT COUNT(*) FROM respuestas WHERE status = 'completed'),
        'total_informes', (SELECT COUNT(*) FROM informes),
        'respuestas_hoy', (
            SELECT COUNT(*) FROM respuestas 
            WHERE DATE(created_at) = CURRENT_DATE AND status = 'completed'
        ),
        'respuestas_semana', (
            SELECT COUNT(*) FROM respuestas 
            WHERE created_at >= CURRENT_DATE - INTERVAL '7 days' AND status = 'completed'
        ),
        'respuestas_mes', (
            SELECT COUNT(*) FROM respuestas 
            WHERE created_at >= CURRENT_DATE - INTERVAL '30 days' AND status = 'completed'
        ),
        'cuestionarios_activos', (SELECT COUNT(*) FROM cuestionarios WHERE status = 'active'),
        'instituciones_activas', (SELECT COUNT(*) FROM instituciones WHERE activa = true)
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para obtener estadísticas de un candidato
CREATE OR REPLACE FUNCTION get_candidato_stats(candidato_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_evaluaciones', (
            SELECT COUNT(*) FROM respuestas 
            WHERE candidato_id = candidato_uuid AND status = 'completed'
        ),
        'puntuacion_promedio', (
            SELECT COALESCE(AVG(puntuacion_total), 0) FROM respuestas 
            WHERE candidato_id = candidato_uuid AND status = 'completed'
        ),
        'ultima_evaluacion', (
            SELECT MAX(fecha_finalizacion) FROM respuestas 
            WHERE candidato_id = candidato_uuid AND status = 'completed'
        ),
        'evaluaciones_pendientes', (
            SELECT COUNT(*) FROM respuestas 
            WHERE candidato_id = candidato_uuid AND status = 'started'
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para obtener ranking de candidatos
CREATE OR REPLACE FUNCTION get_candidatos_ranking(cuestionario_uuid UUID DEFAULT NULL)
RETURNS TABLE(
    candidato_id UUID,
    nombre_completo TEXT,
    puntuacion_total DECIMAL,
    fecha_evaluacion TIMESTAMP WITH TIME ZONE,
    ranking INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.candidato_id,
        CONCAT(u.nombre, ' ', u.apellido) as nombre_completo,
        r.puntuacion_total,
        r.fecha_finalizacion as fecha_evaluacion,
        ROW_NUMBER() OVER (ORDER BY r.puntuacion_total DESC)::INTEGER as ranking
    FROM respuestas r
    JOIN usuarios u ON r.candidato_id = u.id
    WHERE r.status = 'completed'
    AND (cuestionario_uuid IS NULL OR r.cuestionario_id = cuestionario_uuid)
    ORDER BY r.puntuacion_total DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Políticas de seguridad RLS (Row Level Security)
ALTER TABLE usuarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE cuestionarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE respuestas ENABLE ROW LEVEL SECURITY;
ALTER TABLE informes ENABLE ROW LEVEL SECURITY;
ALTER TABLE sesiones_evaluacion ENABLE ROW LEVEL SECURITY;

-- Política para usuarios: pueden ver y editar su propio perfil
CREATE POLICY "usuarios_own_profile" ON usuarios
    FOR ALL USING (auth.uid() = auth_id);

-- Política para administradores: pueden ver todos los usuarios
CREATE POLICY "usuarios_admin_access" ON usuarios
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM usuarios
            WHERE auth_id = auth.uid() AND tipo_usuario = 'administrador'
        )
    );

-- Política para psicólogos: pueden ver candidatos de su institución
CREATE POLICY "usuarios_psicologo_access" ON usuarios
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM usuarios u
            WHERE u.auth_id = auth.uid()
            AND u.tipo_usuario = 'psicologo'
            AND (u.institucion_id = usuarios.institucion_id OR usuarios.tipo_usuario = 'candidato')
        )
    );

-- Política para cuestionarios: administradores y psicólogos pueden ver todos
CREATE POLICY "cuestionarios_access" ON cuestionarios
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM usuarios
            WHERE auth_id = auth.uid()
            AND tipo_usuario IN ('administrador', 'psicologo')
        )
    );

-- Política para respuestas: candidatos ven sus respuestas, psicólogos ven respuestas de sus candidatos
CREATE POLICY "respuestas_candidato_access" ON respuestas
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM usuarios
            WHERE auth_id = auth.uid() AND id = respuestas.candidato_id
        )
    );

CREATE POLICY "respuestas_psicologo_access" ON respuestas
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM usuarios u1
            JOIN usuarios u2 ON u2.id = respuestas.candidato_id
            WHERE u1.auth_id = auth.uid()
            AND u1.tipo_usuario IN ('psicologo', 'administrador')
            AND (u1.institucion_id = u2.institucion_id OR u1.tipo_usuario = 'administrador')
        )
    );

-- Política para informes: solo psicólogos y administradores
CREATE POLICY "informes_access" ON informes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM usuarios
            WHERE auth_id = auth.uid()
            AND tipo_usuario IN ('administrador', 'psicologo')
            AND (id = informes.psicologo_id OR tipo_usuario = 'administrador')
        )
    );
