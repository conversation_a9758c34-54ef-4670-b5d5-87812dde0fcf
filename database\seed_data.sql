-- Datos de ejemplo para BAT-7
-- Ejecutar después de crear el esquema principal

-- Insertar instituciones de ejemplo
INSERT INTO instituciones (id, nombre, tipo, direccion, telefono, email, sitio_web) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Universidad Nacional', 'universidad', 'Calle Principal 123', '+1234567890', '<EMAIL>', 'https://universidad.edu'),
('550e8400-e29b-41d4-a716-446655440002', 'Instituto Tecnológico', 'instituto', 'Avenida Tecnología 456', '+1234567891', '<EMAIL>', 'https://instituto.edu'),
('550e8400-e29b-41d4-a716-************', 'Empresa Consultora ABC', 'empresa', 'Torre Empresarial 789', '+1234567892', '<EMAIL>', 'https://empresa.com');

-- Insertar usuarios de ejemplo
-- Administrador
INSERT INTO usuarios (id, email, nombre, apellido, documento, tipo_usuario, activo) VALUES
('550e8400-e29b-41d4-a716-446655440010', '<EMAIL>', 'Administrador', 'Sistema', 'ADM001', 'administrador', true);

-- Psicólogos
INSERT INTO usuarios (id, email, nombre, apellido, documento, telefono, tipo_usuario, institucion_id, especialidad, numero_licencia, activo) VALUES
('550e8400-e29b-41d4-a716-446655440011', '<EMAIL>', 'María', 'González', 'PSI001', '+1234567893', 'psicologo', '550e8400-e29b-41d4-a716-446655440001', 'Psicología Clínica', 'LIC001', true),
('550e8400-e29b-41d4-a716-446655440012', '<EMAIL>', 'Carlos', 'Rodríguez', 'PSI002', '+1234567894', 'psicologo', '550e8400-e29b-41d4-a716-446655440002', 'Psicología Organizacional', 'LIC002', true);

-- Candidatos
INSERT INTO usuarios (id, email, nombre, apellido, documento, telefono, fecha_nacimiento, tipo_usuario, institucion_id, activo) VALUES
('550e8400-e29b-41d4-a716-446655440020', '<EMAIL>', 'Juan', 'Pérez', 'CAN001', '+1234567895', '1995-05-15', 'candidato', '550e8400-e29b-41d4-a716-446655440001', true),
('550e8400-e29b-41d4-a716-446655440021', '<EMAIL>', 'Ana', 'López', 'CAN002', '+1234567896', '1996-08-22', 'candidato', '550e8400-e29b-41d4-a716-446655440001', true),
('550e8400-e29b-41d4-a716-446655440022', '<EMAIL>', 'Pedro', 'Martínez', 'CAN003', '+1234567897', '1994-12-10', 'candidato', '550e8400-e29b-41d4-a716-446655440002', true),
('550e8400-e29b-41d4-a716-446655440023', '<EMAIL>', 'Laura', 'García', 'CAN004', '+1234567898', '1997-03-18', 'candidato', '550e8400-e29b-41d4-a716-446655440002', true),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Miguel', 'Hernández', 'CAN005', '+1234567899', '1995-11-05', 'candidato', '550e8400-e29b-41d4-a716-************', true);

-- Insertar cuestionarios de ejemplo
INSERT INTO cuestionarios (id, titulo, descripcion, instrucciones, duracion_minutos, categoria, dificultad, status, preguntas, creado_por) VALUES
('550e8400-e29b-41d4-a716-************', 
 'BAT-7 Evaluación Verbal', 
 'Evaluación de aptitudes verbales y comprensión lectora',
 'Lee cuidadosamente cada pregunta y selecciona la respuesta que consideres más apropiada. Tienes 45 minutos para completar la evaluación.',
 45,
 'Aptitud Verbal',
 'medium',
 'active',
 '[
   {
     "id": 1,
     "tipo": "opcion_multiple",
     "pregunta": "¿Cuál es el sinónimo más apropiado para la palabra \"perspicaz\"?",
     "opciones": [
       {"id": "a", "texto": "Confuso"},
       {"id": "b", "texto": "Astuto"},
       {"id": "c", "texto": "Lento"},
       {"id": "d", "texto": "Descuidado"}
     ],
     "respuesta_correcta": "b",
     "puntos": 10,
     "categoria": "vocabulario"
   },
   {
     "id": 2,
     "tipo": "opcion_multiple",
     "pregunta": "Complete la analogía: LIBRO es a BIBLIOTECA como CUADRO es a:",
     "opciones": [
       {"id": "a", "texto": "Marco"},
       {"id": "b", "texto": "Pintura"},
       {"id": "c", "texto": "Museo"},
       {"id": "d", "texto": "Arte"}
     ],
     "respuesta_correcta": "c",
     "puntos": 15,
     "categoria": "analogias"
   },
   {
     "id": 3,
     "tipo": "opcion_multiple",
     "pregunta": "¿Cuál de las siguientes oraciones está correctamente escrita?",
     "opciones": [
       {"id": "a", "texto": "Habían muchas personas en el evento"},
       {"id": "b", "texto": "Había muchas personas en el evento"},
       {"id": "c", "texto": "Hubieron muchas personas en el evento"},
       {"id": "d", "texto": "Habrían muchas personas en el evento"}
     ],
     "respuesta_correcta": "b",
     "puntos": 12,
     "categoria": "gramatica"
   }
 ]'::jsonb,
 '550e8400-e29b-41d4-a716-446655440011'),

('550e8400-e29b-41d4-a716-446655440031',
 'BAT-7 Evaluación Numérica',
 'Evaluación de aptitudes numéricas y razonamiento matemático',
 'Resuelve cada problema matemático seleccionando la respuesta correcta. Puedes usar papel y lápiz para hacer cálculos.',
 60,
 'Aptitud Numérica',
 'medium',
 'active',
 '[
   {
     "id": 1,
     "tipo": "opcion_multiple",
     "pregunta": "Si un producto cuesta $120 y tiene un descuento del 25%, ¿cuál es el precio final?",
     "opciones": [
       {"id": "a", "texto": "$90"},
       {"id": "b", "texto": "$95"},
       {"id": "c", "texto": "$100"},
       {"id": "d", "texto": "$105"}
     ],
     "respuesta_correcta": "a",
     "puntos": 10,
     "categoria": "porcentajes"
   },
   {
     "id": 2,
     "tipo": "opcion_multiple",
     "pregunta": "¿Cuál es el siguiente número en la secuencia: 2, 6, 18, 54, ?",
     "opciones": [
       {"id": "a", "texto": "108"},
       {"id": "b", "texto": "162"},
       {"id": "c", "texto": "216"},
       {"id": "d", "texto": "270"}
     ],
     "respuesta_correcta": "b",
     "puntos": 15,
     "categoria": "secuencias"
   }
 ]'::jsonb,
 '550e8400-e29b-41d4-a716-446655440011');

-- Insertar respuestas de ejemplo
INSERT INTO respuestas (id, cuestionario_id, candidato_id, respuestas, puntuacion_total, tiempo_empleado, fecha_inicio, fecha_finalizacion, status) VALUES
('550e8400-e29b-41d4-a716-446655440040',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440020',
 '{"1": "b", "2": "c", "3": "b"}'::jsonb,
 37.0,
 2100,
 NOW() - INTERVAL '2 hours',
 NOW() - INTERVAL '1 hour 25 minutes',
 'completed'),

('550e8400-e29b-41d4-a716-446655440041',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440021',
 '{"1": "b", "2": "c", "3": "a"}'::jsonb,
 25.0,
 2400,
 NOW() - INTERVAL '1 day',
 NOW() - INTERVAL '23 hours 20 minutes',
 'completed'),

('550e8400-e29b-41d4-a716-446655440042',
 '550e8400-e29b-41d4-a716-446655440031',
 '550e8400-e29b-41d4-a716-446655440022',
 '{"1": "a", "2": "b"}'::jsonb,
 25.0,
 3000,
 NOW() - INTERVAL '3 days',
 NOW() - INTERVAL '2 days 23 hours',
 'completed');

-- Insertar informes de ejemplo
INSERT INTO informes (id, titulo, tipo, cuestionario_id, candidato_ids, psicologo_id, contenido, status) VALUES
('550e8400-e29b-41d4-a716-446655440050',
 'Informe Individual - Juan Pérez',
 'individual',
 '550e8400-e29b-41d4-a716-************',
 ARRAY['550e8400-e29b-41d4-a716-446655440020'],
 '550e8400-e29b-41d4-a716-446655440011',
 '{
   "resumen": "El candidato muestra un buen desempeño en aptitudes verbales",
   "puntuacion_total": 37,
   "fortalezas": ["Vocabulario", "Analogías", "Gramática"],
   "areas_mejora": ["Velocidad de respuesta"],
   "recomendaciones": ["Practicar ejercicios de velocidad lectora"]
 }'::jsonb,
 'completed'),

('550e8400-e29b-41d4-a716-446655440051',
 'Informe Grupal - Universidad Nacional',
 'grupal',
 '550e8400-e29b-41d4-a716-************',
 ARRAY['550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440021'],
 '550e8400-e29b-41d4-a716-446655440011',
 '{
   "resumen": "Análisis comparativo de candidatos de la Universidad Nacional",
   "puntuacion_promedio": 31,
   "mejor_candidato": "Juan Pérez",
   "areas_fortaleza_grupo": ["Vocabulario", "Analogías"],
   "areas_mejora_grupo": ["Gramática"]
 }'::jsonb,
 'completed');

-- Insertar sesiones de evaluación
INSERT INTO sesiones_evaluacion (id, cuestionario_id, candidato_id, psicologo_id, fecha_programada, fecha_inicio, fecha_fin, duracion_real, observaciones) VALUES
('550e8400-e29b-41d4-a716-446655440060',
 '550e8400-e29b-41d4-a716-************',
 '550e8400-e29b-41d4-a716-446655440020',
 '550e8400-e29b-41d4-a716-446655440011',
 NOW() - INTERVAL '2 hours',
 NOW() - INTERVAL '2 hours',
 NOW() - INTERVAL '1 hour 25 minutes',
 2100,
 'Candidato mostró concentración durante toda la evaluación'),

('550e8400-e29b-41d4-a716-446655440061',
 '550e8400-e29b-41d4-a716-446655440031',
 '550e8400-e29b-41d4-a716-446655440022',
 '550e8400-e29b-41d4-a716-446655440012',
 NOW() + INTERVAL '1 day',
 NULL,
 NULL,
 NULL,
 NULL);
