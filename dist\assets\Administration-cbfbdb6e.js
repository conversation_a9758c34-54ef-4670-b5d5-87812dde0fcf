import{r as e,R as a,j as i,e as s,f as t,h as n,Q as r,s as o,b as l}from"./index-165d7974.js";import{C as d,b as c,a as m}from"./Card-54419bd4.js";import{B as u}from"./Button-9c521291.js";import{M as x}from"./Modal-035dd70d.js";const g=({children:s,activeTab:t=0,onChange:n})=>{const[r,o]=e.useState(t);e.useEffect((()=>{o(t)}),[t]);const l=e=>{o(e),n&&n(e)};let d=null,c=[];return e.Children.forEach(s,(e=>{e.type===h?d=a.cloneElement(e,{activeTab:r,onTabChange:l}):e.type===b&&c.push(e)})),i.jsxs("div",{className:"tabs",children:[d,i.jsx("div",{className:"tab-content mt-4",children:c.length>0&&c[r]})]})},h=({children:s,activeTab:t,onTabChange:n})=>i.jsx("div",{className:"border-b border-gray-200",children:i.jsx("nav",{className:"-mb-px flex space-x-8",children:e.Children.map(s,((e,i)=>a.cloneElement(e,{isActive:i===t,onClick:()=>n(i),index:i})))})}),p=({children:e,isActive:a,onClick:s,index:t})=>i.jsx("button",{className:"py-4 px-1 border-b-2 font-medium text-sm "+(a?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),onClick:s,children:e}),b=({children:e})=>i.jsx("div",{children:e}),f=()=>{const[a,r]=e.useState({pendingCount:0,lastSyncAttempt:null,operations:[]}),[o,l]=e.useState(!1),[d,c]=e.useState(!1);e.useEffect((()=>{m();const e=setInterval(m,3e4);return()=>clearInterval(e)}),[]);const m=()=>{const e=s.getSyncStatus();r(e)},u=async()=>{if(!o){l(!0);try{const e=await s.syncPendingOperations();e.success?t(`Sincronización completada. ${e.syncedCount} operaciones sincronizadas.`):n({message:`Sincronización parcial. ${e.syncedCount} operaciones sincronizadas, ${e.errors.length} errores.`}),m()}catch(e){n(e,"sincronizar","datos")}finally{l(!1)}}},x=e=>{if(!e)return"Nunca";return new Date(e).toLocaleString("es-ES",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})},g=e=>{const a={instituciones:"institución",psicologos:"psicólogo",pacientes:"paciente"}[e.entity]||e.entity;return`${{CREATE:"Crear",UPDATE:"Actualizar",DELETE:"Eliminar"}[e.type]||e.type} ${a} (ID: ${e.id})`};return i.jsxs("div",{className:"relative",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsxs("button",{onClick:()=>a.pendingCount>0?u():c(!d),className:"flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors "+(o?"bg-blue-100 text-blue-800":a.pendingCount>0?"bg-yellow-100 text-yellow-800 hover:bg-yellow-200":"bg-green-100 text-green-800 hover:bg-green-200"),disabled:o,title:a.pendingCount>0?"Sincronizar cambios pendientes":"Estado de sincronización",children:[o?i.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):i.jsx("span",{className:"inline-block w-3 h-3 rounded-full mr-2 "+(a.pendingCount>0?"bg-yellow-500 animate-pulse":"bg-green-500")}),o?"Sincronizando...":a.pendingCount>0?`${a.pendingCount} pendiente${1!==a.pendingCount?"s":""}`:"Sincronizado"]}),i.jsx("button",{onClick:()=>c(!d),className:"ml-2 text-gray-500 hover:text-gray-700 focus:outline-none",title:"Ver detalles de sincronización",children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})})]}),d&&i.jsx("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-10 border border-gray-200",children:i.jsxs("div",{className:"p-4",children:[i.jsxs("div",{className:"flex justify-between items-center mb-3",children:[i.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Estado de sincronización"}),i.jsx("button",{onClick:()=>c(!1),className:"text-gray-400 hover:text-gray-600",children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),i.jsxs("div",{className:"bg-gray-50 p-3 rounded-md mb-3",children:[i.jsxs("div",{className:"flex items-center justify-between mb-2",children:[i.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Estado:"}),i.jsx("span",{className:"text-sm font-medium px-2 py-1 rounded-full "+(o?"bg-blue-100 text-blue-800":a.pendingCount>0?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:o?"Sincronizando...":a.pendingCount>0?"Cambios pendientes":"Sincronizado"})]}),i.jsxs("div",{className:"text-sm text-gray-600",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{children:"Última sincronización:"}),i.jsx("span",{className:"font-medium",children:x(a.lastSyncAttempt)})]}),i.jsxs("div",{className:"flex justify-between mt-1",children:[i.jsx("span",{children:"Operaciones pendientes:"}),i.jsx("span",{className:"font-medium "+(a.pendingCount>0?"text-yellow-600":"text-green-600"),children:a.pendingCount})]})]})]}),a.operations.length>0&&i.jsxs("div",{className:"mb-3",children:[i.jsxs("h4",{className:"text-xs font-medium text-gray-700 mb-2 flex items-center",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),"Operaciones pendientes:"]}),i.jsx("div",{className:"bg-gray-50 rounded-md border border-gray-200",children:i.jsx("ul",{className:"text-xs text-gray-600 max-h-40 overflow-y-auto divide-y divide-gray-200",children:a.operations.map(((e,a)=>i.jsxs("li",{className:"p-2 hover:bg-gray-100",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("span",{className:"inline-block w-2 h-2 rounded-full mr-2 "+("CREATE"===e.type?"bg-green-500":"UPDATE"===e.type?"bg-blue-500":"bg-red-500")}),i.jsx("span",{className:"font-medium",children:g(e)})]}),i.jsx("div",{className:"text-gray-400 text-2xs mt-1 ml-4",children:x(e.timestamp)})]},a)))})})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("div",{className:"text-xs text-gray-500",children:a.pendingCount>0?"Los cambios pendientes se sincronizarán automáticamente cuando haya conexión.":"Todos los cambios están sincronizados."}),i.jsx("button",{onClick:u,disabled:o||0===a.pendingCount,className:"px-3 py-1.5 text-xs rounded-md font-medium "+(o?"bg-blue-100 text-blue-800 cursor-not-allowed":a.pendingCount>0?"bg-blue-500 text-white hover:bg-blue-600":"bg-gray-200 text-gray-500 cursor-not-allowed"),children:o?i.jsxs("span",{className:"flex items-center",children:[i.jsxs("svg",{className:"animate-spin -ml-1 mr-1 h-3 w-3 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sincronizando"]}):"Sincronizar ahora"})]})]})})]})},y=({columns:a=[],data:s=[],sortField:t="",sortDirection:n="asc",onSort:r,loading:o=!1,enableActions:l=!0,onEdit:d,onDelete:c,isTemporaryFn:m,itemsPerPage:x=10,emptyMessage:g="No hay datos disponibles",actionLabels:h={edit:"Editar",delete:"Eliminar"}})=>{const[p,b]=e.useState(1);e.useEffect((()=>{b(1)}),[s.length]);const f=(e,a,i)=>{if(e.render)return e.render(a[e.field],a,i);const s=a[e.field];return null==s?e.emptyValue||"-":s},y=p*x,v=y-x,j=s.slice(v,y),N=Math.ceil(s.length/x),w=e=>b(e);return i.jsxs("div",{className:"relative",children:[o&&i.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-80 z-10 flex items-center justify-center",children:i.jsxs("div",{className:"text-center",children:[i.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mb-2"}),i.jsx("p",{className:"text-gray-700",children:"Cargando datos..."})]})}),o||0!==s.length?i.jsxs("div",{className:"overflow-x-auto shadow-sm rounded-lg border border-gray-200",children:[i.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[i.jsx("thead",{className:"bg-gray-50",children:i.jsxs("tr",{children:[a.map(((e,a)=>i.jsx("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${!1!==e.sortable?"cursor-pointer hover:bg-gray-100":""} ${e.width?e.width:""}`,onClick:()=>{return!1!==e.sortable&&(a=e.field,void(r&&r(a)));var a},children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("span",{children:e.header}),!1!==e.sortable&&i.jsx("span",{className:"ml-2",children:t===e.field?"asc"===n?i.jsx("span",{className:"text-blue-500",children:"▲"}):i.jsx("span",{className:"text-blue-500",children:"▼"}):i.jsx("span",{className:"text-gray-400",children:"⇅"})})]})},a))),l&&i.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32",children:"Acciones"})]})}),i.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:j.map(((e,s)=>i.jsxs("tr",{className:"hover:bg-gray-50 "+(m&&m(e.id)?"bg-yellow-50 opacity-80 italic":""),children:[a.map(((a,t)=>i.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${"numeric"===a.type?"text-right":""} ${a.highlight?"font-medium text-gray-900":"text-gray-500"}`,children:f(a,e,s)},`${s}-${t}`))),l&&i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium",children:i.jsxs("div",{className:"flex justify-center space-x-3",children:[d&&i.jsx("button",{onClick:()=>d(e),className:"bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 transition-colors p-2 rounded-full",title:h.edit,children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})})}),c&&i.jsx("button",{onClick:()=>c(e.id,e),className:"bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-800 transition-colors p-2 rounded-full",title:h.delete,children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"})})})]})})]},e.id||s)))})]}),N>1&&i.jsxs("div",{className:"px-6 py-3 flex items-center justify-between border-t border-gray-200 bg-white",children:[i.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[i.jsx(u,{variant:"outline",onClick:()=>w(p-1),disabled:1===p,size:"sm",children:"Anterior"}),i.jsx(u,{variant:"outline",onClick:()=>w(p+1),disabled:p===N,size:"sm",children:"Siguiente"})]}),i.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[i.jsx("div",{children:i.jsxs("p",{className:"text-sm text-gray-700",children:["Mostrando ",i.jsx("span",{className:"font-medium",children:v+1})," a ",i.jsx("span",{className:"font-medium",children:Math.min(y,s.length)})," de"," ",i.jsx("span",{className:"font-medium",children:s.length})," resultados"]})}),i.jsx("div",{children:i.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[i.jsxs("button",{onClick:()=>w(p-1),disabled:1===p,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 "+(1===p?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-500 hover:bg-gray-50"),children:[i.jsx("span",{className:"sr-only",children:"Anterior"}),i.jsx("span",{className:"text-lg",children:"«"})]}),Array.from({length:N}).map(((e,a)=>{const s=a+1;return 1===s||s===N||s>=p-1&&s<=p+1?i.jsx("button",{onClick:()=>w(s),className:"relative inline-flex items-center px-4 py-2 border "+(p===s?"z-10 bg-blue-50 border-blue-500 text-blue-600 font-medium":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:s},s):2===s&&p>3||s===N-1&&p<N-2?i.jsx("span",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700",children:"..."},s):null})),i.jsxs("button",{onClick:()=>w(p+1),disabled:p===N,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 "+(p===N?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-500 hover:bg-gray-50"),children:[i.jsx("span",{className:"sr-only",children:"Siguiente"}),i.jsx("span",{className:"text-lg",children:"»"})]})]})})]})]})]}):i.jsxs("div",{className:"text-center py-8 bg-gray-50 rounded-lg border border-gray-200",children:[i.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),i.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No hay datos"}),i.jsx("p",{className:"mt-1 text-sm text-gray-500",children:g})]})]})},v=({searchTerm:a="",onSearchChange:s,searchPlaceholder:t="Buscar...",filters:n=[],filterValues:r={},onFilterChange:o,onClearFilters:l,onAddNew:d,canAdd:c=!1,addButtonText:m="Añadir Nuevo"})=>{const[x,g]=e.useState(!1),h=Object.values(r).some((e=>""!==e&&null!=e));return i.jsxs("div",{className:"mb-6 space-y-4",children:[i.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[i.jsxs("div",{className:"relative flex-grow max-w-md",children:[i.jsx("input",{type:"text",placeholder:t,className:"form-input w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50",value:a,onChange:e=>s(e.target.value)}),i.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:i.jsx("span",{className:"text-gray-400",children:"🔍"})}),a&&i.jsx("button",{className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",onClick:()=>s(""),title:"Limpiar búsqueda",children:i.jsx("span",{children:"×"})})]}),i.jsxs("div",{className:"flex space-x-2 justify-end",children:[n.length>0&&i.jsxs(u,{variant:x||h?"primary":"outline",onClick:()=>g(!x),className:"flex items-center",children:[i.jsx("span",{className:"mr-2",children:"📊"}),h?`Filtros (${Object.values(r).filter((e=>""!==e&&null!=e)).length})`:"Filtros"]}),c&&i.jsxs(u,{variant:"primary",onClick:d,className:"flex items-center shadow-sm hover:shadow-md transition-shadow",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z",clipRule:"evenodd"})}),i.jsx("span",{className:"font-medium",children:m})]})]})]}),x&&n.length>0&&i.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg border border-gray-200 animate-fadeIn",children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Filtros"}),h&&i.jsx(u,{variant:"secondary",size:"sm",onClick:l,className:"text-sm",children:"Limpiar todos"})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:n.map((e=>i.jsxs("div",{className:"space-y-1",children:[i.jsx("label",{htmlFor:`filter-${e.id}`,className:"block text-sm font-medium text-gray-700",children:e.label}),"select"===e.type?i.jsxs("div",{className:"relative",children:[i.jsxs("select",{id:`filter-${e.id}`,name:e.id,value:r[e.id]||"",onChange:a=>o(e.id,a.target.value),className:"form-select w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",children:[i.jsx("option",{value:"",children:e.placeholder||"Todos"}),e.options.map((e=>i.jsx("option",{value:e.value,children:e.label},e.value)))]}),r[e.id]&&i.jsx("button",{type:"button",onClick:()=>o(e.id,""),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",title:"Limpiar filtro",children:i.jsx("span",{children:"×"})})]}):"range"===e.type?i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("input",{type:"number",id:`filter-${e.id}-min`,name:`${e.id}_min`,placeholder:e.minPlaceholder||"Mín",value:r[`${e.id}_min`]||"",onChange:a=>o(`${e.id}_min`,a.target.value),className:"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",min:e.min,max:e.max}),i.jsx("span",{className:"text-gray-500",children:"-"}),i.jsx("input",{type:"number",id:`filter-${e.id}-max`,name:`${e.id}_max`,placeholder:e.maxPlaceholder||"Máx",value:r[`${e.id}_max`]||"",onChange:a=>o(`${e.id}_max`,a.target.value),className:"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50",min:e.min,max:e.max})]}):"date"===e.type?i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("input",{type:"date",id:`filter-${e.id}-from`,name:`${e.id}_from`,value:r[`${e.id}_from`]||"",onChange:a=>o(`${e.id}_from`,a.target.value),className:"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"}),i.jsx("span",{className:"text-gray-500",children:"a"}),i.jsx("input",{type:"date",id:`filter-${e.id}-to`,name:`${e.id}_to`,value:r[`${e.id}_to`]||"",onChange:a=>o(`${e.id}_to`,a.target.value),className:"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"})]}):i.jsxs("div",{className:"relative",children:[i.jsx("input",{type:e.type||"text",id:`filter-${e.id}`,name:e.id,placeholder:e.placeholder||"",value:r[e.id]||"",onChange:a=>o(e.id,a.target.value),className:"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"}),r[e.id]&&i.jsx("button",{type:"button",onClick:()=>o(e.id,""),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",title:"Limpiar filtro",children:i.jsx("span",{children:"×"})})]})]},e.id)))}),h&&i.jsxs("div",{className:"mt-4 text-sm text-gray-700",children:[i.jsx("strong",{children:"Filtros activos:"})," ",Object.entries(r).filter((([e,a])=>""!==a&&null!=a)).map((([e,a])=>{const s=n.find((a=>a.id===e||e.startsWith(`${a.id}_`)));if(!s)return null;let t=s.label;e.endsWith("_min")||e.endsWith("_from")?t=`${s.label} desde`:(e.endsWith("_max")||e.endsWith("_to"))&&(t=`${s.label} hasta`);let r=a;if("select"===s.type&&s.options){const e=s.options.find((e=>e.value===a));e&&(r=e.label)}return i.jsxs("span",{className:"inline-flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-2 mb-1",children:[t,": ",r,i.jsx("button",{type:"button",onClick:()=>o(e,""),className:"ml-1 text-blue-600 hover:text-blue-800",title:"Quitar filtro",children:i.jsx("span",{children:"×"})})]},e)}))]})]})]})},j=({isOpen:a,onClose:s,title:t,fields:n=[],initialValues:r={},onSubmit:o,loading:l=!1,submitText:d="Guardar",cancelText:c="Cancelar",loadingText:m="Guardando...",size:g="md",isEdit:h=!1})=>{const[p,b]=e.useState({}),[f,y]=e.useState({}),[v,j]=e.useState({});e.useEffect((()=>{a&&(b({...r}),y({}),j({}))}),[a,r]);const N=(e,a)=>{b((i=>({...i,[e]:a}))),j((a=>({...a,[e]:!0}))),w(e,a)},w=(e,a)=>{const i=n.find((a=>a.id===e));if(!i||!i.validation)return;let s=null;if(i.validation.required&&(!a||"string"==typeof a&&""===a.trim())&&(s=i.validation.requiredMessage||"Este campo es obligatorio"),a&&i.validation.pattern&&!i.validation.pattern.test(a)&&(s=i.validation.patternMessage||"El formato no es válido"),a&&i.validation.minLength&&a.length<i.validation.minLength&&(s=i.validation.minLengthMessage||`Debe tener al menos ${i.validation.minLength} caracteres`),a&&i.validation.maxLength&&a.length>i.validation.maxLength&&(s=i.validation.maxLengthMessage||`Debe tener máximo ${i.validation.maxLength} caracteres`),a&&void 0!==i.validation.min&&parseFloat(a)<i.validation.min&&(s=i.validation.minMessage||`El valor mínimo es ${i.validation.min}`),a&&void 0!==i.validation.max&&parseFloat(a)>i.validation.max&&(s=i.validation.maxMessage||`El valor máximo es ${i.validation.max}`),i.validation.custom&&"function"==typeof i.validation.custom){const e=i.validation.custom(a,p);e&&(s=e)}return y((a=>({...a,[e]:s}))),s};return i.jsx(x,{isOpen:a,onClose:s,title:t,size:g,children:i.jsxs("form",{onSubmit:e=>{e.preventDefault();const a={};n.forEach((e=>{"section"!==e.type&&"divider"!==e.type&&(a[e.id]=!0)})),j(a),(()=>{const e={};let a=!0;return n.forEach((i=>{if("section"===i.type||"divider"===i.type)return;const s=p[i.id],t=w(i.id,s);t&&(e[i.id]=t,a=!1)})),y(e),a})()&&o(p)},children:[i.jsx("div",{className:"space-y-1",children:n.map((e=>{var a;const{id:s,label:t,type:n,placeholder:r,options:o,disabled:d,info:c,className:m=""}=e,u=void 0!==p[s]?p[s]:"",x=f[s],g=v[s],h=x&&g;return"section"===n?i.jsxs("div",{className:`mb-4 ${m}`,children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900",children:t}),c&&i.jsx("p",{className:"mt-1 text-sm text-gray-500",children:c})]},s):"divider"===n?i.jsx("div",{className:`my-6 border-t border-gray-200 ${m}`},s):i.jsxs("div",{className:`mb-4 ${m}`,children:[t&&i.jsxs("label",{htmlFor:s,className:"block text-sm font-medium text-gray-700 mb-1",children:[t," ",(null==(a=e.validation)?void 0:a.required)&&i.jsx("span",{className:"text-red-500",children:"*"})]}),"text"===n||"email"===n||"number"===n||"tel"===n||"password"===n||"date"===n||"time"===n?i.jsxs("div",{className:"relative",children:[i.jsx("input",{type:n,id:s,name:s,value:u,onChange:e=>N(s,e.target.value),placeholder:r,disabled:d||l,className:`w-full rounded-md shadow-sm ${h?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-blue-500 focus:ring-blue-500"} focus:ring focus:ring-opacity-50 ${d?"bg-gray-100 cursor-not-allowed":""}`,min:e.min,max:e.max,step:e.step,maxLength:e.maxLength}),h&&i.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:i.jsx("span",{className:"text-red-500",children:"⚠️"})})]}):"textarea"===n?i.jsx("textarea",{id:s,name:s,value:u,onChange:e=>N(s,e.target.value),placeholder:r,disabled:d||l,rows:e.rows||3,className:`w-full rounded-md shadow-sm ${h?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-blue-500 focus:ring-blue-500"} focus:ring focus:ring-opacity-50 ${d?"bg-gray-100 cursor-not-allowed":""}`,maxLength:e.maxLength}):"select"===n?i.jsxs("select",{id:s,name:s,value:u,onChange:e=>N(s,e.target.value),disabled:d||l,className:`w-full rounded-md shadow-sm ${h?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-blue-500 focus:ring-blue-500"} focus:ring focus:ring-opacity-50 ${d?"bg-gray-100 cursor-not-allowed":""}`,children:[r&&i.jsx("option",{value:"",children:r}),o&&o.map((e=>i.jsx("option",{value:e.value,children:e.label},e.value)))]}):"radio"===n?i.jsx("div",{className:"mt-2 space-y-2",children:o&&o.map((e=>i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{id:`${s}-${e.value}`,name:s,type:"radio",value:e.value,checked:u===e.value,onChange:()=>N(s,e.value),disabled:d||l,className:`h-4 w-4 ${h?"border-red-300 text-red-600 focus:ring-red-500":"border-gray-300 text-blue-600 focus:ring-blue-500"} ${d?"bg-gray-100 cursor-not-allowed":""}`}),i.jsx("label",{htmlFor:`${s}-${e.value}`,className:"ml-3 block text-sm font-medium text-gray-700 "+(d?"text-gray-500":""),children:e.label})]},e.value)))}):"checkbox"===n?i.jsxs("div",{className:"flex items-center h-5 mt-1",children:[i.jsx("input",{id:s,name:s,type:"checkbox",checked:!!u,onChange:e=>N(s,e.target.checked),disabled:d||l,className:`h-4 w-4 rounded ${h?"border-red-300 text-red-600 focus:ring-red-500":"border-gray-300 text-blue-600 focus:ring-blue-500"} focus:ring focus:ring-opacity-50 ${d?"bg-gray-100 cursor-not-allowed":""}`}),e.checkboxLabel&&i.jsx("label",{htmlFor:s,className:"ml-3 block text-sm text-gray-700 "+(d?"text-gray-500":""),children:e.checkboxLabel})]}):"checkboxGroup"===n?i.jsx("div",{className:"mt-2 space-y-2",children:o&&o.map((e=>i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{id:`${s}-${e.value}`,name:`${s}-${e.value}`,type:"checkbox",checked:Array.isArray(u)&&u.includes(e.value),onChange:a=>{const i=a.target.checked,t=Array.isArray(u)?[...u]:[],n=i?[...t,e.value]:t.filter((a=>a!==e.value));N(s,n)},disabled:d||l,className:`h-4 w-4 rounded ${h?"border-red-300 text-red-600 focus:ring-red-500":"border-gray-300 text-blue-600 focus:ring-blue-500"} focus:ring focus:ring-opacity-50 ${d?"bg-gray-100 cursor-not-allowed":""}`}),i.jsx("label",{htmlFor:`${s}-${e.value}`,className:"ml-3 block text-sm font-medium text-gray-700 "+(d?"text-gray-500":""),children:e.label})]},e.value)))}):i.jsxs("p",{className:"text-sm text-red-500",children:["Tipo de campo no soportado: ",n]}),h&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:x}),c&&!h&&i.jsx("p",{className:"mt-1 text-sm text-gray-500",children:c})]},s)}))}),i.jsxs("div",{className:"flex justify-end space-x-4 mt-8 border-t border-gray-100 pt-6",children:[i.jsx(u,{type:"button",variant:"outline",onClick:s,disabled:l,className:"px-6",children:c}),i.jsx(u,{type:"submit",variant:"primary",disabled:l,className:"min-w-[120px] px-6",children:l?i.jsxs("span",{className:"flex items-center justify-center",children:[i.jsx("span",{className:"inline-block animate-spin mr-2",children:"↻"}),m]}):h?"Actualizar":d})]})]})})},N=({isAdmin:a})=>{const[o,l]=e.useState([]),[d,c]=e.useState(!1),[m,u]=e.useState(!1),[x,g]=e.useState(null),[h,p]=e.useState({nombre:"",direccion:"",telefono:""}),[b,f]=e.useState(""),[N,w]=e.useState("nombre"),[C,E]=e.useState("asc"),[_,L]=e.useState({}),S=e.useCallback((async()=>{c(!0);try{const{data:e,error:a,isOffline:i}=await s.getInstitutions(N,C);if(a)return console.error("Error al cargar instituciones:",a),n(a,"cargar","instituciones"),void l([]);l(e||[]),i&&r.info("Mostrando datos almacenados localmente. Algunas funciones pueden estar limitadas.")}catch(e){console.error("Error inesperado en fetchInstitutions:",e),n(e,"cargar","instituciones"),l([])}finally{c(!1)}}),[N,C]);e.useEffect((()=>{S()}),[S]);const $=(e=null)=>{g(e),p(e?{nombre:e.nombre,direccion:e.direccion||"",telefono:e.telefono||""}:{nombre:"",direccion:"",telefono:""}),u(!0)},z=()=>{u(!1)},M=o.filter((e=>e.nombre.toLowerCase().includes(b.toLowerCase())||e.direccion&&e.direccion.toLowerCase().includes(b.toLowerCase())||e.telefono&&e.telefono.toLowerCase().includes(b.toLowerCase())));return i.jsxs("div",{className:"space-y-6",children:[i.jsx(v,{searchTerm:b,onSearchChange:f,searchPlaceholder:"Buscar instituciones...",filters:[],filterValues:_,onFilterChange:(e,a)=>{L((i=>({...i,[e]:a})))},onClearFilters:()=>{L({})},onAddNew:()=>$(),canAdd:a,addButtonText:"Nueva Institución"}),i.jsx(y,{columns:[{field:"nombre",header:"Nombre",sortable:!0,highlight:!0},{field:"direccion",header:"Dirección",sortable:!0,emptyValue:"-"},{field:"telefono",header:"Teléfono",sortable:!0,emptyValue:"-"}],data:M,sortField:N,sortDirection:C,onSort:e=>{N===e?E("asc"===C?"desc":"asc"):(w(e),E("asc"))},loading:d,enableActions:a,onEdit:$,onDelete:async(e,a)=>{if(window.confirm(`¿Está seguro que desea eliminar la institución "${a.nombre}"?`)){c(!0);try{const{error:a,isOffline:i}=await s.deleteInstitution(e);if(a)return void n(a,"eliminar","institución");t(i?"Institución eliminada correctamente. Los cambios se sincronizarán cuando haya conexión.":"Institución eliminada correctamente."),S()}catch(i){console.error("Error al eliminar institución:",i),n(i,"eliminar","institución")}finally{c(!1)}}},isTemporaryFn:e=>"string"==typeof e&&e.startsWith("temp-"),emptyMessage:"No se encontraron instituciones registradas",actionLabels:{edit:"Editar institución",delete:"Eliminar institución"}}),i.jsx(j,{isOpen:m,onClose:z,title:x?"Editar Institución":"Nueva Institución",fields:[{id:"nombre",type:"text",label:"Nombre",placeholder:"Ej. Universidad Nacional",validation:{required:!0,requiredMessage:"El nombre es obligatorio",maxLength:100,maxLengthMessage:"El nombre debe tener máximo 100 caracteres"}},{id:"direccion",type:"text",label:"Dirección",placeholder:"Ej. Calle 45 #12-34",info:"Dirección física de la institución",validation:{maxLength:200,maxLengthMessage:"La dirección debe tener máximo 200 caracteres"}},{id:"telefono",type:"tel",label:"Teléfono",placeholder:"Ej. ************",validation:{pattern:/^[0-9\s+\-()]*$/,patternMessage:"Formato de teléfono inválido"}}],initialValues:h,onSubmit:async e=>{c(!0);try{const a={nombre:e.nombre,direccion:e.direccion,telefono:e.telefono};let i;if(x){if(i=await s.updateInstitution(x.id,a),i.error)return void n(i.error,"actualizar","institución");i.isOffline?t("Institución actualizada correctamente. Los cambios se sincronizarán cuando haya conexión."):t("Institución actualizada correctamente.")}else{if(i=await s.createInstitution(a),i.error)return void n(i.error,"crear","institución");i.isOffline?t("Institución creada correctamente. Los cambios se sincronizarán cuando haya conexión."):t("Institución creada correctamente.")}S(),z()}catch(a){console.error("Error al guardar institución:",a),n(a,x?"actualizar":"crear","institución")}finally{c(!1)}},loading:d,submitText:"Guardar",isEdit:!!x})]})},w=({isAdmin:a})=>{const[l,d]=e.useState([]),[c,m]=e.useState([]),[u,x]=e.useState(!1),[g,h]=e.useState(!1),[p,b]=e.useState(null),[f,N]=e.useState({nombre:"",apellidos:"",genero:"",email:"",documento_identidad:"",telefono:"",institucion_id:""}),[w,C]=e.useState(""),[E,_]=e.useState("nombre"),[L,S]=e.useState("asc"),[$,z]=e.useState({institucion_id:""}),M=[{field:"nombre",header:"Nombre",sortable:!0,highlight:!0},{field:"apellidos",header:"Apellidos",sortable:!0},{field:"genero",header:"Género",sortable:!0,emptyValue:"-"},{field:"email",header:"Email",sortable:!0},{field:"documento_identidad",header:"Documento",sortable:!0},{field:"telefono",header:"Teléfono",sortable:!0,emptyValue:"-"},{field:"institucion",header:"Institución",sortable:!1,render:(e,a)=>{var i,s;return(null==(i=a.instituciones)?void 0:i.nombre)||(null==(s=c.find((e=>e.id===a.institucion_id)))?void 0:s.nombre)||"-"}}],A=e.useCallback((async()=>{x(!0);try{const[e,a]=await Promise.all([s.getPsychologists(E,L),s.getInstitutions()]);e.error?(console.error("Error al cargar psicólogos:",e.error),n(e.error,"cargar","psicólogos"),d([])):(d(e.data||[]),e.isOffline&&r.info("Mostrando datos de psicólogos almacenados localmente. Algunas funciones pueden estar limitadas.")),a.error?(console.error("Error al cargar instituciones:",a.error),n(a.error,"cargar","instituciones"),m([])):(m(a.data||[]),a.isOffline&&!e.isOffline&&r.info("Mostrando datos de instituciones almacenados localmente."))}catch(e){console.error("Error inesperado al cargar datos:",e),n(e,"cargar","datos"),d([]),m([])}finally{x(!1)}}),[E,L]);e.useEffect((()=>{A()}),[A]);const k=(e=null)=>{b(e),N(e?{nombre:e.nombre,apellidos:e.apellidos,genero:e.genero||"",email:e.email,documento_identidad:e.documento_identidad,telefono:e.telefono||"",institucion_id:e.institucion_id}:{nombre:"",apellidos:"",genero:"",email:"",documento_identidad:"",telefono:"",institucion_id:c.length>0?c[0].id:""}),h(!0)},P=()=>{h(!1)},T=l.filter((e=>{const a=e.nombre.toLowerCase().includes(w.toLowerCase())||e.apellidos.toLowerCase().includes(w.toLowerCase())||e.genero&&e.genero.toLowerCase().includes(w.toLowerCase())||e.email.toLowerCase().includes(w.toLowerCase())||e.documento_identidad.toLowerCase().includes(w.toLowerCase())||e.telefono&&e.telefono.toLowerCase().includes(w.toLowerCase()),i=!$.institucion_id||e.institucion_id===$.institucion_id;return a&&i})),F=[{id:"institucion_id",type:"select",label:"Institución",placeholder:"Todas las instituciones",options:c.map((e=>({value:e.id,label:e.nombre})))}],I=[{id:"nombre",type:"text",label:"Nombre",placeholder:"Ej. Juan",validation:{required:!0,requiredMessage:"El nombre es obligatorio",maxLength:50,maxLengthMessage:"El nombre debe tener máximo 50 caracteres"}},{id:"apellidos",type:"text",label:"Apellidos",placeholder:"Ej. Pérez Gómez",validation:{required:!0,requiredMessage:"Los apellidos son obligatorios",maxLength:50,maxLengthMessage:"Los apellidos deben tener máximo 50 caracteres"}},{id:"genero",type:"select",label:"Género",placeholder:"Seleccionar género",options:[{value:"Masculino",label:"Masculino"},{value:"Femenino",label:"Femenino"},{value:"Otro",label:"Otro"},{value:"No especificado",label:"No especificado"}],validation:{required:!0,requiredMessage:"El género es obligatorio"}},{id:"email",type:"email",label:"Email",placeholder:"Ej. <EMAIL>",disabled:!!p,info:p?"El email no se puede modificar una vez creado el psicólogo.":"Se creará una cuenta con este email.",validation:{required:!0,requiredMessage:"El email es obligatorio",pattern:/\S+@\S+\.\S+/,patternMessage:"Ingrese un email válido"}},{id:"documento_identidad",type:"text",label:"Documento de Identidad",placeholder:"Ej. 1234567890",validation:{required:!0,requiredMessage:"El documento de identidad es obligatorio",maxLength:20,maxLengthMessage:"El documento debe tener máximo 20 caracteres"}},{id:"telefono",type:"tel",label:"Teléfono",placeholder:"Ej. ************",validation:{pattern:/^[0-9\s+\-()]*$/,patternMessage:"Formato de teléfono inválido"}},{id:"institucion_id",type:"select",label:"Institución",placeholder:"Seleccionar institución",options:c.map((e=>({value:e.id,label:e.nombre}))),validation:{required:!0,requiredMessage:"La institución es obligatoria"}}];return i.jsxs("div",{className:"space-y-6",children:[i.jsx(v,{searchTerm:w,onSearchChange:C,searchPlaceholder:"Buscar psicólogos...",filters:F,filterValues:$,onFilterChange:(e,a)=>{z((i=>({...i,[e]:a})))},onClearFilters:()=>{z({institucion_id:""})},onAddNew:()=>k(),canAdd:a,addButtonText:"Nuevo Psicólogo"}),i.jsx(y,{columns:M,data:T,sortField:E,sortDirection:L,onSort:e=>{E===e?S("asc"===L?"desc":"asc"):(_(e),S("asc"))},loading:u,enableActions:a,onEdit:k,onDelete:async(e,a)=>{if(window.confirm(`¿Está seguro que desea eliminar al psicólogo "${a.nombre} ${a.apellidos}"? Esta acción no se puede deshacer.`)){x(!0);try{const{error:a,isOffline:i}=await s.deletePsychologist(e);if(a)return void n(a,"eliminar","psicólogo");t(i?"Psicólogo eliminado correctamente. Los cambios se sincronizarán cuando haya conexión.":"Psicólogo eliminado correctamente."),A()}catch(i){console.error("Error al eliminar psicólogo:",i),n(i,"eliminar","psicólogo")}finally{x(!1)}}},isTemporaryFn:e=>"string"==typeof e&&e.startsWith("temp-"),emptyMessage:"No se encontraron psicólogos registrados",actionLabels:{edit:"Editar psicólogo",delete:"Eliminar psicólogo"}}),i.jsx(j,{isOpen:g,onClose:P,title:p?"Editar Psicólogo":"Nuevo Psicólogo",fields:I,initialValues:f,onSubmit:async e=>{x(!0);try{const i={nombre:e.nombre,apellidos:e.apellidos,genero:e.genero,email:e.email,documento_identidad:e.documento_identidad,telefono:e.telefono,institucion_id:e.institucion_id};if(p){const e={...i};delete e.email;const{error:a,isOffline:r}=await s.updatePsychologist(p.id,e);if(a)return void n(a,"actualizar","psicólogo");t(r?"Psicólogo actualizado correctamente. Los cambios se sincronizarán cuando haya conexión.":"Psicólogo actualizado correctamente.")}else try{console.log("Creando usuario en Auth para:",e.email);const{data:a,error:r}=await o.auth.signUp({email:e.email,password:"Temporal123!",options:{data:{rol:"psicologo",nombre_completo:`${e.nombre} ${e.apellidos}`}}});if(r)return console.error("Error al crear usuario en Auth:",r),void(r.message.includes("User already registered")?n({message:"Ya existe un usuario registrado con este email."},"crear","usuario"):n(r,"crear","usuario"));if(!a.user)throw new Error("No se pudo obtener el ID del usuario creado en Auth.");const l={...i,usuario_id:a.user.id},{error:d,isOffline:c}=await s.createPsychologist(l);if(d)return console.error("Error al crear registro de psicólogo:",d),void n(d,"guardar","psicólogo");t(c?"Psicólogo creado correctamente. Los datos se sincronizarán cuando haya conexión.":"Psicólogo creado correctamente.")}catch(a){return console.error("Error en el proceso de creación de usuario/psicólogo:",a),void n(a,"crear","psicólogo")}P(),A()}catch(i){console.error("Error final en handleSubmit:",i),n(i,p?"actualizar":"crear","psicólogo")}finally{x(!1)}},loading:u,submitText:"Guardar",isEdit:!!p,size:"lg"})]})},C=({isAdmin:a})=>{const[o,l]=e.useState([]),[d,c]=e.useState([]),[m,u]=e.useState([]),[x,g]=e.useState(!1),[h,p]=e.useState(!1),[b,f]=e.useState(null),[N,w]=e.useState({nombre:"",apellidos:"",fecha_nacimiento:"",genero:"",documento_identidad:"",email:"",telefono:"",institucion_id:"",psicologo_id:"",notas:""}),[C,E]=e.useState(""),[_,L]=e.useState("nombre"),[S,$]=e.useState("asc"),[z,M]=e.useState({institucion_id:"",genero:"",psicologo_id:"",edad_min:"",edad_max:""}),A=[{field:"nombre",header:"Nombre",sortable:!0,highlight:!0},{field:"apellidos",header:"Apellidos",sortable:!0},{field:"edad",header:"Edad",sortable:!0,emptyValue:"-",type:"numeric"},{field:"genero",header:"Género",sortable:!0},{field:"documento_identidad",header:"Documento",sortable:!0,emptyValue:"-"},{field:"email",header:"Email",sortable:!0,emptyValue:"-"},{field:"telefono",header:"Teléfono",sortable:!0,emptyValue:"-"},{field:"institucion",header:"Institución",sortable:!1,render:(e,a)=>{var i,s;return(null==(i=a.instituciones)?void 0:i.nombre)||(null==(s=d.find((e=>e.id===a.institucion_id)))?void 0:s.nombre)||"-"}},{field:"psicologo",header:"Psicólogo",sortable:!1,render:(e,a)=>{if(!a.psicologo_id)return"-";const i=m.find((e=>e.id===a.psicologo_id));return i?`${i.nombre} ${i.apellidos}`:"-"}}],k=e.useCallback((async()=>{g(!0);try{const[e,a,i]=await Promise.all([s.getPatients(_,S),s.getInstitutions(),s.getPsychologists()]);e.error?(console.error("Error al cargar pacientes:",e.error),n(e.error,"cargar","pacientes"),l([])):(l(e.data||[]),e.isOffline&&r.info("Mostrando datos de pacientes almacenados localmente. Algunas funciones pueden estar limitadas.")),a.error?(console.error("Error al cargar instituciones:",a.error),n(a.error,"cargar","instituciones"),c([])):c(a.data||[]),i.error?(console.error("Error al cargar psicólogos:",i.error),n(i.error,"cargar","psicólogos"),u([])):u(i.data||[])}catch(e){console.error("Error inesperado al cargar datos:",e),n(e,"cargar","datos"),l([]),c([]),u([])}finally{g(!1)}}),[_,S]);e.useEffect((()=>{k()}),[k]);const P=(e=null)=>{f(e),w(e?{nombre:e.nombre,apellidos:e.apellidos||"",fecha_nacimiento:e.fecha_nacimiento,genero:e.genero,documento_identidad:e.documento_identidad||"",email:e.email||"",telefono:e.telefono||"",institucion_id:e.institucion_id,psicologo_id:e.psicologo_id||"",notas:e.notas||""}:{nombre:"",apellidos:"",fecha_nacimiento:"",genero:"",documento_identidad:"",email:"",telefono:"",institucion_id:d.length>0?d[0].id:"",psicologo_id:"",notas:""}),p(!0)},T=()=>{p(!1)},F=o.filter((e=>{const a=e.nombre.toLowerCase().includes(C.toLowerCase())||e.apellidos&&e.apellidos.toLowerCase().includes(C.toLowerCase())||e.documento_identidad&&e.documento_identidad.toLowerCase().includes(C.toLowerCase())||e.email&&e.email.toLowerCase().includes(C.toLowerCase())||e.telefono&&e.telefono.toLowerCase().includes(C.toLowerCase())||e.notas&&e.notas.toLowerCase().includes(C.toLowerCase()),i=!z.institucion_id||e.institucion_id===z.institucion_id,s=!z.genero||e.genero===z.genero,t=!z.psicologo_id||("null"===z.psicologo_id?!e.psicologo_id:e.psicologo_id===z.psicologo_id),n=!z.edad_min||e.edad&&e.edad>=parseInt(z.edad_min),r=!z.edad_max||e.edad&&e.edad<=parseInt(z.edad_max);return a&&i&&s&&t&&n&&r})),I=[{id:"institucion_id",type:"select",label:"Institución",placeholder:"Todas las instituciones",options:d.map((e=>({value:e.id,label:e.nombre})))},{id:"genero",type:"select",label:"Género",placeholder:"Todos los géneros",options:[{value:"Masculino",label:"Masculino"},{value:"Femenino",label:"Femenino"}]},{id:"psicologo_id",type:"select",label:"Psicólogo",placeholder:"Todos los psicólogos",options:[{value:"null",label:"Sin psicólogo asignado"},...m.map((e=>({value:e.id,label:`${e.nombre} ${e.apellidos}`})))]},{id:"edad",type:"range",label:"Edad",minPlaceholder:"Mínima",maxPlaceholder:"Máxima",min:0,max:120}],q=[{id:"nombre",type:"text",label:"Nombre",placeholder:"Ej. Juan",validation:{required:!0,requiredMessage:"El nombre es obligatorio",maxLength:50,maxLengthMessage:"El nombre debe tener máximo 50 caracteres"}},{id:"apellidos",type:"text",label:"Apellidos",placeholder:"Ej. Pérez Gómez",validation:{required:!0,requiredMessage:"Los apellidos son obligatorios",maxLength:50,maxLengthMessage:"Los apellidos deben tener máximo 50 caracteres"}},{id:"fecha_nacimiento",type:"date",label:"Fecha de Nacimiento",validation:{required:!0,requiredMessage:"La fecha de nacimiento es obligatoria"}},{id:"genero",type:"select",label:"Género",placeholder:"Seleccionar género",options:[{value:"Masculino",label:"Masculino"},{value:"Femenino",label:"Femenino"},{value:"Otro",label:"Otro"},{value:"No especificado",label:"No especificado"}],validation:{required:!0,requiredMessage:"El género es obligatorio"}},{id:"documento_identidad",type:"text",label:"Documento de Identidad",placeholder:"Ej. 1234567890",validation:{required:!0,requiredMessage:"El documento de identidad es obligatorio",maxLength:20,maxLengthMessage:"El documento debe tener máximo 20 caracteres"}},{id:"email",type:"email",label:"Email",placeholder:"Ej. <EMAIL>",validation:{pattern:/\S+@\S+\.\S+/,patternMessage:"Ingrese un email válido"}},{id:"telefono",type:"tel",label:"Teléfono",placeholder:"Ej. ************",validation:{pattern:/^[0-9\s+\-()]*$/,patternMessage:"Formato de teléfono inválido"}},{id:"institucion_id",type:"select",label:"Institución",placeholder:"Seleccionar institución",options:d.map((e=>({value:e.id,label:e.nombre}))),validation:{required:!0,requiredMessage:"La institución es obligatoria"}},{id:"psicologo_id",type:"select",label:"Psicólogo Asignado",placeholder:"Sin psicólogo asignado",options:m.map((e=>({value:e.id,label:`${e.nombre} ${e.apellidos}`})))},{id:"notas",type:"textarea",label:"Notas",placeholder:"Información adicional sobre el paciente",rows:3}];return i.jsxs("div",{className:"space-y-6",children:[i.jsx(v,{searchTerm:C,onSearchChange:E,searchPlaceholder:"Buscar pacientes...",filters:I,filterValues:z,onFilterChange:(e,a)=>{M((i=>({...i,[e]:a})))},onClearFilters:()=>{M({institucion_id:"",genero:"",psicologo_id:"",edad_min:"",edad_max:""})},onAddNew:()=>P(),canAdd:a,addButtonText:"Nuevo Paciente"}),i.jsx(y,{columns:A,data:F,sortField:_,sortDirection:S,onSort:e=>{_===e?$("asc"===S?"desc":"asc"):(L(e),$("asc"))},loading:x,enableActions:a,onEdit:P,onDelete:async(e,a)=>{if(window.confirm(`¿Está seguro que desea eliminar al paciente "${a.nombre}"? Esta acción no se puede deshacer.`)){g(!0);try{const{error:i,isOffline:r}=await s.deletePatient(e);if(i)return void n(i,"eliminar","paciente");t(r?`Paciente "${a.nombre}" eliminado correctamente. Los cambios se sincronizarán cuando haya conexión.`:`Paciente "${a.nombre}" eliminado correctamente.`),k()}catch(i){console.error("Error al eliminar paciente:",i),n(i,"eliminar","paciente")}finally{g(!1)}}},isTemporaryFn:e=>"string"==typeof e&&e.startsWith("temp-"),emptyMessage:"No se encontraron pacientes registrados",actionLabels:{edit:"Editar paciente",delete:"Eliminar paciente"}}),i.jsx(j,{isOpen:h,onClose:T,title:b?"Editar Paciente":"Nuevo Paciente",fields:q,initialValues:N,onSubmit:async e=>{g(!0);try{const a=(e=>{if(!e)return null;const a=new Date,i=new Date(e);let s=a.getFullYear()-i.getFullYear();const t=a.getMonth()-i.getMonth();return(t<0||0===t&&a.getDate()<i.getDate())&&s--,s})(e.fecha_nacimiento),i={nombre:e.nombre,apellidos:e.apellidos,fecha_nacimiento:e.fecha_nacimiento,genero:e.genero,documento_identidad:e.documento_identidad,email:e.email,telefono:e.telefono,institucion_id:e.institucion_id,psicologo_id:e.psicologo_id||null,notas:e.notas,edad:a};if(b){const{error:a,isOffline:r}=await s.updatePatient(b.id,i);if(a)return void n(a,"actualizar","paciente");t(r?`Paciente "${e.nombre}" actualizado correctamente. Los cambios se sincronizarán cuando haya conexión.`:`Paciente "${e.nombre}" actualizado correctamente.`)}else{const{error:a,isOffline:r}=await s.createPatient(i);if(a)return void n(a,"crear","paciente");t(r?`Paciente "${e.nombre}" creado correctamente. Los datos se sincronizarán cuando haya conexión.`:`Paciente "${e.nombre}" creado correctamente.`)}T(),k()}catch(a){console.error("Error al guardar paciente:",a),n(a,b?"actualizar":"crear","paciente")}finally{g(!1)}},loading:x,submitText:"Guardar",isEdit:!!b,size:"lg"})]})},E=()=>{const[a,s]=e.useState(0),[t,n]=e.useState(!1),r=l((e=>e.auth.user)),o="admin"===(null==r?void 0:r.role)||"administrador"===(null==r?void 0:r.role)||!0;return i.jsxs("div",{className:"container mx-auto py-6 px-4 sm:px-6 lg:px-8",children:[i.jsxs("header",{className:"mb-6",children:[i.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4",children:[i.jsxs("div",{children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-1",children:"Panel de Administración"}),i.jsx("p",{className:"text-gray-600",children:"Gestión centralizada de recursos de la plataforma"})]}),i.jsxs("div",{className:"flex items-center mt-4 sm:mt-0",children:[i.jsx("button",{onClick:()=>n(!t),className:"mr-3 text-gray-500 hover:text-blue-600 transition-colors focus:outline-none",title:"Mostrar información",children:i.jsx("span",{style:{fontSize:"1.25rem"},children:"ℹ️"})}),i.jsx(f,{})]})]}),t&&i.jsx(d,{className:"mb-6 border-blue-100 bg-blue-50",children:i.jsx(c,{className:"py-3",children:i.jsxs("div",{className:"text-sm text-gray-600",children:[i.jsx("h3",{className:"font-medium text-base mb-2",children:"Sobre el Panel de Administración"}),i.jsx("p",{className:"mb-2",children:"Este panel le permite gestionar todos los aspectos de la plataforma:"}),i.jsxs("ul",{className:"list-disc pl-5 space-y-1",children:[i.jsxs("li",{children:[i.jsx("strong",{children:"Instituciones:"})," Añada, edite o elimine las instituciones asociadas."]}),i.jsxs("li",{children:[i.jsx("strong",{children:"Psicólogos:"})," Gestione los profesionales que pueden administrar pruebas."]}),i.jsxs("li",{children:[i.jsx("strong",{children:"Pacientes:"})," Administre los registros de pacientes y sus asignaciones."]})]}),i.jsxs("p",{className:"mt-2 text-blue-600",children:[i.jsx("span",{className:"font-medium",children:"Nota:"})," Los cambios realizados se sincronizarán automáticamente cuando haya conexión a internet."]})]})})})]}),i.jsxs(d,{className:"shadow-md border-gray-200",children:[i.jsx(m,{className:"bg-white px-6 py-4 border-b border-gray-200",children:i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsxs("h2",{className:"text-lg font-medium text-gray-900 flex items-center",children:[i.jsx("span",{className:"mr-2 text-gray-600",children:"⚙️"}),"Administración de Recursos"]}),i.jsx("div",{className:"flex items-center space-x-2",children:o?i.jsx("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium",children:"Acceso Completo"}):i.jsx("span",{className:"bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium",children:"Acceso Limitado"})})]})}),i.jsx(c,{className:"p-0",children:i.jsxs(g,{activeTab:a,onChange:s,children:[i.jsxs(h,{className:"px-6 pt-2 bg-gray-50",children:[i.jsx(p,{children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("span",{className:"mr-2",children:"🏢"}),"Instituciones"]})}),i.jsx(p,{children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("span",{className:"mr-2",children:"👨‍⚕️"}),"Psicólogos"]})}),i.jsx(p,{children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("span",{className:"mr-2",children:"👥"}),"Pacientes"]})})]}),i.jsxs("div",{className:"p-6",children:[i.jsx(b,{children:i.jsx(N,{isAdmin:o})}),i.jsx(b,{children:i.jsx(w,{isAdmin:o})}),i.jsx(b,{children:i.jsx(C,{isAdmin:o})})]})]})})]})]})};export{E as default};
//# sourceMappingURL=Administration-cbfbdb6e.js.map
