{"version": 3, "file": "Administration-cbfbdb6e.js", "sources": ["../../src/components/ui/Tabs.jsx", "../../src/components/admin/EnhancedSyncStatus.jsx", "../../src/components/ui/DataTable.jsx", "../../src/components/ui/SearchFilter.jsx", "../../src/components/ui/FormModal.jsx", "../../src/pages/admin/administration/InstitutionsTab.jsx", "../../src/pages/admin/administration/PsychologistsTab.jsx", "../../src/pages/admin/administration/PatientsTab.jsx", "../../src/pages/admin/Administration.jsx"], "sourcesContent": ["import React, { useState, useEffect, Children } from 'react';\n\nexport const Tabs = ({ children, activeTab = 0, onChange }) => {\n  const [currentTab, setCurrentTab] = useState(activeTab);\n\n  useEffect(() => {\n    setCurrentTab(activeTab);\n  }, [activeTab]);\n\n  const handleTabChange = (index) => {\n    setCurrentTab(index);\n    if (onChange) {\n      onChange(index);\n    }\n  };\n\n  // Extraer TabList y TabPanels de los children\n  let tabList = null;\n  let tabPanels = [];\n\n  Children.forEach(children, (child) => {\n    if (child.type === TabList) {\n      tabList = React.cloneElement(child, {\n        activeTab: currentTab,\n        onTabChange: handleTabChange\n      });\n    } else if (child.type === TabPanel) {\n      tabPanels.push(child);\n    }\n  });\n\n  return (\n    <div className=\"tabs\">\n      {tabList}\n      <div className=\"tab-content mt-4\">\n        {tabPanels.length > 0 && tabPanels[currentTab]}\n      </div>\n    </div>\n  );\n};\n\nexport const TabList = ({ children, activeTab, onTabChange }) => {\n  return (\n    <div className=\"border-b border-gray-200\">\n      <nav className=\"-mb-px flex space-x-8\">\n        {Children.map(children, (child, index) => {\n          return React.cloneElement(child, {\n            isActive: index === activeTab,\n            onClick: () => onTabChange(index),\n            index\n          });\n        })}\n      </nav>\n    </div>\n  );\n};\n\nexport const Tab = ({ children, isActive, onClick, index }) => {\n  return (\n    <button\n      className={`py-4 px-1 border-b-2 font-medium text-sm ${\n        isActive\n          ? 'border-blue-500 text-blue-600'\n          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n      }`}\n      onClick={onClick}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport const TabPanel = ({ children }) => {\n  return <div>{children}</div>;\n};\n\nexport default { Tabs, TabList, Tab, TabPanel };\n", "import React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport enhancedSupabaseService from '../../services/enhancedSupabaseService';\nimport { showErrorToast, showSuccessToast } from '../../utils/errorHandler';\n\n/**\n * Componente que muestra el estado de sincronización y permite sincronizar manualmente\n */\nconst EnhancedSyncStatus = () => {\n  const [syncStatus, setSyncStatus] = useState({\n    pendingCount: 0,\n    lastSyncAttempt: null,\n    operations: []\n  });\n  const [isSyncing, setIsSyncing] = useState(false);\n  const [showDetails, setShowDetails] = useState(false);\n\n  // Obtener estado de sincronización al montar el componente\n  useEffect(() => {\n    updateSyncStatus();\n\n    // Actualizar estado cada 30 segundos\n    const interval = setInterval(updateSyncStatus, 30000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Actualizar estado de sincronización\n  const updateSyncStatus = () => {\n    const status = enhancedSupabaseService.getSyncStatus();\n    setSyncStatus(status);\n  };\n\n  // Sincronizar manualmente\n  const handleSync = async () => {\n    if (isSyncing) return;\n\n    setIsSyncing(true);\n\n    try {\n      const result = await enhancedSupabaseService.syncPendingOperations();\n\n      if (result.success) {\n        showSuccessToast(`Sincronización completada. ${result.syncedCount} operaciones sincronizadas.`);\n      } else {\n        showErrorToast({\n          message: `Sincronización parcial. ${result.syncedCount} operaciones sincronizadas, ${result.errors.length} errores.`\n        });\n      }\n\n      updateSyncStatus();\n    } catch (error) {\n      showErrorToast(error, 'sincronizar', 'datos');\n    } finally {\n      setIsSyncing(false);\n    }\n  };\n\n  // Formatear fecha\n  const formatDate = (timestamp) => {\n    if (!timestamp) return 'Nunca';\n\n    const date = new Date(timestamp);\n    return date.toLocaleString('es-ES', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Obtener texto de operación\n  const getOperationText = (operation) => {\n    const entityMap = {\n      instituciones: 'institución',\n      psicologos: 'psicólogo',\n      pacientes: 'paciente'\n    };\n\n    const typeMap = {\n      CREATE: 'Crear',\n      UPDATE: 'Actualizar',\n      DELETE: 'Eliminar'\n    };\n\n    const entity = entityMap[operation.entity] || operation.entity;\n    const type = typeMap[operation.type] || operation.type;\n\n    return `${type} ${entity} (ID: ${operation.id})`;\n  };\n\n  return (\n    <div className=\"relative\">\n      <div className=\"flex items-center\">\n        {/* Botón de estado de sincronización */}\n        <button\n          onClick={() => syncStatus.pendingCount > 0 ? handleSync() : setShowDetails(!showDetails)}\n          className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${\n            isSyncing\n              ? 'bg-blue-100 text-blue-800'\n              : syncStatus.pendingCount > 0\n                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'\n                : 'bg-green-100 text-green-800 hover:bg-green-200'\n          }`}\n          disabled={isSyncing}\n          title={syncStatus.pendingCount > 0 ? 'Sincronizar cambios pendientes' : 'Estado de sincronización'}\n        >\n          {isSyncing ? (\n            <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n          ) : (\n            <span className={`inline-block w-3 h-3 rounded-full mr-2 ${\n              syncStatus.pendingCount > 0 ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'\n            }`}></span>\n          )}\n          {isSyncing\n            ? 'Sincronizando...'\n            : syncStatus.pendingCount > 0\n              ? `${syncStatus.pendingCount} pendiente${syncStatus.pendingCount !== 1 ? 's' : ''}`\n              : 'Sincronizado'\n          }\n        </button>\n\n        {/* Botón de información */}\n        <button\n          onClick={() => setShowDetails(!showDetails)}\n          className=\"ml-2 text-gray-500 hover:text-gray-700 focus:outline-none\"\n          title=\"Ver detalles de sincronización\"\n        >\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n            <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n          </svg>\n        </button>\n      </div>\n\n      {/* Panel de detalles */}\n      {showDetails && (\n        <div className=\"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-10 border border-gray-200\">\n          <div className=\"p-4\">\n            <div className=\"flex justify-between items-center mb-3\">\n              <h3 className=\"text-sm font-medium text-gray-900\">Estado de sincronización</h3>\n              <button\n                onClick={() => setShowDetails(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </button>\n            </div>\n\n            <div className=\"bg-gray-50 p-3 rounded-md mb-3\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">Estado:</span>\n                <span className={`text-sm font-medium px-2 py-1 rounded-full ${\n                  isSyncing\n                    ? 'bg-blue-100 text-blue-800'\n                    : syncStatus.pendingCount > 0\n                      ? 'bg-yellow-100 text-yellow-800'\n                      : 'bg-green-100 text-green-800'\n                }`}>\n                  {isSyncing\n                    ? 'Sincronizando...'\n                    : syncStatus.pendingCount > 0\n                      ? 'Cambios pendientes'\n                      : 'Sincronizado'\n                  }\n                </span>\n              </div>\n\n              <div className=\"text-sm text-gray-600\">\n                <div className=\"flex justify-between\">\n                  <span>Última sincronización:</span>\n                  <span className=\"font-medium\">{formatDate(syncStatus.lastSyncAttempt)}</span>\n                </div>\n                <div className=\"flex justify-between mt-1\">\n                  <span>Operaciones pendientes:</span>\n                  <span className={`font-medium ${syncStatus.pendingCount > 0 ? 'text-yellow-600' : 'text-green-600'}`}>\n                    {syncStatus.pendingCount}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {syncStatus.operations.length > 0 && (\n              <div className=\"mb-3\">\n                <h4 className=\"text-xs font-medium text-gray-700 mb-2 flex items-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1 text-yellow-500\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Operaciones pendientes:\n                </h4>\n                <div className=\"bg-gray-50 rounded-md border border-gray-200\">\n                  <ul className=\"text-xs text-gray-600 max-h-40 overflow-y-auto divide-y divide-gray-200\">\n                    {syncStatus.operations.map((op, index) => (\n                      <li key={index} className=\"p-2 hover:bg-gray-100\">\n                        <div className=\"flex items-center\">\n                          <span className={`inline-block w-2 h-2 rounded-full mr-2 ${\n                            op.type === 'CREATE' ? 'bg-green-500' :\n                            op.type === 'UPDATE' ? 'bg-blue-500' : 'bg-red-500'\n                          }`}></span>\n                          <span className=\"font-medium\">{getOperationText(op)}</span>\n                        </div>\n                        <div className=\"text-gray-400 text-2xs mt-1 ml-4\">\n                          {formatDate(op.timestamp)}\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex justify-between items-center\">\n              <div className=\"text-xs text-gray-500\">\n                {syncStatus.pendingCount > 0\n                  ? 'Los cambios pendientes se sincronizarán automáticamente cuando haya conexión.'\n                  : 'Todos los cambios están sincronizados.'}\n              </div>\n              <button\n                onClick={handleSync}\n                disabled={isSyncing || syncStatus.pendingCount === 0}\n                className={`px-3 py-1.5 text-xs rounded-md font-medium ${\n                  isSyncing\n                    ? 'bg-blue-100 text-blue-800 cursor-not-allowed'\n                    : syncStatus.pendingCount > 0\n                      ? 'bg-blue-500 text-white hover:bg-blue-600'\n                      : 'bg-gray-200 text-gray-500 cursor-not-allowed'\n                }`}\n              >\n                {isSyncing ? (\n                  <span className=\"flex items-center\">\n                    <svg className=\"animate-spin -ml-1 mr-1 h-3 w-3 text-current\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Sincronizando\n                  </span>\n                ) : 'Sincronizar ahora'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default EnhancedSyncStatus;\n", "import React, { useState, useEffect } from 'react';\nimport { Button } from './Button';\n// En lugar de usar react-icons, usaremos SVG directos\n// import { FaSort, FaSortUp, FaSortDown, FaChevronLeft, FaChevronRight } from 'react-icons/fa';\n\n/**\n * Componente de tabla de datos reutilizable con ordenamiento, paginación y acciones\n *\n * @param {Object} props\n * @param {Array} props.columns - Configuración de columnas\n * @param {Array} props.data - Datos a mostrar en la tabla\n * @param {string} props.sortField - Campo de ordenamiento actual\n * @param {string} props.sortDirection - Dirección de ordenamiento ('asc' o 'desc')\n * @param {Function} props.onSort - Función a ejecutar cuando se cambia el ordenamiento\n * @param {boolean} props.loading - Estado de carga\n * @param {boolean} props.enableActions - Habilitar columna de acciones\n * @param {Function} props.onEdit - Función para editar un registro\n * @param {Function} props.onDelete - Función para eliminar un registro\n * @param {boolean} props.isTemporaryFn - Función para determinar si un registro es temporal\n */\nconst DataTable = ({\n  columns = [],\n  data = [],\n  sortField = '',\n  sortDirection = 'asc',\n  onSort,\n  loading = false,\n  enableActions = true,\n  onEdit,\n  onDelete,\n  isTemporaryFn,\n  itemsPerPage = 10,\n  emptyMessage = \"No hay datos disponibles\",\n  actionLabels = { edit: \"Editar\", delete: \"Eliminar\" }\n}) => {\n  const [currentPage, setCurrentPage] = useState(1);\n\n  // Resetear página actual cuando cambian los datos\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [data.length]);\n\n  // Manejador de ordenamiento\n  const handleSort = (field) => {\n    if (onSort) {\n      onSort(field);\n    }\n  };\n\n  // Obtener renderizador de celda\n  const getCellRenderer = (column, item, index) => {\n    // Si la columna tiene un renderizador personalizado, utilizarlo\n    if (column.render) {\n      return column.render(item[column.field], item, index);\n    }\n\n    // Renderizado predeterminado según el tipo de dato\n    const value = item[column.field];\n\n    // Manejo de valores nulos o indefinidos\n    if (value === null || value === undefined) {\n      return column.emptyValue || '-';\n    }\n\n    return value;\n  };\n\n  // Paginación\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentItems = data.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(data.length / itemsPerPage);\n\n  const paginate = (pageNumber) => setCurrentPage(pageNumber);\n\n  return (\n    <div className=\"relative\">\n      {/* Estado de carga */}\n      {loading && (\n        <div className=\"absolute inset-0 bg-white bg-opacity-80 z-10 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mb-2\"></div>\n            <p className=\"text-gray-700\">Cargando datos...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Mensaje cuando no hay datos */}\n      {!loading && data.length === 0 ? (\n        <div className=\"text-center py-8 bg-gray-50 rounded-lg border border-gray-200\">\n          <svg\n            className=\"mx-auto h-12 w-12 text-gray-400\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            stroke=\"currentColor\"\n            aria-hidden=\"true\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth=\"2\"\n              d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            />\n          </svg>\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No hay datos</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">{emptyMessage}</p>\n        </div>\n      ) : (\n        <div className=\"overflow-x-auto shadow-sm rounded-lg border border-gray-200\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                {columns.map((column, index) => (\n                  <th\n                    key={index}\n                    scope=\"col\"\n                    className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${\n                      column.sortable !== false ? 'cursor-pointer hover:bg-gray-100' : ''\n                    } ${column.width ? column.width : ''}`}\n                    onClick={() => column.sortable !== false && handleSort(column.field)}\n                  >\n                    <div className=\"flex items-center\">\n                      <span>{column.header}</span>\n                      {column.sortable !== false && (\n                        <span className=\"ml-2\">\n                          {sortField === column.field ? (\n                            sortDirection === 'asc' ? (\n                              <span className=\"text-blue-500\">▲</span>\n                            ) : (\n                              <span className=\"text-blue-500\">▼</span>\n                            )\n                          ) : (\n                            <span className=\"text-gray-400\">⇅</span>\n                          )}\n                        </span>\n                      )}\n                    </div>\n                  </th>\n                ))}\n                {enableActions && (\n                  <th\n                    scope=\"col\"\n                    className=\"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32\"\n                  >\n                    Acciones\n                  </th>\n                )}\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {currentItems.map((item, rowIndex) => (\n                <tr\n                  key={item.id || rowIndex}\n                  className={`hover:bg-gray-50 ${\n                    isTemporaryFn && isTemporaryFn(item.id) ? 'bg-yellow-50 opacity-80 italic' : ''\n                  }`}\n                >\n                  {columns.map((column, colIndex) => (\n                    <td\n                      key={`${rowIndex}-${colIndex}`}\n                      className={`px-6 py-4 whitespace-nowrap text-sm ${\n                        column.type === 'numeric' ? 'text-right' : ''\n                      } ${column.highlight ? 'font-medium text-gray-900' : 'text-gray-500'}`}\n                    >\n                      {getCellRenderer(column, item, rowIndex)}\n                    </td>\n                  ))}\n                  {enableActions && (\n                    <td className=\"px-6 py-4 whitespace-nowrap text-center text-sm font-medium\">\n                      <div className=\"flex justify-center space-x-3\">\n                        {onEdit && (\n                          <button\n                            onClick={() => onEdit(item)}\n                            className=\"bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 transition-colors p-2 rounded-full\"\n                            title={actionLabels.edit}\n                          >\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                              <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\" />\n                            </svg>\n                          </button>\n                        )}\n                        {onDelete && (\n                          <button\n                            onClick={() => onDelete(item.id, item)}\n                            className=\"bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-800 transition-colors p-2 rounded-full\"\n                            title={actionLabels.delete}\n                          >\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                              <path fillRule=\"evenodd\" d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                            </svg>\n                          </button>\n                        )}\n                      </div>\n                    </td>\n                  )}\n                </tr>\n              ))}\n            </tbody>\n          </table>\n\n          {/* Paginación */}\n          {totalPages > 1 && (\n            <div className=\"px-6 py-3 flex items-center justify-between border-t border-gray-200 bg-white\">\n              <div className=\"flex-1 flex justify-between sm:hidden\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => paginate(currentPage - 1)}\n                  disabled={currentPage === 1}\n                  size=\"sm\"\n                >\n                  Anterior\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => paginate(currentPage + 1)}\n                  disabled={currentPage === totalPages}\n                  size=\"sm\"\n                >\n                  Siguiente\n                </Button>\n              </div>\n              <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                <div>\n                  <p className=\"text-sm text-gray-700\">\n                    Mostrando <span className=\"font-medium\">{indexOfFirstItem + 1}</span> a <span className=\"font-medium\">{Math.min(indexOfLastItem, data.length)}</span> de{' '}\n                    <span className=\"font-medium\">{data.length}</span> resultados\n                  </p>\n                </div>\n                <div>\n                  <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\n                    <button\n                      onClick={() => paginate(currentPage - 1)}\n                      disabled={currentPage === 1}\n                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 ${\n                        currentPage === 1\n                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                          : 'bg-white text-gray-500 hover:bg-gray-50'\n                      }`}\n                    >\n                      <span className=\"sr-only\">Anterior</span>\n                      <span className=\"text-lg\">&laquo;</span>\n                    </button>\n\n                    {/* Paginación dinámica con elipsis */}\n                    {Array.from({ length: totalPages }).map((_, index) => {\n                      const pageNumber = index + 1;\n                      // Mostrar el primer y último número, los números cercanos a la página actual y elipsis para los demás\n                      const shouldRenderPageNumber =\n                        pageNumber === 1 ||\n                        pageNumber === totalPages ||\n                        (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1);\n\n                      if (shouldRenderPageNumber) {\n                        return (\n                          <button\n                            key={pageNumber}\n                            onClick={() => paginate(pageNumber)}\n                            className={`relative inline-flex items-center px-4 py-2 border ${\n                              currentPage === pageNumber\n                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600 font-medium'\n                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n                            }`}\n                          >\n                            {pageNumber}\n                          </button>\n                        );\n                      } else if (\n                        (pageNumber === 2 && currentPage > 3) ||\n                        (pageNumber === totalPages - 1 && currentPage < totalPages - 2)\n                      ) {\n                        return (\n                          <span\n                            key={pageNumber}\n                            className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700\"\n                          >\n                            ...\n                          </span>\n                        );\n                      }\n                      return null;\n                    })}\n\n                    <button\n                      onClick={() => paginate(currentPage + 1)}\n                      disabled={currentPage === totalPages}\n                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 ${\n                        currentPage === totalPages\n                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                          : 'bg-white text-gray-500 hover:bg-gray-50'\n                      }`}\n                    >\n                      <span className=\"sr-only\">Siguiente</span>\n                      <span className=\"text-lg\">&raquo;</span>\n                    </button>\n                  </nav>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DataTable;", "import React, { useState } from 'react';\n// En lugar de usar react-icons, usaremos SVG directos o HTML Unicode\n// import { FaSearch, FaFilter, FaTimesCircle } from 'react-icons/fa';\nimport { Button } from './Button';\n\n/**\n * Componente reutilizable para búsqueda y filtros\n *\n * @param {Object} props\n * @param {string} props.searchTerm - Término de búsqueda actual\n * @param {Function} props.onSearchChange - Función a ejecutar cuando cambia el término de búsqueda\n * @param {string} props.searchPlaceholder - Texto de placeholder para el campo de búsqueda\n * @param {Array} props.filters - Configuración de filtros disponibles\n * @param {Object} props.filterValues - Valores actuales de los filtros\n * @param {Function} props.onFilterChange - Función a ejecutar cuando cambian los filtros\n * @param {Function} props.onClearFilters - Función para limpiar todos los filtros\n * @param {Function} props.onAddNew - Función para añadir un nuevo elemento\n * @param {boolean} props.canAdd - Si el usuario puede añadir nuevos elementos\n * @param {string} props.addButtonText - Texto del botón para añadir\n */\nconst SearchFilter = ({\n  searchTerm = '',\n  onSearchChange,\n  searchPlaceholder = 'Buscar...',\n  filters = [],\n  filterValues = {},\n  onFilterChange,\n  onClearFilters,\n  onAddNew,\n  canAdd = false,\n  addButtonText = 'Añadir Nuevo'\n}) => {\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Verificar si hay filtros activos\n  const hasActiveFilters = Object.values(filterValues).some(\n    value => value !== '' && value !== null && value !== undefined\n  );\n\n  return (\n    <div className=\"mb-6 space-y-4\">\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div className=\"relative flex-grow max-w-md\">\n          <input\n            type=\"text\"\n            placeholder={searchPlaceholder}\n            className=\"form-input w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n            value={searchTerm}\n            onChange={(e) => onSearchChange(e.target.value)}\n          />\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <span className=\"text-gray-400\">🔍</span>\n          </div>\n          {searchTerm && (\n            <button\n              className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\"\n              onClick={() => onSearchChange('')}\n              title=\"Limpiar búsqueda\"\n            >\n              <span>×</span>\n            </button>\n          )}\n        </div>\n\n        <div className=\"flex space-x-2 justify-end\">\n          {filters.length > 0 && (\n            <Button\n              variant={showFilters || hasActiveFilters ? \"primary\" : \"outline\"}\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center\"\n            >\n              <span className=\"mr-2\">📊</span>\n              {hasActiveFilters\n                ? `Filtros (${Object.values(filterValues).filter(v => v !== '' && v !== null && v !== undefined).length})`\n                : \"Filtros\"}\n            </Button>\n          )}\n\n          {canAdd && (\n            <Button\n              variant=\"primary\"\n              onClick={onAddNew}\n              className=\"flex items-center shadow-sm hover:shadow-md transition-shadow\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z\" clipRule=\"evenodd\" />\n              </svg>\n              <span className=\"font-medium\">{addButtonText}</span>\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Panel de filtros */}\n      {showFilters && filters.length > 0 && (\n        <div className=\"bg-gray-50 p-4 rounded-lg border border-gray-200 animate-fadeIn\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Filtros</h3>\n            {hasActiveFilters && (\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onClearFilters}\n                className=\"text-sm\"\n              >\n                Limpiar todos\n              </Button>\n            )}\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {filters.map((filter) => (\n              <div key={filter.id} className=\"space-y-1\">\n                <label\n                  htmlFor={`filter-${filter.id}`}\n                  className=\"block text-sm font-medium text-gray-700\"\n                >\n                  {filter.label}\n                </label>\n\n                {filter.type === 'select' ? (\n                  <div className=\"relative\">\n                    <select\n                      id={`filter-${filter.id}`}\n                      name={filter.id}\n                      value={filterValues[filter.id] || ''}\n                      onChange={(e) => onFilterChange(filter.id, e.target.value)}\n                      className=\"form-select w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n                    >\n                      <option value=\"\">{filter.placeholder || 'Todos'}</option>\n                      {filter.options.map((option) => (\n                        <option key={option.value} value={option.value}>\n                          {option.label}\n                        </option>\n                      ))}\n                    </select>\n\n                    {filterValues[filter.id] && (\n                      <button\n                        type=\"button\"\n                        onClick={() => onFilterChange(filter.id, '')}\n                        className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\"\n                        title=\"Limpiar filtro\"\n                      >\n                        <span>×</span>\n                      </button>\n                    )}\n                  </div>\n                ) : filter.type === 'range' ? (\n                  <div className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"number\"\n                      id={`filter-${filter.id}-min`}\n                      name={`${filter.id}_min`}\n                      placeholder={filter.minPlaceholder || 'Mín'}\n                      value={filterValues[`${filter.id}_min`] || ''}\n                      onChange={(e) => onFilterChange(`${filter.id}_min`, e.target.value)}\n                      className=\"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n                      min={filter.min}\n                      max={filter.max}\n                    />\n                    <span className=\"text-gray-500\">-</span>\n                    <input\n                      type=\"number\"\n                      id={`filter-${filter.id}-max`}\n                      name={`${filter.id}_max`}\n                      placeholder={filter.maxPlaceholder || 'Máx'}\n                      value={filterValues[`${filter.id}_max`] || ''}\n                      onChange={(e) => onFilterChange(`${filter.id}_max`, e.target.value)}\n                      className=\"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n                      min={filter.min}\n                      max={filter.max}\n                    />\n                  </div>\n                ) : filter.type === 'date' ? (\n                  <div className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"date\"\n                      id={`filter-${filter.id}-from`}\n                      name={`${filter.id}_from`}\n                      value={filterValues[`${filter.id}_from`] || ''}\n                      onChange={(e) => onFilterChange(`${filter.id}_from`, e.target.value)}\n                      className=\"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n                    />\n                    <span className=\"text-gray-500\">a</span>\n                    <input\n                      type=\"date\"\n                      id={`filter-${filter.id}-to`}\n                      name={`${filter.id}_to`}\n                      value={filterValues[`${filter.id}_to`] || ''}\n                      onChange={(e) => onFilterChange(`${filter.id}_to`, e.target.value)}\n                      className=\"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n                    />\n                  </div>\n                ) : (\n                  <div className=\"relative\">\n                    <input\n                      type={filter.type || 'text'}\n                      id={`filter-${filter.id}`}\n                      name={filter.id}\n                      placeholder={filter.placeholder || ''}\n                      value={filterValues[filter.id] || ''}\n                      onChange={(e) => onFilterChange(filter.id, e.target.value)}\n                      className=\"form-input w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n                    />\n\n                    {filterValues[filter.id] && (\n                      <button\n                        type=\"button\"\n                        onClick={() => onFilterChange(filter.id, '')}\n                        className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\"\n                        title=\"Limpiar filtro\"\n                      >\n                        <span>×</span>\n                      </button>\n                    )}\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {hasActiveFilters && (\n            <div className=\"mt-4 text-sm text-gray-700\">\n              <strong>Filtros activos:</strong> {\n                Object.entries(filterValues)\n                  .filter(([_, value]) => value !== '' && value !== null && value !== undefined)\n                  .map(([key, value]) => {\n                    const filter = filters.find(f => f.id === key || key.startsWith(`${f.id}_`));\n                    if (!filter) return null;\n\n                    let label = filter.label;\n                    if (key.endsWith('_min') || key.endsWith('_from')) {\n                      label = `${filter.label} desde`;\n                    } else if (key.endsWith('_max') || key.endsWith('_to')) {\n                      label = `${filter.label} hasta`;\n                    }\n\n                    // Formatear valor si es una opción de un select\n                    let displayValue = value;\n                    if (filter.type === 'select' && filter.options) {\n                      const option = filter.options.find(opt => opt.value === value);\n                      if (option) {\n                        displayValue = option.label;\n                      }\n                    }\n\n                    return (\n                      <span\n                        key={key}\n                        className=\"inline-flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-2 mb-1\"\n                      >\n                        {label}: {displayValue}\n                        <button\n                          type=\"button\"\n                          onClick={() => onFilterChange(key, '')}\n                          className=\"ml-1 text-blue-600 hover:text-blue-800\"\n                          title=\"Quitar filtro\"\n                        >\n                          <span>×</span>\n                        </button>\n                      </span>\n                    );\n                  })\n              }\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchFilter;", "import React, { useState, useEffect } from 'react';\nimport { Modal } from './Modal';\nimport { Button } from './Button';\n// En lugar de usar react-icons, usaremos HTML Unicode o SVG directos\n// import { FaS<PERSON>ner, FaExclamationTriangle } from 'react-icons/fa';\n\n/**\n * Componente de modal con formulario dinámico\n *\n * @param {Object} props\n * @param {boolean} props.isOpen - Estado de apertura del modal\n * @param {Function} props.onClose - Función para cerrar el modal\n * @param {string} props.title - Título del modal\n * @param {Array} props.fields - Configuración de los campos del formulario\n * @param {Object} props.initialValues - Valores iniciales de los campos\n * @param {Function} props.onSubmit - Función a ejecutar al enviar el formulario\n * @param {boolean} props.loading - Estado de carga\n * @param {string} props.submitText - Texto del botón de envío\n * @param {string} props.cancelText - Texto del botón de cancelación\n * @param {string} props.size - Tamaño del modal (sm, md, lg, xl)\n * @param {boolean} props.isEdit - Si es un formulario de edición\n */\nconst FormModal = ({\n  isOpen,\n  onClose,\n  title,\n  fields = [],\n  initialValues = {},\n  onSubmit,\n  loading = false,\n  submitText = 'Guardar',\n  cancelText = 'Cancelar',\n  loadingText = 'Guardando...',\n  size = 'md',\n  isEdit = false\n}) => {\n  const [formValues, setFormValues] = useState({});\n  const [errors, setErrors] = useState({});\n  const [touched, setTouched] = useState({});\n\n  // Inicializar valores del formulario cuando cambian los valores iniciales o se abre el modal\n  useEffect(() => {\n    if (isOpen) {\n      setFormValues({ ...initialValues });\n      setErrors({});\n      setTouched({});\n    }\n  }, [isOpen, initialValues]);\n\n  // Manejar cambios en los campos\n  const handleChange = (fieldId, value) => {\n    setFormValues(prev => ({\n      ...prev,\n      [fieldId]: value\n    }));\n\n    // Marcar campo como tocado\n    setTouched(prev => ({\n      ...prev,\n      [fieldId]: true\n    }));\n\n    // Validar campo y actualizar errores\n    validateField(fieldId, value);\n  };\n\n  // Validar un campo específico\n  const validateField = (fieldId, value) => {\n    const field = fields.find(f => f.id === fieldId);\n    if (!field || !field.validation) return;\n\n    let error = null;\n\n    // Validación requerida\n    if (field.validation.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n      error = field.validation.requiredMessage || 'Este campo es obligatorio';\n    }\n\n    // Validación de expresión regular\n    if (value && field.validation.pattern && !field.validation.pattern.test(value)) {\n      error = field.validation.patternMessage || 'El formato no es válido';\n    }\n\n    // Validación de longitud mínima\n    if (value && field.validation.minLength && value.length < field.validation.minLength) {\n      error = field.validation.minLengthMessage || `Debe tener al menos ${field.validation.minLength} caracteres`;\n    }\n\n    // Validación de longitud máxima\n    if (value && field.validation.maxLength && value.length > field.validation.maxLength) {\n      error = field.validation.maxLengthMessage || `Debe tener máximo ${field.validation.maxLength} caracteres`;\n    }\n\n    // Validación de valor mínimo (para campos numéricos)\n    if (value && field.validation.min !== undefined && parseFloat(value) < field.validation.min) {\n      error = field.validation.minMessage || `El valor mínimo es ${field.validation.min}`;\n    }\n\n    // Validación de valor máximo (para campos numéricos)\n    if (value && field.validation.max !== undefined && parseFloat(value) > field.validation.max) {\n      error = field.validation.maxMessage || `El valor máximo es ${field.validation.max}`;\n    }\n\n    // Validación personalizada\n    if (field.validation.custom && typeof field.validation.custom === 'function') {\n      const customError = field.validation.custom(value, formValues);\n      if (customError) {\n        error = customError;\n      }\n    }\n\n    setErrors(prev => ({\n      ...prev,\n      [fieldId]: error\n    }));\n\n    return error;\n  };\n\n  // Validar todos los campos\n  const validateAllFields = () => {\n    const newErrors = {};\n    let isValid = true;\n\n    fields.forEach(field => {\n      if (field.type === 'section' || field.type === 'divider') return;\n\n      const value = formValues[field.id];\n      const error = validateField(field.id, value);\n\n      if (error) {\n        newErrors[field.id] = error;\n        isValid = false;\n      }\n    });\n\n    setErrors(newErrors);\n    return isValid;\n  };\n\n  // Manejar envío del formulario\n  const handleSubmit = (e) => {\n    e.preventDefault();\n\n    // Marcar todos los campos como tocados\n    const newTouched = {};\n    fields.forEach(field => {\n      if (field.type !== 'section' && field.type !== 'divider') {\n        newTouched[field.id] = true;\n      }\n    });\n    setTouched(newTouched);\n\n    // Validar todos los campos\n    if (validateAllFields()) {\n      onSubmit(formValues);\n    }\n  };\n\n  // Renderizar campo según su tipo\n  const renderField = (field) => {\n    const { id, label, type, placeholder, options, disabled, info, className = '' } = field;\n    const value = formValues[id] !== undefined ? formValues[id] : '';\n    const error = errors[id];\n    const isTouched = touched[id];\n    const showError = error && isTouched;\n\n    // Campo section (título de sección)\n    if (type === 'section') {\n      return (\n        <div key={id} className={`mb-4 ${className}`}>\n          <h3 className=\"text-lg font-medium text-gray-900\">{label}</h3>\n          {info && <p className=\"mt-1 text-sm text-gray-500\">{info}</p>}\n        </div>\n      );\n    }\n\n    // Línea divisoria\n    if (type === 'divider') {\n      return (\n        <div key={id} className={`my-6 border-t border-gray-200 ${className}`}></div>\n      );\n    }\n\n    return (\n      <div key={id} className={`mb-4 ${className}`}>\n        {label && (\n          <label\n            htmlFor={id}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label} {field.validation?.required && <span className=\"text-red-500\">*</span>}\n          </label>\n        )}\n\n        {type === 'text' || type === 'email' || type === 'number' || type === 'tel' || type === 'password' || type === 'date' || type === 'time' ? (\n          <div className=\"relative\">\n            <input\n              type={type}\n              id={id}\n              name={id}\n              value={value}\n              onChange={(e) => handleChange(id, e.target.value)}\n              placeholder={placeholder}\n              disabled={disabled || loading}\n              className={`w-full rounded-md shadow-sm ${\n                showError\n                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500'\n                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'\n              } focus:ring focus:ring-opacity-50 ${\n                disabled ? 'bg-gray-100 cursor-not-allowed' : ''\n              }`}\n              min={field.min}\n              max={field.max}\n              step={field.step}\n              maxLength={field.maxLength}\n            />\n            {showError && (\n              <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <span className=\"text-red-500\">⚠️</span>\n              </div>\n            )}\n          </div>\n        ) : type === 'textarea' ? (\n          <textarea\n            id={id}\n            name={id}\n            value={value}\n            onChange={(e) => handleChange(id, e.target.value)}\n            placeholder={placeholder}\n            disabled={disabled || loading}\n            rows={field.rows || 3}\n            className={`w-full rounded-md shadow-sm ${\n              showError\n                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'\n                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'\n            } focus:ring focus:ring-opacity-50 ${\n              disabled ? 'bg-gray-100 cursor-not-allowed' : ''\n            }`}\n            maxLength={field.maxLength}\n          />\n        ) : type === 'select' ? (\n          <select\n            id={id}\n            name={id}\n            value={value}\n            onChange={(e) => handleChange(id, e.target.value)}\n            disabled={disabled || loading}\n            className={`w-full rounded-md shadow-sm ${\n              showError\n                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'\n                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'\n            } focus:ring focus:ring-opacity-50 ${\n              disabled ? 'bg-gray-100 cursor-not-allowed' : ''\n            }`}\n          >\n            {placeholder && (\n              <option value=\"\">{placeholder}</option>\n            )}\n            {options && options.map((option) => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </select>\n        ) : type === 'radio' ? (\n          <div className=\"mt-2 space-y-2\">\n            {options && options.map((option) => (\n              <div key={option.value} className=\"flex items-center\">\n                <input\n                  id={`${id}-${option.value}`}\n                  name={id}\n                  type=\"radio\"\n                  value={option.value}\n                  checked={value === option.value}\n                  onChange={() => handleChange(id, option.value)}\n                  disabled={disabled || loading}\n                  className={`h-4 w-4 ${\n                    showError\n                      ? 'border-red-300 text-red-600 focus:ring-red-500'\n                      : 'border-gray-300 text-blue-600 focus:ring-blue-500'\n                  } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}\n                />\n                <label\n                  htmlFor={`${id}-${option.value}`}\n                  className={`ml-3 block text-sm font-medium text-gray-700 ${\n                    disabled ? 'text-gray-500' : ''\n                  }`}\n                >\n                  {option.label}\n                </label>\n              </div>\n            ))}\n          </div>\n        ) : type === 'checkbox' ? (\n          <div className=\"flex items-center h-5 mt-1\">\n            <input\n              id={id}\n              name={id}\n              type=\"checkbox\"\n              checked={!!value}\n              onChange={(e) => handleChange(id, e.target.checked)}\n              disabled={disabled || loading}\n              className={`h-4 w-4 rounded ${\n                showError\n                  ? 'border-red-300 text-red-600 focus:ring-red-500'\n                  : 'border-gray-300 text-blue-600 focus:ring-blue-500'\n              } focus:ring focus:ring-opacity-50 ${\n                disabled ? 'bg-gray-100 cursor-not-allowed' : ''\n              }`}\n            />\n            {field.checkboxLabel && (\n              <label\n                htmlFor={id}\n                className={`ml-3 block text-sm text-gray-700 ${\n                  disabled ? 'text-gray-500' : ''\n                }`}\n              >\n                {field.checkboxLabel}\n              </label>\n            )}\n          </div>\n        ) : type === 'checkboxGroup' ? (\n          <div className=\"mt-2 space-y-2\">\n            {options && options.map((option) => (\n              <div key={option.value} className=\"flex items-center\">\n                <input\n                  id={`${id}-${option.value}`}\n                  name={`${id}-${option.value}`}\n                  type=\"checkbox\"\n                  checked={Array.isArray(value) && value.includes(option.value)}\n                  onChange={(e) => {\n                    const checked = e.target.checked;\n                    const currentValues = Array.isArray(value) ? [...value] : [];\n                    const newValues = checked\n                      ? [...currentValues, option.value]\n                      : currentValues.filter(v => v !== option.value);\n                    handleChange(id, newValues);\n                  }}\n                  disabled={disabled || loading}\n                  className={`h-4 w-4 rounded ${\n                    showError\n                      ? 'border-red-300 text-red-600 focus:ring-red-500'\n                      : 'border-gray-300 text-blue-600 focus:ring-blue-500'\n                  } focus:ring focus:ring-opacity-50 ${\n                    disabled ? 'bg-gray-100 cursor-not-allowed' : ''\n                  }`}\n                />\n                <label\n                  htmlFor={`${id}-${option.value}`}\n                  className={`ml-3 block text-sm font-medium text-gray-700 ${\n                    disabled ? 'text-gray-500' : ''\n                  }`}\n                >\n                  {option.label}\n                </label>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <p className=\"text-sm text-red-500\">Tipo de campo no soportado: {type}</p>\n        )}\n\n        {showError && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n\n        {info && !showError && (\n          <p className=\"mt-1 text-sm text-gray-500\">{info}</p>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={title}\n      size={size}\n    >\n      <form onSubmit={handleSubmit}>\n        <div className=\"space-y-1\">\n          {fields.map(renderField)}\n        </div>\n\n        <div className=\"flex justify-end space-x-4 mt-8 border-t border-gray-100 pt-6\">\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={loading}\n            className=\"px-6\"\n          >\n            {cancelText}\n          </Button>\n          <Button\n            type=\"submit\"\n            variant=\"primary\"\n            disabled={loading}\n            className=\"min-w-[120px] px-6\"\n          >\n            {loading ? (\n              <span className=\"flex items-center justify-center\">\n                <span className=\"inline-block animate-spin mr-2\">↻</span>\n                {loadingText}\n              </span>\n            ) : (\n              isEdit ? `Actualizar` : submitText\n            )}\n          </Button>\n        </div>\n      </form>\n    </Modal>\n  );\n};\n\nexport default FormModal;", "import React, { useState, useEffect, useCallback } from 'react';\n// Importamos el servicio mejorado y las utilidades de manejo de errores\nimport enhancedSupabaseService from '../../../services/enhancedSupabaseService';\nimport { toast } from 'react-toastify';\nimport { showErrorToast, showSuccessToast } from '../../../utils/errorHandler';\n\n// Componentes reutilizables\nimport DataTable from '../../../components/ui/DataTable';\nimport SearchFilter from '../../../components/ui/SearchFilter';\nimport FormModal from '../../../components/ui/FormModal';\n\n/**\n * Componente mejorado para la gestión de instituciones\n */\nconst InstitutionsTab = ({ isAdmin }) => {\n  const [institutions, setInstitutions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentInstitution, setCurrentInstitution] = useState(null);\n  const [formValues, setFormValues] = useState({ nombre: '', direccion: '', telefono: '' });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortField, setSortField] = useState('nombre');\n  const [sortDirection, setSortDirection] = useState('asc');\n  const [filterValues, setFilterValues] = useState({});\n\n  // Configuración de columnas para la tabla\n  const columns = [\n    {\n      field: 'nombre',\n      header: 'Nombre',\n      sortable: true,\n      highlight: true,\n    },\n    {\n      field: 'direccion',\n      header: 'Dirección',\n      sortable: true,\n      emptyValue: '-',\n    },\n    {\n      field: 'telefono',\n      header: 'Teléfono',\n      sortable: true,\n      emptyValue: '-',\n    }\n  ];\n\n  // Configuración de campos para el formulario\n  const formFields = [\n    {\n      id: 'nombre',\n      type: 'text',\n      label: 'Nombre',\n      placeholder: 'Ej. Universidad Nacional',\n      validation: {\n        required: true,\n        requiredMessage: 'El nombre es obligatorio',\n        maxLength: 100,\n        maxLengthMessage: 'El nombre debe tener máximo 100 caracteres'\n      }\n    },\n    {\n      id: 'direccion',\n      type: 'text',\n      label: 'Dirección',\n      placeholder: 'Ej. Calle 45 #12-34',\n      info: 'Dirección física de la institución',\n      validation: {\n        maxLength: 200,\n        maxLengthMessage: 'La dirección debe tener máximo 200 caracteres'\n      }\n    },\n    {\n      id: 'telefono',\n      type: 'tel',\n      label: 'Teléfono',\n      placeholder: 'Ej. ************',\n      validation: {\n        pattern: /^[0-9\\s+\\-()]*$/,\n        patternMessage: 'Formato de teléfono inválido'\n      }\n    }\n  ];\n\n  // Configuración de filtros\n  const filters = [\n    // Por ahora no se necesitan filtros específicos para instituciones\n    // Pero se podría agregar filtros como tipo de institución si se requiere\n  ];\n\n  // Función para obtener instituciones usando el servicio mejorado\n  const fetchInstitutions = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Usar la función del servicio mejorado\n      const { data, error, isOffline } = await enhancedSupabaseService.getInstitutions(sortField, sortDirection);\n\n      if (error) {\n        console.error('Error al cargar instituciones:', error);\n        showErrorToast(error, 'cargar', 'instituciones');\n        setInstitutions([]);\n        return;\n      }\n\n      // Establecer los datos\n      setInstitutions(data || []);\n\n      // Mostrar notificación si los datos son de caché\n      if (isOffline) {\n        toast.info(\"Mostrando datos almacenados localmente. Algunas funciones pueden estar limitadas.\");\n      }\n    } catch (error) {\n      console.error('Error inesperado en fetchInstitutions:', error);\n      showErrorToast(error, 'cargar', 'instituciones');\n      setInstitutions([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [sortField, sortDirection]);\n\n  // Cargar instituciones al montar y cuando cambie el orden\n  useEffect(() => {\n    fetchInstitutions();\n  }, [fetchInstitutions]);\n\n  // Abrir modal para crear/editar institución\n  const openModal = (institution = null) => {\n    setCurrentInstitution(institution);\n\n    if (institution) {\n      setFormValues({\n        nombre: institution.nombre,\n        direccion: institution.direccion || '',\n        telefono: institution.telefono || ''\n      });\n    } else {\n      setFormValues({\n        nombre: '',\n        direccion: '',\n        telefono: ''\n      });\n    }\n\n    setIsModalOpen(true);\n  };\n\n  // Cerrar modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n  };\n\n  // Manejar cambio de ordenamiento\n  const handleSort = (field) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  // Manejar cambio en los filtros\n  const handleFilterChange = (filterId, value) => {\n    setFilterValues(prev => ({\n      ...prev,\n      [filterId]: value\n    }));\n  };\n\n  // Limpiar filtros\n  const handleClearFilters = () => {\n    setFilterValues({});\n  };\n\n  // Manejar eliminación de institución\n  const handleDelete = async (id, institution) => {\n    if (!window.confirm(`¿Está seguro que desea eliminar la institución \"${institution.nombre}\"?`)) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Eliminar usando el servicio mejorado\n      const { error, isOffline } = await enhancedSupabaseService.deleteInstitution(id);\n\n      if (error) {\n        showErrorToast(error, 'eliminar', 'institución');\n        return;\n      }\n\n      if (isOffline) {\n        showSuccessToast('Institución eliminada correctamente. Los cambios se sincronizarán cuando haya conexión.');\n      } else {\n        showSuccessToast('Institución eliminada correctamente.');\n      }\n\n      // Actualizar la lista para reflejar la eliminación\n      fetchInstitutions();\n\n    } catch (error) {\n      console.error('Error al eliminar institución:', error);\n      showErrorToast(error, 'eliminar', 'institución');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtrar instituciones por término de búsqueda\n  const filteredInstitutions = institutions.filter(institution =>\n    institution.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (institution.direccion && institution.direccion.toLowerCase().includes(searchTerm.toLowerCase())) ||\n    (institution.telefono && institution.telefono.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  // Manejar envío del formulario\n  const handleSubmit = async (values) => {\n    setLoading(true);\n    try {\n      const institutionData = {\n        nombre: values.nombre,\n        direccion: values.direccion,\n        telefono: values.telefono\n      };\n\n      let result;\n\n      if (currentInstitution) {\n        // Actualizar usando el servicio mejorado\n        result = await enhancedSupabaseService.updateInstitution(currentInstitution.id, institutionData);\n\n        if (result.error) {\n          showErrorToast(result.error, 'actualizar', 'institución');\n          return;\n        }\n\n        if (result.isOffline) {\n          showSuccessToast('Institución actualizada correctamente. Los cambios se sincronizarán cuando haya conexión.');\n        } else {\n          showSuccessToast('Institución actualizada correctamente.');\n        }\n      } else {\n        // Crear usando el servicio mejorado\n        result = await enhancedSupabaseService.createInstitution(institutionData);\n\n        if (result.error) {\n          showErrorToast(result.error, 'crear', 'institución');\n          return;\n        }\n\n        if (result.isOffline) {\n          showSuccessToast('Institución creada correctamente. Los cambios se sincronizarán cuando haya conexión.');\n        } else {\n          showSuccessToast('Institución creada correctamente.');\n        }\n      }\n\n      // Actualizar la lista inmediatamente\n      fetchInstitutions();\n      closeModal();\n\n    } catch (error) {\n      console.error('Error al guardar institución:', error);\n      showErrorToast(error, currentInstitution ? 'actualizar' : 'crear', 'institución');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Barra de búsqueda y botones */}\n      <SearchFilter\n        searchTerm={searchTerm}\n        onSearchChange={setSearchTerm}\n        searchPlaceholder=\"Buscar instituciones...\"\n        filters={filters}\n        filterValues={filterValues}\n        onFilterChange={handleFilterChange}\n        onClearFilters={handleClearFilters}\n        onAddNew={() => openModal()}\n        canAdd={isAdmin}\n        addButtonText=\"Nueva Institución\"\n      />\n\n      {/* Tabla de datos */}\n      <DataTable\n        columns={columns}\n        data={filteredInstitutions}\n        sortField={sortField}\n        sortDirection={sortDirection}\n        onSort={handleSort}\n        loading={loading}\n        enableActions={isAdmin}\n        onEdit={openModal}\n        onDelete={handleDelete}\n        isTemporaryFn={(id) => typeof id === 'string' && id.startsWith('temp-')}\n        emptyMessage=\"No se encontraron instituciones registradas\"\n        actionLabels={{ edit: \"Editar institución\", delete: \"Eliminar institución\" }}\n      />\n\n      {/* Modal de formulario */}\n      <FormModal\n        isOpen={isModalOpen}\n        onClose={closeModal}\n        title={currentInstitution ? 'Editar Institución' : 'Nueva Institución'}\n        fields={formFields}\n        initialValues={formValues}\n        onSubmit={handleSubmit}\n        loading={loading}\n        submitText=\"Guardar\"\n        isEdit={!!currentInstitution}\n      />\n    </div>\n  );\n};\n\nexport default InstitutionsTab;", "import React, { useState, useEffect, useCallback } from 'react';\n// Quitamos las importaciones de react-icons para evitar conflictos\nimport { supabase } from '../../../api/supabaseClient'; // Para auth.signUp\nimport enhancedSupabaseService from '../../../services/enhancedSupabaseService';\nimport { toast } from 'react-toastify';\nimport { showErrorToast, showSuccessToast } from '../../../utils/errorHandler';\n\n// Componentes reutilizables\nimport DataTable from '../../../components/ui/DataTable';\nimport SearchFilter from '../../../components/ui/SearchFilter';\nimport FormModal from '../../../components/ui/FormModal';\n\n/**\n * Componente mejorado para la gestión de psicólogos\n */\nconst PsychologistsTab = ({ isAdmin }) => {\n  const [psychologists, setPsychologists] = useState([]);\n  const [institutions, setInstitutions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPsychologist, setCurrentPsychologist] = useState(null);\n  const [formValues, setFormValues] = useState({\n    nombre: '',\n    apellidos: '',\n    genero: '',\n    email: '',\n    documento_identidad: '',\n    telefono: '',\n    institucion_id: ''\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortField, setSortField] = useState('nombre');\n  const [sortDirection, setSortDirection] = useState('asc');\n  const [filterValues, setFilterValues] = useState({\n    institucion_id: ''\n  });\n\n  // Configuración de columnas para la tabla\n  const columns = [\n    {\n      field: 'nombre',\n      header: 'Nombre',\n      sortable: true,\n      highlight: true,\n    },\n    {\n      field: 'apellidos',\n      header: 'Apellidos',\n      sortable: true,\n    },\n    {\n      field: 'genero',\n      header: 'Género',\n      sortable: true,\n      emptyValue: '-',\n    },\n    {\n      field: 'email',\n      header: 'Email',\n      sortable: true,\n    },\n    {\n      field: 'documento_identidad',\n      header: 'Documento',\n      sortable: true,\n    },\n    {\n      field: 'telefono',\n      header: 'Teléfono',\n      sortable: true,\n      emptyValue: '-',\n    },\n    {\n      field: 'institucion',\n      header: 'Institución',\n      sortable: false,\n      render: (_, row) => {\n        // Renderizar el nombre de la institución\n        return row.instituciones?.nombre ||\n          institutions.find(inst => inst.id === row.institucion_id)?.nombre || '-';\n      }\n    }\n  ];\n\n  // Función para obtener datos usando el servicio mejorado\n  const fetchData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Usar el servicio mejorado para obtener datos\n      const [psychologistsResult, institutionsResult] = await Promise.all([\n        enhancedSupabaseService.getPsychologists(sortField, sortDirection),\n        enhancedSupabaseService.getInstitutions()\n      ]);\n\n      // Manejar resultado de psicólogos\n      if (psychologistsResult.error) {\n        console.error('Error al cargar psicólogos:', psychologistsResult.error);\n        showErrorToast(psychologistsResult.error, 'cargar', 'psicólogos');\n        setPsychologists([]);\n      } else {\n        setPsychologists(psychologistsResult.data || []);\n\n        // Mostrar notificación si los datos son de caché\n        if (psychologistsResult.isOffline) {\n          toast.info(\"Mostrando datos de psicólogos almacenados localmente. Algunas funciones pueden estar limitadas.\");\n        }\n      }\n\n      // Manejar resultado de instituciones\n      if (institutionsResult.error) {\n        console.error('Error al cargar instituciones:', institutionsResult.error);\n        showErrorToast(institutionsResult.error, 'cargar', 'instituciones');\n        setInstitutions([]);\n      } else {\n        setInstitutions(institutionsResult.data || []);\n\n        // Mostrar notificación si los datos son de caché\n        if (institutionsResult.isOffline && !psychologistsResult.isOffline) {\n          toast.info(\"Mostrando datos de instituciones almacenados localmente.\");\n        }\n      }\n    } catch (error) {\n      console.error('Error inesperado al cargar datos:', error);\n      showErrorToast(error, 'cargar', 'datos');\n      setPsychologists([]);\n      setInstitutions([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [sortField, sortDirection]);\n\n  // Cargar datos al montar y cuando cambie el orden\n  useEffect(() => {\n    fetchData();\n  }, [fetchData]);\n\n  // Abrir modal para crear/editar psicólogo\n  const openModal = (psychologist = null) => {\n    setCurrentPsychologist(psychologist);\n\n    if (psychologist) {\n      setFormValues({\n        nombre: psychologist.nombre,\n        apellidos: psychologist.apellidos,\n        genero: psychologist.genero || '',\n        email: psychologist.email,\n        documento_identidad: psychologist.documento_identidad,\n        telefono: psychologist.telefono || '',\n        institucion_id: psychologist.institucion_id\n      });\n    } else {\n      setFormValues({\n        nombre: '',\n        apellidos: '',\n        genero: '',\n        email: '',\n        documento_identidad: '',\n        telefono: '',\n        institucion_id: institutions.length > 0 ? institutions[0].id : ''\n      });\n    }\n\n    setIsModalOpen(true);\n  };\n\n  // Cerrar modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n  };\n\n  // Manejar cambio de ordenamiento\n  const handleSort = (field) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  // Manejar cambio en los filtros\n  const handleFilterChange = (filterId, value) => {\n    setFilterValues(prev => ({\n      ...prev,\n      [filterId]: value\n    }));\n  };\n\n  // Limpiar filtros\n  const handleClearFilters = () => {\n    setFilterValues({\n      institucion_id: ''\n    });\n  };\n\n  // Manejar eliminación de psicólogo\n  const handleDelete = async (id, psychologist) => {\n    if (!window.confirm(`¿Está seguro que desea eliminar al psicólogo \"${psychologist.nombre} ${psychologist.apellidos}\"? Esta acción no se puede deshacer.`)) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Eliminar usando el servicio mejorado\n      const { error, isOffline } = await enhancedSupabaseService.deletePsychologist(id);\n\n      if (error) {\n        showErrorToast(error, 'eliminar', 'psicólogo');\n        return;\n      }\n\n      if (isOffline) {\n        showSuccessToast('Psicólogo eliminado correctamente. Los cambios se sincronizarán cuando haya conexión.');\n      } else {\n        showSuccessToast('Psicólogo eliminado correctamente.');\n      }\n\n      // Actualizar la lista para reflejar la eliminación\n      fetchData();\n\n    } catch (error) {\n      console.error('Error al eliminar psicólogo:', error);\n      showErrorToast(error, 'eliminar', 'psicólogo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtrar psicólogos por término de búsqueda y filtros avanzados\n  const filteredPsychologists = psychologists.filter(psychologist => {\n    // Filtro por término de búsqueda\n    const matchesSearch =\n      psychologist.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      psychologist.apellidos.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (psychologist.genero && psychologist.genero.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      psychologist.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      psychologist.documento_identidad.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (psychologist.telefono && psychologist.telefono.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Filtro por institución\n    const matchesInstitution =\n      !filterValues.institucion_id ||\n      psychologist.institucion_id === filterValues.institucion_id;\n\n    return matchesSearch && matchesInstitution;\n  });\n\n  // Configuración de filtros\n  const filters = [\n    {\n      id: 'institucion_id',\n      type: 'select',\n      label: 'Institución',\n      placeholder: 'Todas las instituciones',\n      options: institutions.map(inst => ({\n        value: inst.id,\n        label: inst.nombre\n      }))\n    }\n  ];\n\n  // Configuración de campos para el formulario\n  const formFields = [\n    {\n      id: 'nombre',\n      type: 'text',\n      label: 'Nombre',\n      placeholder: 'Ej. Juan',\n      validation: {\n        required: true,\n        requiredMessage: 'El nombre es obligatorio',\n        maxLength: 50,\n        maxLengthMessage: 'El nombre debe tener máximo 50 caracteres'\n      }\n    },\n    {\n      id: 'apellidos',\n      type: 'text',\n      label: 'Apellidos',\n      placeholder: 'Ej. Pérez Gómez',\n      validation: {\n        required: true,\n        requiredMessage: 'Los apellidos son obligatorios',\n        maxLength: 50,\n        maxLengthMessage: 'Los apellidos deben tener máximo 50 caracteres'\n      }\n    },\n    {\n      id: 'genero',\n      type: 'select',\n      label: 'Género',\n      placeholder: 'Seleccionar género',\n      options: [\n        { value: 'Masculino', label: 'Masculino' },\n        { value: 'Femenino', label: 'Femenino' },\n        { value: 'Otro', label: 'Otro' },\n        { value: 'No especificado', label: 'No especificado' }\n      ],\n      validation: {\n        required: true,\n        requiredMessage: 'El género es obligatorio'\n      }\n    },\n    {\n      id: 'email',\n      type: 'email',\n      label: 'Email',\n      placeholder: 'Ej. <EMAIL>',\n      disabled: !!currentPsychologist, // Deshabilitar en modo edición\n      info: currentPsychologist ? 'El email no se puede modificar una vez creado el psicólogo.' : 'Se creará una cuenta con este email.',\n      validation: {\n        required: true,\n        requiredMessage: 'El email es obligatorio',\n        pattern: /\\S+@\\S+\\.\\S+/,\n        patternMessage: 'Ingrese un email válido'\n      }\n    },\n    {\n      id: 'documento_identidad',\n      type: 'text',\n      label: 'Documento de Identidad',\n      placeholder: 'Ej. 1234567890',\n      validation: {\n        required: true,\n        requiredMessage: 'El documento de identidad es obligatorio',\n        maxLength: 20,\n        maxLengthMessage: 'El documento debe tener máximo 20 caracteres'\n      }\n    },\n    {\n      id: 'telefono',\n      type: 'tel',\n      label: 'Teléfono',\n      placeholder: 'Ej. ************',\n      validation: {\n        pattern: /^[0-9\\s+\\-()]*$/,\n        patternMessage: 'Formato de teléfono inválido'\n      }\n    },\n    {\n      id: 'institucion_id',\n      type: 'select',\n      label: 'Institución',\n      placeholder: 'Seleccionar institución',\n      options: institutions.map(inst => ({\n        value: inst.id,\n        label: inst.nombre\n      })),\n      validation: {\n        required: true,\n        requiredMessage: 'La institución es obligatoria'\n      }\n    }\n  ];\n\n  // Manejar envío del formulario\n  const handleSubmit = async (values) => {\n    setLoading(true);\n    try {\n      const psychologistData = {\n        nombre: values.nombre,\n        apellidos: values.apellidos,\n        genero: values.genero,\n        email: values.email,\n        documento_identidad: values.documento_identidad,\n        telefono: values.telefono,\n        institucion_id: values.institucion_id,\n      };\n\n      if (currentPsychologist) {\n        // Actualizar usando el servicio mejorado\n        const dataToUpdate = { ...psychologistData };\n        delete dataToUpdate.email; // No permitir cambio de email\n\n        const { error, isOffline } = await enhancedSupabaseService.updatePsychologist(currentPsychologist.id, dataToUpdate);\n\n        if (error) {\n          showErrorToast(error, 'actualizar', 'psicólogo');\n          return;\n        }\n\n        if (isOffline) {\n          showSuccessToast('Psicólogo actualizado correctamente. Los cambios se sincronizarán cuando haya conexión.');\n        } else {\n          showSuccessToast('Psicólogo actualizado correctamente.');\n        }\n      } else {\n        // Crear nuevo psicólogo: Primero Auth, luego tabla psicologos\n        try {\n          // 1. Crear usuario en Supabase Auth\n          console.log('Creando usuario en Auth para:', values.email);\n          const { data: authData, error: authError } = await supabase.auth.signUp({\n            email: values.email,\n            password: 'Temporal123!', // TODO: Considerar una mejor gestión de contraseñas\n            options: {\n              data: {\n                rol: 'psicologo',\n                nombre_completo: `${values.nombre} ${values.apellidos}`\n              }\n            }\n          });\n\n          if (authError) {\n            console.error('Error al crear usuario en Auth:', authError);\n            // Verificar si el error es porque el usuario ya existe\n            if (authError.message.includes('User already registered')) {\n              showErrorToast({ message: 'Ya existe un usuario registrado con este email.' }, 'crear', 'usuario');\n            } else {\n              showErrorToast(authError, 'crear', 'usuario');\n            }\n            return;\n          }\n\n          if (!authData.user) {\n            throw new Error('No se pudo obtener el ID del usuario creado en Auth.');\n          }\n\n          // 2. Crear registro en tabla 'psicologos' usando el servicio mejorado\n          const psychologistPayload = {\n            ...psychologistData,\n            usuario_id: authData.user.id\n          };\n\n          const { error: createError, isOffline } = await enhancedSupabaseService.createPsychologist(psychologistPayload);\n\n          if (createError) {\n            console.error('Error al crear registro de psicólogo:', createError);\n            showErrorToast(createError, 'guardar', 'psicólogo');\n            return;\n          }\n\n          if (isOffline) {\n            showSuccessToast('Psicólogo creado correctamente. Los datos se sincronizarán cuando haya conexión.');\n          } else {\n            showSuccessToast('Psicólogo creado correctamente.');\n          }\n        } catch (authError) {\n          console.error('Error en el proceso de creación de usuario/psicólogo:', authError);\n          showErrorToast(authError, 'crear', 'psicólogo');\n          return;\n        }\n      }\n\n      closeModal();\n      fetchData();\n\n    } catch (error) {\n      console.error('Error final en handleSubmit:', error);\n      showErrorToast(error, currentPsychologist ? 'actualizar' : 'crear', 'psicólogo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Barra de búsqueda y botones */}\n      <SearchFilter\n        searchTerm={searchTerm}\n        onSearchChange={setSearchTerm}\n        searchPlaceholder=\"Buscar psicólogos...\"\n        filters={filters}\n        filterValues={filterValues}\n        onFilterChange={handleFilterChange}\n        onClearFilters={handleClearFilters}\n        onAddNew={() => openModal()}\n        canAdd={isAdmin}\n        addButtonText=\"Nuevo Psicólogo\"\n      />\n\n      {/* Tabla de datos */}\n      <DataTable\n        columns={columns}\n        data={filteredPsychologists}\n        sortField={sortField}\n        sortDirection={sortDirection}\n        onSort={handleSort}\n        loading={loading}\n        enableActions={isAdmin}\n        onEdit={openModal}\n        onDelete={handleDelete}\n        isTemporaryFn={(id) => typeof id === 'string' && id.startsWith('temp-')}\n        emptyMessage=\"No se encontraron psicólogos registrados\"\n        actionLabels={{ edit: \"Editar psicólogo\", delete: \"Eliminar psicólogo\" }}\n      />\n\n      {/* Modal de formulario */}\n      <FormModal\n        isOpen={isModalOpen}\n        onClose={closeModal}\n        title={currentPsychologist ? 'Editar Psicólogo' : 'Nuevo Psicólogo'}\n        fields={formFields}\n        initialValues={formValues}\n        onSubmit={handleSubmit}\n        loading={loading}\n        submitText=\"Guardar\"\n        isEdit={!!currentPsychologist}\n        size=\"lg\"\n      />\n    </div>\n  );\n};\n\nexport default PsychologistsTab;", "import React, { useState, useEffect, useCallback } from 'react';\n// Quitamos las importaciones de react-icons para evitar conflictos\nimport enhancedSupabaseService from '../../../services/enhancedSupabaseService';\nimport { toast } from 'react-toastify';\nimport { showErrorToast, showSuccessToast } from '../../../utils/errorHandler';\n\n// Componentes reutilizables\nimport DataTable from '../../../components/ui/DataTable';\nimport SearchFilter from '../../../components/ui/SearchFilter';\nimport FormModal from '../../../components/ui/FormModal';\n\n/**\n * Componente mejorado para la gestión de pacientes\n */\nconst PatientsTab = ({ isAdmin }) => {\n  const [patients, setPatients] = useState([]);\n  const [institutions, setInstitutions] = useState([]);\n  const [psychologists, setPsychologists] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPatient, setCurrentPatient] = useState(null);\n  const [formValues, setFormValues] = useState({\n    nombre: '',\n    apellidos: '',\n    fecha_nacimiento: '',\n    genero: '',\n    documento_identidad: '',\n    email: '',\n    telefono: '',\n    institucion_id: '',\n    psicologo_id: '',\n    notas: ''\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortField, setSortField] = useState('nombre');\n  const [sortDirection, setSortDirection] = useState('asc');\n  const [filterValues, setFilterValues] = useState({\n    institucion_id: '',\n    genero: '',\n    psicologo_id: '',\n    edad_min: '',\n    edad_max: ''\n  });\n\n  // Configuración de columnas para la tabla\n  const columns = [\n    {\n      field: 'nombre',\n      header: 'Nombre',\n      sortable: true,\n      highlight: true,\n    },\n    {\n      field: 'apellidos',\n      header: 'Apellidos',\n      sortable: true,\n    },\n    {\n      field: 'edad',\n      header: 'Edad',\n      sortable: true,\n      emptyValue: '-',\n      type: 'numeric'\n    },\n    {\n      field: 'genero',\n      header: 'Género',\n      sortable: true,\n    },\n    {\n      field: 'documento_identidad',\n      header: 'Documento',\n      sortable: true,\n      emptyValue: '-',\n    },\n    {\n      field: 'email',\n      header: 'Email',\n      sortable: true,\n      emptyValue: '-',\n    },\n    {\n      field: 'telefono',\n      header: 'Teléfono',\n      sortable: true,\n      emptyValue: '-',\n    },\n    {\n      field: 'institucion',\n      header: 'Institución',\n      sortable: false,\n      render: (_, row) => {\n        return row.instituciones?.nombre ||\n          institutions.find(inst => inst.id === row.institucion_id)?.nombre || '-';\n      }\n    },\n    {\n      field: 'psicologo',\n      header: 'Psicólogo',\n      sortable: false,\n      render: (_, row) => {\n        if (!row.psicologo_id) return '-';\n        const psico = psychologists.find(p => p.id === row.psicologo_id);\n        return psico ? `${psico.nombre} ${psico.apellidos}` : '-';\n      }\n    }\n  ];\n\n  // Función para obtener datos usando el servicio mejorado\n  const fetchData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Fetch all data concurrently usando el servicio mejorado\n      const [patientsResult, institutionsResult, psychologistsResult] = await Promise.all([\n        enhancedSupabaseService.getPatients(sortField, sortDirection),\n        enhancedSupabaseService.getInstitutions(),\n        enhancedSupabaseService.getPsychologists()\n      ]);\n\n      // Manejar resultado de pacientes\n      if (patientsResult.error) {\n        console.error('Error al cargar pacientes:', patientsResult.error);\n        showErrorToast(patientsResult.error, 'cargar', 'pacientes');\n        setPatients([]);\n      } else {\n        setPatients(patientsResult.data || []);\n\n        // Mostrar notificación si los datos son de caché\n        if (patientsResult.isOffline) {\n          toast.info(\"Mostrando datos de pacientes almacenados localmente. Algunas funciones pueden estar limitadas.\");\n        }\n      }\n\n      // Manejar resultado de instituciones\n      if (institutionsResult.error) {\n        console.error('Error al cargar instituciones:', institutionsResult.error);\n        showErrorToast(institutionsResult.error, 'cargar', 'instituciones');\n        setInstitutions([]);\n      } else {\n        setInstitutions(institutionsResult.data || []);\n      }\n\n      // Manejar resultado de psicólogos\n      if (psychologistsResult.error) {\n        console.error('Error al cargar psicólogos:', psychologistsResult.error);\n        showErrorToast(psychologistsResult.error, 'cargar', 'psicólogos');\n        setPsychologists([]);\n      } else {\n        setPsychologists(psychologistsResult.data || []);\n      }\n\n    } catch (error) {\n      console.error('Error inesperado al cargar datos:', error);\n      showErrorToast(error, 'cargar', 'datos');\n      setPatients([]);\n      setInstitutions([]);\n      setPsychologists([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [sortField, sortDirection]);\n\n  // Cargar datos al montar y cuando cambie el orden\n  useEffect(() => {\n    fetchData();\n  }, [fetchData]);\n\n  // Calcular edad a partir de la fecha de nacimiento\n  const calculateAge = (birthDate) => {\n    if (!birthDate) return null;\n\n    const today = new Date();\n    const birth = new Date(birthDate);\n    let age = today.getFullYear() - birth.getFullYear();\n    const monthDiff = today.getMonth() - birth.getMonth();\n\n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {\n      age--;\n    }\n\n    return age;\n  };\n\n  // Abrir modal para crear/editar paciente\n  const openModal = (patient = null) => {\n    setCurrentPatient(patient);\n\n    if (patient) {\n      setFormValues({\n        nombre: patient.nombre,\n        apellidos: patient.apellidos || '',\n        fecha_nacimiento: patient.fecha_nacimiento,\n        genero: patient.genero,\n        documento_identidad: patient.documento_identidad || '',\n        email: patient.email || '',\n        telefono: patient.telefono || '',\n        institucion_id: patient.institucion_id,\n        psicologo_id: patient.psicologo_id || '',\n        notas: patient.notas || ''\n      });\n    } else {\n      setFormValues({\n        nombre: '',\n        apellidos: '',\n        fecha_nacimiento: '',\n        genero: '',\n        documento_identidad: '',\n        email: '',\n        telefono: '',\n        institucion_id: institutions.length > 0 ? institutions[0].id : '',\n        psicologo_id: '',\n        notas: ''\n      });\n    }\n\n    setIsModalOpen(true);\n  };\n\n  // Cerrar modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n  };\n\n  // Manejar cambio de ordenamiento\n  const handleSort = (field) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  // Manejar cambio en los filtros\n  const handleFilterChange = (filterId, value) => {\n    setFilterValues(prev => ({\n      ...prev,\n      [filterId]: value\n    }));\n  };\n\n  // Limpiar filtros\n  const handleClearFilters = () => {\n    setFilterValues({\n      institucion_id: '',\n      genero: '',\n      psicologo_id: '',\n      edad_min: '',\n      edad_max: ''\n    });\n  };\n\n  // Manejar eliminación de paciente\n  const handleDelete = async (id, patient) => {\n    if (!window.confirm(`¿Está seguro que desea eliminar al paciente \"${patient.nombre}\"? Esta acción no se puede deshacer.`)) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Eliminar usando el servicio mejorado\n      const { error, isOffline } = await enhancedSupabaseService.deletePatient(id);\n\n      if (error) {\n        showErrorToast(error, 'eliminar', 'paciente');\n        return;\n      }\n\n      if (isOffline) {\n        showSuccessToast(`Paciente \"${patient.nombre}\" eliminado correctamente. Los cambios se sincronizarán cuando haya conexión.`);\n      } else {\n        showSuccessToast(`Paciente \"${patient.nombre}\" eliminado correctamente.`);\n      }\n\n      // Actualizar la lista para reflejar la eliminación\n      fetchData();\n\n    } catch (error) {\n      console.error('Error al eliminar paciente:', error);\n      showErrorToast(error, 'eliminar', 'paciente');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtrar pacientes por término de búsqueda y filtros avanzados\n  const filteredPatients = patients.filter(patient => {\n    // Filtro por término de búsqueda\n    const matchesSearch =\n      patient.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (patient.apellidos && patient.apellidos.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (patient.documento_identidad && patient.documento_identidad.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (patient.email && patient.email.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (patient.telefono && patient.telefono.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (patient.notas && patient.notas.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Filtro por institución\n    const matchesInstitution =\n      !filterValues.institucion_id || patient.institucion_id === filterValues.institucion_id;\n\n    // Filtro por género\n    const matchesGender =\n      !filterValues.genero || patient.genero === filterValues.genero;\n\n    // Filtro por psicólogo\n    const matchesPsychologist =\n      !filterValues.psicologo_id ||\n      (filterValues.psicologo_id === 'null' ? !patient.psicologo_id : patient.psicologo_id === filterValues.psicologo_id);\n\n    // Filtro por edad mínima\n    const matchesMinAge =\n      !filterValues.edad_min || (patient.edad && patient.edad >= parseInt(filterValues.edad_min));\n\n    // Filtro por edad máxima\n    const matchesMaxAge =\n      !filterValues.edad_max || (patient.edad && patient.edad <= parseInt(filterValues.edad_max));\n\n    // Debe cumplir con todos los filtros\n    return matchesSearch && matchesInstitution && matchesGender &&\n           matchesPsychologist && matchesMinAge && matchesMaxAge;\n  });\n\n  // Configuración de filtros\n  const filters = [\n    {\n      id: 'institucion_id',\n      type: 'select',\n      label: 'Institución',\n      placeholder: 'Todas las instituciones',\n      options: institutions.map(inst => ({\n        value: inst.id,\n        label: inst.nombre\n      }))\n    },\n    {\n      id: 'genero',\n      type: 'select',\n      label: 'Género',\n      placeholder: 'Todos los géneros',\n      options: [\n        { value: 'Masculino', label: 'Masculino' },\n        { value: 'Femenino', label: 'Femenino' }\n      ]\n    },\n    {\n      id: 'psicologo_id',\n      type: 'select',\n      label: 'Psicólogo',\n      placeholder: 'Todos los psicólogos',\n      options: [\n        { value: 'null', label: 'Sin psicólogo asignado' },\n        ...psychologists.map(psico => ({\n          value: psico.id,\n          label: `${psico.nombre} ${psico.apellidos}`\n        }))\n      ]\n    },\n    {\n      id: 'edad',\n      type: 'range',\n      label: 'Edad',\n      minPlaceholder: 'Mínima',\n      maxPlaceholder: 'Máxima',\n      min: 0,\n      max: 120\n    }\n  ];\n\n  // Configuración de campos para el formulario\n  const formFields = [\n    {\n      id: 'nombre',\n      type: 'text',\n      label: 'Nombre',\n      placeholder: 'Ej. Juan',\n      validation: {\n        required: true,\n        requiredMessage: 'El nombre es obligatorio',\n        maxLength: 50,\n        maxLengthMessage: 'El nombre debe tener máximo 50 caracteres'\n      }\n    },\n    {\n      id: 'apellidos',\n      type: 'text',\n      label: 'Apellidos',\n      placeholder: 'Ej. Pérez Gómez',\n      validation: {\n        required: true,\n        requiredMessage: 'Los apellidos son obligatorios',\n        maxLength: 50,\n        maxLengthMessage: 'Los apellidos deben tener máximo 50 caracteres'\n      }\n    },\n    {\n      id: 'fecha_nacimiento',\n      type: 'date',\n      label: 'Fecha de Nacimiento',\n      validation: {\n        required: true,\n        requiredMessage: 'La fecha de nacimiento es obligatoria'\n      }\n    },\n    {\n      id: 'genero',\n      type: 'select',\n      label: 'Género',\n      placeholder: 'Seleccionar género',\n      options: [\n        { value: 'Masculino', label: 'Masculino' },\n        { value: 'Femenino', label: 'Femenino' },\n        { value: 'Otro', label: 'Otro' },\n        { value: 'No especificado', label: 'No especificado' }\n      ],\n      validation: {\n        required: true,\n        requiredMessage: 'El género es obligatorio'\n      }\n    },\n    {\n      id: 'documento_identidad',\n      type: 'text',\n      label: 'Documento de Identidad',\n      placeholder: 'Ej. 1234567890',\n      validation: {\n        required: true,\n        requiredMessage: 'El documento de identidad es obligatorio',\n        maxLength: 20,\n        maxLengthMessage: 'El documento debe tener máximo 20 caracteres'\n      }\n    },\n    {\n      id: 'email',\n      type: 'email',\n      label: 'Email',\n      placeholder: 'Ej. <EMAIL>',\n      validation: {\n        pattern: /\\S+@\\S+\\.\\S+/,\n        patternMessage: 'Ingrese un email válido'\n      }\n    },\n    {\n      id: 'telefono',\n      type: 'tel',\n      label: 'Teléfono',\n      placeholder: 'Ej. ************',\n      validation: {\n        pattern: /^[0-9\\s+\\-()]*$/,\n        patternMessage: 'Formato de teléfono inválido'\n      }\n    },\n    {\n      id: 'institucion_id',\n      type: 'select',\n      label: 'Institución',\n      placeholder: 'Seleccionar institución',\n      options: institutions.map(inst => ({\n        value: inst.id,\n        label: inst.nombre\n      })),\n      validation: {\n        required: true,\n        requiredMessage: 'La institución es obligatoria'\n      }\n    },\n    {\n      id: 'psicologo_id',\n      type: 'select',\n      label: 'Psicólogo Asignado',\n      placeholder: 'Sin psicólogo asignado',\n      options: psychologists.map(psico => ({\n        value: psico.id,\n        label: `${psico.nombre} ${psico.apellidos}`\n      }))\n    },\n    {\n      id: 'notas',\n      type: 'textarea',\n      label: 'Notas',\n      placeholder: 'Información adicional sobre el paciente',\n      rows: 3\n    }\n  ];\n\n  // Manejar envío del formulario\n  const handleSubmit = async (values) => {\n    setLoading(true);\n    try {\n      // Calcular edad\n      const edad = calculateAge(values.fecha_nacimiento);\n\n      // Preparar datos para el servicio\n      const patientData = {\n        nombre: values.nombre,\n        apellidos: values.apellidos,\n        fecha_nacimiento: values.fecha_nacimiento,\n        genero: values.genero,\n        documento_identidad: values.documento_identidad,\n        email: values.email,\n        telefono: values.telefono,\n        institucion_id: values.institucion_id,\n        psicologo_id: values.psicologo_id || null, // Asegurar null si está vacío\n        notas: values.notas,\n        edad: edad,\n      };\n\n      if (currentPatient) {\n        // Actualizar usando el servicio mejorado\n        const { error, isOffline } = await enhancedSupabaseService.updatePatient(currentPatient.id, patientData);\n\n        if (error) {\n          showErrorToast(error, 'actualizar', 'paciente');\n          return;\n        }\n\n        if (isOffline) {\n          showSuccessToast(`Paciente \"${values.nombre}\" actualizado correctamente. Los cambios se sincronizarán cuando haya conexión.`);\n        } else {\n          showSuccessToast(`Paciente \"${values.nombre}\" actualizado correctamente.`);\n        }\n      } else {\n        // Crear usando el servicio mejorado\n        const { error, isOffline } = await enhancedSupabaseService.createPatient(patientData);\n\n        if (error) {\n          showErrorToast(error, 'crear', 'paciente');\n          return;\n        }\n\n        if (isOffline) {\n          showSuccessToast(`Paciente \"${values.nombre}\" creado correctamente. Los datos se sincronizarán cuando haya conexión.`);\n        } else {\n          showSuccessToast(`Paciente \"${values.nombre}\" creado correctamente.`);\n        }\n      }\n\n      // Cerrar modal y recargar lista\n      closeModal();\n      fetchData();\n\n    } catch (error) {\n      console.error('Error al guardar paciente:', error);\n      showErrorToast(error, currentPatient ? 'actualizar' : 'crear', 'paciente');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Barra de búsqueda y botones */}\n      <SearchFilter\n        searchTerm={searchTerm}\n        onSearchChange={setSearchTerm}\n        searchPlaceholder=\"Buscar pacientes...\"\n        filters={filters}\n        filterValues={filterValues}\n        onFilterChange={handleFilterChange}\n        onClearFilters={handleClearFilters}\n        onAddNew={() => openModal()}\n        canAdd={isAdmin}\n        addButtonText=\"Nuevo Paciente\"\n      />\n\n      {/* Tabla de datos */}\n      <DataTable\n        columns={columns}\n        data={filteredPatients}\n        sortField={sortField}\n        sortDirection={sortDirection}\n        onSort={handleSort}\n        loading={loading}\n        enableActions={isAdmin}\n        onEdit={openModal}\n        onDelete={handleDelete}\n        isTemporaryFn={(id) => typeof id === 'string' && id.startsWith('temp-')}\n        emptyMessage=\"No se encontraron pacientes registrados\"\n        actionLabels={{ edit: \"Editar paciente\", delete: \"Eliminar paciente\" }}\n      />\n\n      {/* Modal de formulario */}\n      <FormModal\n        isOpen={isModalOpen}\n        onClose={closeModal}\n        title={currentPatient ? 'Editar Paciente' : 'Nuevo Paciente'}\n        fields={formFields}\n        initialValues={formValues}\n        onSubmit={handleSubmit}\n        loading={loading}\n        submitText=\"Guardar\"\n        isEdit={!!currentPatient}\n        size=\"lg\"\n      />\n    </div>\n  );\n};\n\nexport default PatientsTab;", "import React, { useState } from 'react';\nimport { <PERSON>, CardHeader, CardBody } from '../../components/ui/Card';\nimport { Ta<PERSON>, <PERSON>b<PERSON><PERSON>, Tab, TabPanel } from '../../components/ui/Tabs';\nimport EnhancedSyncStatus from '../../components/admin/EnhancedSyncStatus';\nimport { useSelector } from 'react-redux';\n\n// Importar componentes de pestañas\nimport InstitutionsTab from './administration/InstitutionsTab';\nimport PsychologistsTab from './administration/PsychologistsTab';\nimport PatientsTab from './administration/PatientsTab';\n\n/**\n * Componente principal del panel de administración\n */\nconst Administration = () => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [showInfo, setShowInfo] = useState(false);\n  const user = useSelector(state => state.auth.user);\n  // Asegurarnos de que isAdmin sea true para mostrar los botones de acción\n  const isAdmin = user?.role === 'admin' || user?.role === 'administrador' || true; // Forzar a true para desarrollo\n\n  return (\n    <div className=\"container mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n      {/* Cabecera del panel */}\n      <header className=\"mb-6\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-1\">Panel de Administración</h1>\n            <p className=\"text-gray-600\">\n              Gestión centralizada de recursos de la plataforma\n            </p>\n          </div>\n          <div className=\"flex items-center mt-4 sm:mt-0\">\n            <button\n              onClick={() => setShowInfo(!showInfo)}\n              className=\"mr-3 text-gray-500 hover:text-blue-600 transition-colors focus:outline-none\"\n              title=\"Mostrar información\"\n            >\n              <span style={{ fontSize: '1.25rem' }}>ℹ️</span>\n            </button>\n            <EnhancedSyncStatus />\n          </div>\n        </div>\n\n        {/* Panel de información colapsable */}\n        {showInfo && (\n          <Card className=\"mb-6 border-blue-100 bg-blue-50\">\n            <CardBody className=\"py-3\">\n              <div className=\"text-sm text-gray-600\">\n                <h3 className=\"font-medium text-base mb-2\">Sobre el Panel de Administración</h3>\n                <p className=\"mb-2\">Este panel le permite gestionar todos los aspectos de la plataforma:</p>\n                <ul className=\"list-disc pl-5 space-y-1\">\n                  <li><strong>Instituciones:</strong> Añada, edite o elimine las instituciones asociadas.</li>\n                  <li><strong>Psicólogos:</strong> Gestione los profesionales que pueden administrar pruebas.</li>\n                  <li><strong>Pacientes:</strong> Administre los registros de pacientes y sus asignaciones.</li>\n                </ul>\n                <p className=\"mt-2 text-blue-600\">\n                  <span className=\"font-medium\">Nota:</span> Los cambios realizados se sincronizarán automáticamente cuando haya conexión a internet.\n                </p>\n              </div>\n            </CardBody>\n          </Card>\n        )}\n\n\n      </header>\n\n      {/* Panel principal con pestañas */}\n      <Card className=\"shadow-md border-gray-200\">\n        <CardHeader className=\"bg-white px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-lg font-medium text-gray-900 flex items-center\">\n              <span className=\"mr-2 text-gray-600\">⚙️</span>\n              Administración de Recursos\n            </h2>\n            <div className=\"flex items-center space-x-2\">\n              {isAdmin ? (\n                <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium\">\n                  Acceso Completo\n                </span>\n              ) : (\n                <span className=\"bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium\">\n                  Acceso Limitado\n                </span>\n              )}\n            </div>\n          </div>\n        </CardHeader>\n        <CardBody className=\"p-0\">\n          <Tabs activeTab={activeTab} onChange={setActiveTab}>\n            <TabList className=\"px-6 pt-2 bg-gray-50\">\n              <Tab>\n                <div className=\"flex items-center\">\n                  <span className=\"mr-2\">🏢</span>\n                  Instituciones\n                </div>\n              </Tab>\n              <Tab>\n                <div className=\"flex items-center\">\n                  <span className=\"mr-2\">👨‍⚕️</span>\n                  Psicólogos\n                </div>\n              </Tab>\n              <Tab>\n                <div className=\"flex items-center\">\n                  <span className=\"mr-2\">👥</span>\n                  Pacientes\n                </div>\n              </Tab>\n            </TabList>\n\n            <div className=\"p-6\">\n              <TabPanel>\n                <InstitutionsTab isAdmin={isAdmin} />\n              </TabPanel>\n              <TabPanel>\n                <PsychologistsTab isAdmin={isAdmin} />\n              </TabPanel>\n              <TabPanel>\n                <PatientsTab isAdmin={isAdmin} />\n              </TabPanel>\n            </div>\n          </Tabs>\n        </CardBody>\n      </Card>\n    </div>\n  );\n};\n\nexport default Administration;"], "names": ["Tabs", "children", "activeTab", "onChange", "currentTab", "setCurrentTab", "useState", "useEffect", "handleTabChange", "index", "tabList", "tabPanels", "Children", "for<PERSON>ach", "child", "type", "TabList", "React", "cloneElement", "onTabChange", "TabPanel", "push", "jsxs", "className", "jsx", "length", "map", "isActive", "onClick", "Tab", "jsxRuntimeExports", "EnhancedSyncStatus", "syncStatus", "setSyncStatus", "pendingCount", "lastSyncAttempt", "operations", "isSyncing", "setIsSyncing", "showDetails", "setShowDetails", "interval", "setInterval", "updateSyncStatus", "clearInterval", "status", "enhancedSupabaseService", "getSyncStatus", "handleSync", "async", "result", "syncPendingOperations", "success", "showSuccessToast", "syncedCount", "showErrorToast", "message", "errors", "error", "formatDate", "timestamp", "Date", "toLocaleString", "day", "month", "year", "hour", "minute", "getOperationText", "operation", "entity", "instituciones", "psicologos", "pacientes", "CREATE", "UPDATE", "DELETE", "id", "disabled", "title", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "fillRule", "clipRule", "op", "DataTable", "columns", "data", "sortField", "sortDirection", "onSort", "loading", "enableActions", "onEdit", "onDelete", "isTemporaryFn", "itemsPerPage", "emptyMessage", "actionLabels", "edit", "delete", "currentPage", "setCurrentPage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column", "item", "render", "field", "value", "emptyValue", "indexOfLastItem", "indexOfFirstItem", "currentItems", "slice", "totalPages", "Math", "ceil", "paginate", "pageNumber", "scope", "sortable", "width", "header", "rowIndex", "colIndex", "highlight", "<PERSON><PERSON>", "variant", "size", "min", "Array", "from", "_", "strokeLinecap", "strokeLinejoin", "SearchFilter", "searchTerm", "onSearchChange", "searchPlaceholder", "filters", "filterValues", "onFilterChange", "onClearFilters", "onAddNew", "canAdd", "addButtonText", "showFilters", "setShowFilters", "hasActiveFilters", "Object", "values", "some", "placeholder", "e", "target", "filter", "v", "htmlFor", "label", "name", "options", "option", "minPlaceholder", "max", "maxPlaceholder", "entries", "key", "find", "f", "startsWith", "endsWith", "displayValue", "opt", "FormModal", "isOpen", "onClose", "fields", "initialValues", "onSubmit", "submitText", "cancelText", "loadingText", "isEdit", "formValues", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "setErrors", "touched", "setTouched", "handleChange", "fieldId", "prev", "validateField", "validation", "required", "trim", "requiredMessage", "pattern", "test", "patternMessage", "<PERSON><PERSON><PERSON><PERSON>", "minLengthMessage", "max<PERSON><PERSON><PERSON>", "maxLengthMessage", "parseFloat", "minMessage", "maxMessage", "custom", "customError", "Modal", "preventDefault", "newTouched", "newErrors", "<PERSON><PERSON><PERSON><PERSON>", "validate<PERSON>ll<PERSON>ields", "info", "isTouched", "showError", "_a", "step", "rows", "checked", "checkboxLabel", "isArray", "includes", "currentV<PERSON>ues", "newValues", "InstitutionsTab", "isAdmin", "institutions", "setInstitutions", "setLoading", "isModalOpen", "setIsModalOpen", "currentInstitution", "setCurrentInstitution", "nombre", "direccion", "telefono", "setSearchTerm", "setSortField", "setSortDirection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchInstitutions", "useCallback", "isOffline", "getInstitutions", "console", "toast", "openModal", "institution", "closeModal", "filteredInstitutions", "toLowerCase", "filterId", "window", "confirm", "deleteInstitution", "institutionData", "updateInstitution", "createInstitution", "PsychologistsTab", "psychologists", "setPsychologists", "currentPsychologist", "setCurrentPsychologist", "<PERSON><PERSON><PERSON><PERSON>", "genero", "email", "documento_identidad", "institucion_id", "row", "_b", "inst", "fetchData", "psychologists<PERSON><PERSON><PERSON>", "institutionsResult", "Promise", "all", "getPsychologists", "psychologist", "filteredPsychologists", "matchesSearch", "matchesInstitution", "formFields", "deletePsychologist", "<PERSON><PERSON><PERSON>", "dataToUpdate", "updatePsychologist", "log", "authData", "authError", "supabase", "auth", "signUp", "password", "rol", "nombre_completo", "user", "Error", "psychologist<PERSON><PERSON><PERSON>", "usuario_id", "createError", "createPsychologist", "PatientsTab", "patients", "setPatients", "currentPatient", "setCurrentPatient", "fecha_nacimiento", "psicologo_id", "notas", "edad_min", "edad_max", "psico", "p", "patientsResult", "getPatients", "patient", "filteredPatients", "matchesGender", "matchesPsychologist", "matchesMinAge", "edad", "parseInt", "matchesMaxAge", "deletePatient", "birthDate", "today", "birth", "age", "getFullYear", "monthDiff", "getMonth", "getDate", "calculateAge", "patientData", "updatePatient", "createPatient", "Administration", "setActiveTab", "showInfo", "setShowInfo", "useSelector", "state", "role", "style", "fontSize", "Card", "CardBody", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "sOAEO,MAAMA,EAAO,EAAGC,WAAUC,YAAY,EAAGC,eAC9C,MAAOC,EAAYC,GAAiBC,WAASJ,GAE7CK,EAAAA,WAAU,KACRF,EAAcH,EAAS,GACtB,CAACA,IAEE,MAAAM,EAAmBC,IACvBJ,EAAcI,GACVN,GACFA,EAASM,EACX,EAIF,IAAIC,EAAU,KACVC,EAAY,GAcd,OAZOC,EAAAA,SAAAC,QAAQZ,GAAWa,IACtBA,EAAMC,OAASC,EACPN,EAAAO,EAAMC,aAAaJ,EAAO,CAClCZ,UAAWE,EACXe,YAAaX,IAENM,EAAMC,OAASK,GACxBT,EAAUU,KAAKP,EACjB,MAIAQ,KAAC,MAAI,CAAAC,UAAU,OACZtB,SAAA,CAAAS,EACDc,EAAAA,IAAC,OAAID,UAAU,mBACZtB,WAAUwB,OAAS,GAAKd,EAAUP,OAEvC,EAISY,EAAU,EAAGf,WAAUC,YAAWiB,iBAE1CK,EAAAA,IAAA,MAAA,CAAID,UAAU,2BACbtB,WAACuB,IAAA,MAAA,CAAID,UAAU,wBACZtB,SAASW,EAAAA,SAAAc,IAAIzB,GAAU,CAACa,EAAOL,IACvBQ,EAAMC,aAAaJ,EAAO,CAC/Ba,SAAUlB,IAAUP,EACpB0B,QAAS,IAAMT,EAAYV,GAC3BA,gBAQCoB,EAAM,EAAG5B,WAAU0B,WAAUC,UAASnB,WAE/CqB,EAAAN,IAAC,SAAA,CACCD,UAAW,6CACTI,EACI,gCACA,8EAENC,UAEC3B,aAKMmB,EAAW,EAAGnB,oBACjB,OAAKA,aCjET8B,EAAqB,KACzB,MAAOC,EAAYC,GAAiB3B,WAAS,CAC3C4B,aAAc,EACdC,gBAAiB,KACjBC,WAAY,MAEPC,EAAWC,GAAgBhC,YAAS,IACpCiC,EAAaC,GAAkBlC,YAAS,GAG/CC,EAAAA,WAAU,SAIF,MAAAkC,EAAWC,YAAYC,EAAkB,KAExC,MAAA,IAAMC,cAAcH,EAAQ,GAClC,IAGH,MAAME,EAAmB,KACjB,MAAAE,EAASC,EAAwBC,gBACvCd,EAAcY,EAAM,EAIhBG,EAAaC,UACb,IAAAZ,EAAA,CAEJC,GAAa,GAET,IACI,MAAAY,QAAeJ,EAAwBK,wBAEzCD,EAAOE,QACQC,EAAA,8BAA8BH,EAAOI,0CAEvCC,EAAA,CACbC,QAAS,2BAA2BN,EAAOI,0CAA0CJ,EAAOO,OAAOhC,8BAKhGiC,GACQH,EAAAG,EAAO,cAAe,QAAO,CAC5C,QACApB,GAAa,EACf,CApBe,CAoBf,EAIIqB,EAAcC,IAClB,IAAKA,EAAkB,MAAA,QAGhB,OADM,IAAIC,KAAKD,GACVE,eAAe,QAAS,CAClCC,IAAK,UACLC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,WACT,EAIGC,EAAoBC,IACxB,MAYMC,EAZY,CAChBC,cAAe,cACfC,WAAY,YACZC,UAAW,YASYJ,EAAUC,SAAWD,EAAUC,OAGxD,MAAO,GATS,CACdI,OAAQ,QACRC,OAAQ,aACRC,OAAQ,YAIWP,EAAUtD,OAASsD,EAAUtD,QAEhCuD,UAAeD,EAAUQ,KAAE,EAI7C,SAAAvD,KAAC,MAAI,CAAAC,UAAU,WACbtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,oBAEbtB,SAAA,CAAA6B,EAAAR,KAAC,SAAA,CACCM,QAAS,IAAMI,EAAWE,aAAe,EAAIc,IAAeR,GAAgBD,GAC5EhB,UAAW,mFACTc,EACI,4BACAL,EAAWE,aAAe,EACxB,oDACA,kDAER4C,SAAUzC,EACV0C,MAAO/C,EAAWE,aAAe,EAAI,iCAAmC,2BAEvEjC,SAAA,CACCoC,EAAAf,EAAAA,KAAC,OAAIC,UAAU,+CAA+CyD,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YACnHjF,SAAA,GAAAuB,IAAC,SAAO,CAAAD,UAAU,aAAa4D,GAAG,KAAKC,GAAG,KAAKC,EAAE,KAAKC,OAAO,eAAeC,YAAY,YACvF,OAAK,CAAAhE,UAAU,aAAa0D,KAAK,eAAeO,EAAE,uHAGpDhE,EAAAA,IAAA,OAAA,CAAKD,UAAW,2CACfS,EAAWE,aAAe,EAAI,8BAAgC,kBAGjEG,EACG,mBACAL,EAAWE,aAAe,EACxB,GAAGF,EAAWE,yBAAqD,IAA5BF,EAAWE,aAAqB,IAAM,KAC7E,kBAKRJ,EAAAN,IAAC,SAAA,CACCI,QAAS,IAAMY,GAAgBD,GAC/BhB,UAAU,4DACVwD,MAAM,iCAEN9E,eAAC,MAAI,CAAA+E,MAAM,6BAA6BzD,UAAU,UAAU2D,QAAQ,YAAYD,KAAK,eACnFhF,eAAC,QAAKwF,SAAS,UAAUD,EAAE,mIAAmIE,SAAS,mBAM5KnD,SACE,MAAI,CAAAhB,UAAU,uFACbtB,SAACqB,EAAAA,KAAA,MAAA,CAAIC,UAAU,MACbtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,yCACbtB,SAAA,CAACuB,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCtB,SAAwB,6BAC1E6B,EAAAN,IAAC,SAAA,CACCI,QAAS,IAAMY,GAAe,GAC9BjB,UAAU,oCAEVtB,eAAC,MAAI,CAAA+E,MAAM,6BAA6BzD,UAAU,UAAU2D,QAAQ,YAAYD,KAAK,eACnFhF,eAAC,QAAKwF,SAAS,UAAUD,EAAE,qMAAqME,SAAS,qBAK/OpE,KAAC,MAAI,CAAAC,UAAU,iCACbtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,yCACbtB,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,oCAAoCtB,SAAO,kBAC1D,OAAK,CAAAsB,UAAW,+CACfc,EACI,4BACAL,EAAWE,aAAe,EACxB,gCACA,+BAELjC,SACGoC,EAAA,mBACAL,EAAWE,aAAe,EACxB,qBACA,sBAKVZ,KAAC,MAAI,CAAAC,UAAU,wBACbtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,uBACbtB,SAAA,GAAAuB,IAAC,QAAKvB,SAAsB,iCAC3B,OAAK,CAAAsB,UAAU,cAAetB,SAAW0D,EAAA3B,EAAWG,wBAEvDb,KAAC,MAAI,CAAAC,UAAU,4BACbtB,SAAA,GAAAuB,IAAC,QAAKvB,SAAuB,4BAC7B6B,EAAAN,IAAC,OAAK,CAAAD,UAAW,gBAAeS,EAAWE,aAAe,EAAI,kBAAoB,kBAC/EjC,SAAA+B,EAAWE,wBAMnBF,EAAWI,WAAWX,OAAS,GAC7BH,EAAAA,KAAA,MAAA,CAAIC,UAAU,OACbtB,SAAA,GAACqB,KAAA,KAAA,CAAGC,UAAU,2DACZtB,SAAA,CAAAuB,MAAC,OAAIwD,MAAM,6BAA6BzD,UAAU,+BAA+B2D,QAAQ,YAAYD,KAAK,eACxGhF,eAAC,QAAKwF,SAAS,UAAUD,EAAE,oHAAoHE,SAAS,cACpJ,mCAGP,MAAI,CAAAnE,UAAU,+CACbtB,SAAAuB,EAAAA,IAAC,MAAGD,UAAU,0EACXtB,SAAW+B,EAAAI,WAAWV,KAAI,CAACiE,EAAIlF,WAC7B,KAAA,CAAec,UAAU,wBACxBtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,oBACbtB,SAAA,GAAAuB,IAAC,OAAK,CAAAD,UAAW,2CACH,WAAZoE,EAAG5E,KAAoB,eACX,WAAZ4E,EAAG5E,KAAoB,cAAgB,sBAExC,OAAK,CAAAQ,UAAU,cAAetB,SAAAmE,EAAiBuB,cAEjD,MAAI,CAAApE,UAAU,mCACZtB,SAAW0D,EAAAgC,EAAG/B,eATVnD,cAkBnBa,KAAC,MAAI,CAAAC,UAAU,oCACbtB,SAAA,CAAAuB,EAAAA,IAAC,OAAID,UAAU,wBACZtB,WAAWiC,aAAe,EACvB,gFACA,2CAENJ,EAAAN,IAAC,SAAA,CACCI,QAASoB,EACT8B,SAAUzC,GAAyC,IAA5BL,EAAWE,aAClCX,UAAW,+CACTc,EACI,+CACAL,EAAWE,aAAe,EACxB,2CACA,gDAGPjC,SACCoC,SAAC,OAAK,CAAAd,UAAU,oBACdtB,SAAA,CAACqB,EAAAA,KAAA,MAAA,CAAIC,UAAU,+CAA+CyD,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YACnHjF,SAAA,GAAAuB,IAAC,SAAO,CAAAD,UAAU,aAAa4D,GAAG,KAAKC,GAAG,KAAKC,EAAE,KAAKC,OAAO,eAAeC,YAAY,YACvF,OAAK,CAAAhE,UAAU,aAAa0D,KAAK,eAAeO,EAAE,uHAC/C,mBAGN,gCAMhB,ECnOEI,EAAY,EAChBC,UAAU,GACVC,OAAO,GACPC,YAAY,GACZC,gBAAgB,MAChBC,SACAC,WAAU,EACVC,iBAAgB,EAChBC,SACAC,WACAC,gBACAC,eAAe,GACfC,eAAe,2BACfC,eAAe,CAAEC,KAAM,SAAUC,OAAQ,gBAEzC,MAAOC,EAAaC,GAAkBvG,WAAS,GAG/CC,EAAAA,WAAU,KACRsG,EAAe,EAAC,GACf,CAACf,EAAKrE,SAGH,MAOAqF,EAAkB,CAACC,EAAQC,EAAMvG,KAErC,GAAIsG,EAAOE,OACT,OAAOF,EAAOE,OAAOD,EAAKD,EAAOG,OAAQF,EAAMvG,GAI3C,MAAA0G,EAAQH,EAAKD,EAAOG,OAGtB,OAAAC,QACKJ,EAAOK,YAAc,IAGvBD,CAAA,EAIHE,EAAkBT,EAAcL,EAChCe,EAAmBD,EAAkBd,EACrCgB,EAAezB,EAAK0B,MAAMF,EAAkBD,GAC5CI,EAAaC,KAAKC,KAAK7B,EAAKrE,OAAS8E,GAErCqB,EAAYC,GAAehB,EAAegB,GAG9C,SAAAvG,KAAC,MAAI,CAAAC,UAAU,WAEZtB,SAAA,CAAAiG,SACE,MAAI,CAAA3E,UAAU,gFACbtB,SAACqB,EAAAA,KAAA,MAAA,CAAIC,UAAU,cACbtB,SAAA,GAACuB,IAAA,MAAA,CAAID,UAAU,mFACdC,EAAAA,IAAA,IAAA,CAAED,UAAU,gBAAgBtB,SAAiB,2BAMlDiG,GAA2B,IAAhBJ,EAAKrE,OAoBhBH,EAAAA,KAAC,MAAI,CAAAC,UAAU,8DACbtB,SAAA,GAACqB,KAAA,QAAA,CAAMC,UAAU,sCACftB,SAAA,CAAAuB,MAAC,QAAM,CAAAD,UAAU,aACftB,SAAA6B,EAAAR,KAAC,KACE,CAAArB,SAAA,CAAQ4F,EAAAnE,KAAI,CAACqF,EAAQtG,IACpBqB,EAAAN,IAAC,KAAA,CAECsG,MAAM,MACNvG,UAAW,mFACW,IAApBwF,EAAOgB,SAAqB,mCAAqC,MAC/DhB,EAAOiB,MAAQjB,EAAOiB,MAAQ,KAClCpG,QAAS,KAAMmF,OAAoB,IAApBA,EAAOgB,WA5EpBb,EA4EqDH,EAAOG,WA3E1EjB,GACFA,EAAOiB,KAFQ,IAACA,CA4EiE,EAEnEjH,SAAA6B,EAAAR,KAAC,MAAI,CAAAC,UAAU,oBACbtB,SAAA,GAACuB,IAAA,OAAA,CAAMvB,WAAOgI,UACO,IAApBlB,EAAOgB,UACNvG,EAAAA,IAAC,OAAK,CAAAD,UAAU,OACbtB,SAAA8F,IAAcgB,EAAOG,MACF,QAAlBlB,EACGxE,EAAAA,IAAA,OAAA,CAAKD,UAAU,gBAAgBtB,SAAC,YAEhC,OAAA,CAAKsB,UAAU,gBAAgBtB,SAAC,MAGlC6B,EAAAN,IAAA,OAAA,CAAKD,UAAU,gBAAgBtB,qBAlBnCQ,KAyBR0F,GACCrE,EAAAN,IAAC,KAAA,CACCsG,MAAM,MACNvG,UAAU,wFACXtB,SAAA,kBAMPuB,EAAAA,IAAC,SAAMD,UAAU,oCACdtB,WAAayB,KAAI,CAACsF,EAAMkB,IACvBpG,EAAAR,KAAC,KAAA,CAECC,UAAW,qBACT+E,GAAiBA,EAAcU,EAAKnC,IAAM,iCAAmC,IAG9E5E,SAAA,CAAQ4F,EAAAnE,KAAI,CAACqF,EAAQoB,IACpBrG,EAAAN,IAAC,KAAA,CAECD,UAAW,uCACO,YAAhBwF,EAAOhG,KAAqB,aAAe,MACzCgG,EAAOqB,UAAY,4BAA8B,kBAEpDnI,SAAA6G,EAAgBC,EAAQC,EAAMkB,IAL1B,GAAGA,KAAYC,OAQvBhC,SACE,KAAG,CAAA5E,UAAU,8DACZtB,SAACqB,EAAAA,KAAA,MAAA,CAAIC,UAAU,gCACZtB,SAAA,CACCmG,GAAAtE,EAAAN,IAAC,SAAA,CACCI,QAAS,IAAMwE,EAAOY,GACtBzF,UAAU,oGACVwD,MAAO0B,EAAaC,KAEpBzG,WAACuB,IAAA,MAAA,CAAIwD,MAAM,6BAA6BzD,UAAU,UAAU2D,QAAQ,YAAYD,KAAK,eACnFhF,eAAC,OAAK,CAAAuF,EAAE,gIAIba,GACCvE,EAAAN,IAAC,SAAA,CACCI,QAAS,IAAMyE,EAASW,EAAKnC,GAAImC,GACjCzF,UAAU,gGACVwD,MAAO0B,EAAaE,OAEpB1G,eAAC,MAAI,CAAA+E,MAAM,6BAA6BzD,UAAU,UAAU2D,QAAQ,YAAYD,KAAK,eACnFhF,eAAC,QAAKwF,SAAS,UAAUD,EAAE,8MAA8ME,SAAS,uBApCzPsB,EAAKnC,IAAMqD,UAiDvBT,EAAa,UACX,MAAA,CAAIlG,UAAU,gFACbtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,wCACbtB,SAAA,CAAA6B,EAAAN,IAAC6G,EAAA,CACCC,QAAQ,UACR1G,QAAS,IAAMgG,EAAShB,EAAc,GACtC9B,SAA0B,IAAhB8B,EACV2B,KAAK,KACNtI,SAAA,aAGD6B,EAAAN,IAAC6G,EAAA,CACCC,QAAQ,UACR1G,QAAS,IAAMgG,EAAShB,EAAc,GACtC9B,SAAU8B,IAAgBa,EAC1Bc,KAAK,KACNtI,SAAA,mBAIHqB,KAAC,MAAI,CAAAC,UAAU,8DACbtB,SAAA,CAAAuB,MAAC,MACC,CAAAvB,SAAAqB,EAAAA,KAAC,IAAE,CAAAC,UAAU,wBAAwBtB,SAAA,CAAA,aACxBuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,cAAetB,WAAmB,IAAS,MAAGuB,EAAAA,IAAC,QAAKD,UAAU,cAAetB,cAAKuI,IAAInB,EAAiBvB,EAAKrE,UAAe,MAAI,IACxJD,EAAAA,IAAA,OAAA,CAAKD,UAAU,cAAetB,WAAKwB,SAAc,yBAGrD,MACC,CAAAxB,SAAAqB,OAAC,OAAIC,UAAU,4DAA4D,aAAW,aACpFtB,SAAA,CAAA6B,EAAAR,KAAC,SAAA,CACCM,QAAS,IAAMgG,EAAShB,EAAc,GACtC9B,SAA0B,IAAhB8B,EACVrF,UAAW,oFACO,IAAhBqF,EACI,+CACA,2CAGN3G,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,UAAUtB,SAAQ,aACjCuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,UAAUtB,SAAO,SAIlCwI,MAAMC,KAAK,CAAEjH,OAAQgG,IAAc/F,KAAI,CAACiH,EAAGlI,KAC1C,MAAMoH,EAAapH,EAAQ,EAO3B,OAJiB,IAAfoH,GACAA,IAAeJ,GACdI,GAAcjB,EAAc,GAAKiB,GAAcjB,EAAc,EAI5D9E,EAAAN,IAAC,SAAA,CAECI,QAAS,IAAMgG,EAASC,GACxBtG,UAAW,uDACTqF,IAAgBiB,EACZ,4DACA,2DAGL5H,SAAA4H,GARIA,GAYO,IAAfA,GAAoBjB,EAAc,GAClCiB,IAAeJ,EAAa,GAAKb,EAAca,EAAa,EAG3D3F,EAAAN,IAAC,OAAA,CAECD,UAAU,4FACXtB,SAAA,OAFM4H,GAOJ,IAAA,IAGT/F,EAAAR,KAAC,SAAA,CACCM,QAAS,IAAMgG,EAAShB,EAAc,GACtC9B,SAAU8B,IAAgBa,EAC1BlG,UAAW,oFACTqF,IAAgBa,EACZ,+CACA,2CAGNxH,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,UAAUtB,SAAS,cAClCuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,UAAUtB,SAAO,8BA3M9C,MAAA,CAAIsB,UAAU,gEACbtB,SAAA,CAAA6B,EAAAN,IAAC,MAAA,CACCD,UAAU,kCACV0D,KAAK,OACLC,QAAQ,YACRI,OAAO,eACP,cAAY,OAEZrF,SAAA6B,EAAAN,IAAC,OAAA,CACCoH,cAAc,QACdC,eAAe,QACftD,YAAY,IACZC,EAAE,2HAGLhE,EAAAA,IAAA,KAAA,CAAGD,UAAU,yCAAyCtB,SAAY,iBAClEuB,EAAAA,IAAA,IAAA,CAAED,UAAU,6BAA8BtB,SAAauG,SAoM9D,ECzREsC,EAAe,EACnBC,aAAa,GACbC,iBACAC,oBAAoB,YACpBC,UAAU,GACVC,eAAe,CAAC,EAChBC,iBACAC,iBACAC,WACAC,UAAS,EACTC,gBAAgB,mBAEhB,MAAOC,EAAaC,GAAkBpJ,YAAS,GAGzCqJ,EAAmBC,OAAOC,OAAOV,GAAcW,MAC1C3C,GAAU,KAAVA,SAAgBA,IAIzB,SAAA7F,KAAC,MAAI,CAAAC,UAAU,iBACbtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,qEACbtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,8BACbtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACCT,KAAK,OACLgJ,YAAad,EACb1H,UAAU,iJACV4F,MAAO4B,EACP5I,SAAW6J,GAAMhB,EAAegB,EAAEC,OAAO9C,WAE3C3F,IAAC,OAAID,UAAU,uEACbtB,eAAC,OAAK,CAAAsB,UAAU,gBAAgBtB,SAAA,SAEjC8I,GACCjH,EAAAN,IAAC,SAAA,CACCD,UAAU,sFACVK,QAAS,IAAMoH,EAAe,IAC9BjE,MAAM,mBAEN9E,SAAAuB,EAAAA,IAAC,QAAKvB,SAAC,aAKbqB,KAAC,MAAI,CAAAC,UAAU,6BACZtB,SAAA,CAAAiJ,EAAQzH,OAAS,GAChBK,EAAAR,KAAC+G,EAAA,CACCC,QAASmB,GAAeE,EAAmB,UAAY,UACvD/H,QAAS,IAAM8H,GAAgBD,GAC/BlI,UAAU,oBAEVtB,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,OAAOtB,SAAE,OACxB0J,EACG,YAAYC,OAAOC,OAAOV,GAAce,QAAOC,GAAW,KAANA,SAAYA,IAA+B1I,UAC/F,aAIP8H,GACCzH,EAAAR,KAAC+G,EAAA,CACCC,QAAQ,UACR1G,QAAS0H,EACT/H,UAAU,gEAEVtB,SAAA,CAAAuB,MAAC,OAAIwD,MAAM,6BAA6BzD,UAAU,eAAe2D,QAAQ,YAAYD,KAAK,eACxFhF,eAAC,QAAKwF,SAAS,UAAUD,EAAE,wFAAwFE,SAAS,cAE7HlE,EAAAA,IAAA,OAAA,CAAKD,UAAU,cAAetB,SAAcuJ,aAOpDC,GAAeP,EAAQzH,OAAS,GAC9BH,EAAAA,KAAA,MAAA,CAAIC,UAAU,kEACbtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,yCACbtB,SAAA,CAACuB,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCtB,SAAO,YACxD0J,GACC7H,EAAAN,IAAC6G,EAAA,CACCC,QAAQ,YACRC,KAAK,KACL3G,QAASyH,EACT9H,UAAU,UACXtB,SAAA,uBAMLuB,IAAC,MAAI,CAAAD,UAAU,uDACZtB,SAAAiJ,EAAQxH,KAAKwI,GACZpI,EAAAR,KAAC,MAAoB,CAAAC,UAAU,YAC7BtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACC4I,QAAS,UAAUF,EAAOrF,KAC1BtD,UAAU,0CAETtB,SAAOiK,EAAAG,QAGO,WAAhBH,EAAOnJ,KACLO,EAAAA,KAAA,MAAA,CAAIC,UAAU,WACbtB,SAAA,CAAA6B,EAAAR,KAAC,SAAA,CACCuD,GAAI,UAAUqF,EAAOrF,KACrByF,KAAMJ,EAAOrF,GACbsC,MAAOgC,EAAae,EAAOrF,KAAO,GAClC1E,SAAW6J,GAAMZ,EAAec,EAAOrF,GAAImF,EAAEC,OAAO9C,OACpD5F,UAAU,qIAEVtB,SAAA,CAAAuB,MAAC,SAAO,CAAA2F,MAAM,GAAIlH,SAAAiK,EAAOH,aAAe,UACvCG,EAAOK,QAAQ7I,KAAK8I,GAClBhJ,EAAAA,IAAA,SAAA,CAA0B2F,MAAOqD,EAAOrD,MACtClH,SAAAuK,EAAOH,OADGG,EAAOrD,YAMvBgC,EAAae,EAAOrF,KACnB/C,EAAAN,IAAC,SAAA,CACCT,KAAK,SACLa,QAAS,IAAMwH,EAAec,EAAOrF,GAAI,IACzCtD,UAAU,sFACVwD,MAAM,iBAEN9E,SAAAuB,EAAAA,IAAC,QAAKvB,SAAC,WAIK,UAAhBiK,EAAOnJ,YACR,MAAA,CAAIQ,UAAU,8BACbtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACCT,KAAK,SACL8D,GAAI,UAAUqF,EAAOrF,SACrByF,KAAM,GAAGJ,EAAOrF,SAChBkF,YAAaG,EAAOO,gBAAkB,MACtCtD,MAAOgC,EAAa,GAAGe,EAAOrF,WAAa,GAC3C1E,SAAW6J,GAAMZ,EAAe,GAAGc,EAAOrF,SAAUmF,EAAEC,OAAO9C,OAC7D5F,UAAU,oIACViH,IAAK0B,EAAO1B,IACZkC,IAAKR,EAAOQ,MAEblJ,EAAAA,IAAA,OAAA,CAAKD,UAAU,gBAAgBtB,SAAC,MACjC6B,EAAAN,IAAC,QAAA,CACCT,KAAK,SACL8D,GAAI,UAAUqF,EAAOrF,SACrByF,KAAM,GAAGJ,EAAOrF,SAChBkF,YAAaG,EAAOS,gBAAkB,MACtCxD,MAAOgC,EAAa,GAAGe,EAAOrF,WAAa,GAC3C1E,SAAW6J,GAAMZ,EAAe,GAAGc,EAAOrF,SAAUmF,EAAEC,OAAO9C,OAC7D5F,UAAU,oIACViH,IAAK0B,EAAO1B,IACZkC,IAAKR,EAAOQ,SAGE,SAAhBR,EAAOnJ,YACR,MAAA,CAAIQ,UAAU,8BACbtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACCT,KAAK,OACL8D,GAAI,UAAUqF,EAAOrF,UACrByF,KAAM,GAAGJ,EAAOrF,UAChBsC,MAAOgC,EAAa,GAAGe,EAAOrF,YAAc,GAC5C1E,SAAW6J,GAAMZ,EAAe,GAAGc,EAAOrF,UAAWmF,EAAEC,OAAO9C,OAC9D5F,UAAU,sIAEXC,EAAAA,IAAA,OAAA,CAAKD,UAAU,gBAAgBtB,SAAC,MACjC6B,EAAAN,IAAC,QAAA,CACCT,KAAK,OACL8D,GAAI,UAAUqF,EAAOrF,QACrByF,KAAM,GAAGJ,EAAOrF,QAChBsC,MAAOgC,EAAa,GAAGe,EAAOrF,UAAY,GAC1C1E,SAAW6J,GAAMZ,EAAe,GAAGc,EAAOrF,QAASmF,EAAEC,OAAO9C,OAC5D5F,UAAU,yIAIdD,EAAAA,KAAC,MAAI,CAAAC,UAAU,WACbtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACCT,KAAMmJ,EAAOnJ,MAAQ,OACrB8D,GAAI,UAAUqF,EAAOrF,KACrByF,KAAMJ,EAAOrF,GACbkF,YAAaG,EAAOH,aAAe,GACnC5C,MAAOgC,EAAae,EAAOrF,KAAO,GAClC1E,SAAW6J,GAAMZ,EAAec,EAAOrF,GAAImF,EAAEC,OAAO9C,OACpD5F,UAAU,sIAGX4H,EAAae,EAAOrF,KACnB/C,EAAAN,IAAC,SAAA,CACCT,KAAK,SACLa,QAAS,IAAMwH,EAAec,EAAOrF,GAAI,IACzCtD,UAAU,sFACVwD,MAAM,iBAEN9E,SAAAuB,EAAAA,IAAC,QAAKvB,SAAC,aArGPiK,EAAOrF,QA8GpB8E,GACC7H,EAAAR,KAAC,MAAI,CAAAC,UAAU,6BACbtB,SAAA,GAAAuB,IAAC,UAAOvB,SAAgB,qBAAS,IAC/B2J,OAAOgB,QAAQzB,GACZe,QAAO,EAAEvB,EAAGxB,KAAqB,KAAVA,SAAgBA,IACvCzF,KAAI,EAAEmJ,EAAK1D,MACV,MAAM+C,EAAShB,EAAQ4B,MAAKC,GAAKA,EAAElG,KAAOgG,GAAOA,EAAIG,WAAW,GAAGD,EAAElG,SACrE,IAAKqF,EAAe,OAAA,KAEpB,IAAIG,EAAQH,EAAOG,MACfQ,EAAII,SAAS,SAAWJ,EAAII,SAAS,SAC/BZ,EAAA,GAAGH,EAAOG,eACTQ,EAAII,SAAS,SAAWJ,EAAII,SAAS,UACtCZ,EAAA,GAAGH,EAAOG,eAIpB,IAAIa,EAAe/D,EACnB,GAAoB,WAAhB+C,EAAOnJ,MAAqBmJ,EAAOK,QAAS,CACxC,MAAAC,EAASN,EAAOK,QAAQO,MAAYK,GAAAA,EAAIhE,QAAUA,IACpDqD,IACFU,EAAeV,EAAOH,MAE1B,CAGE,OAAAvI,EAAAR,KAAC,OAAA,CAECC,UAAU,8FAETtB,SAAA,CAAAoK,EAAM,KAAGa,EACVpJ,EAAAN,IAAC,SAAA,CACCT,KAAK,SACLa,QAAS,IAAMwH,EAAeyB,EAAK,IACnCtJ,UAAU,yCACVwD,MAAM,gBAEN9E,SAAAuB,EAAAA,IAAC,QAAKvB,SAAC,UAVJ4K,EAAA,YAoBzB,ECvPEO,EAAY,EAChBC,SACAC,UACAvG,QACAwG,SAAS,GACTC,gBAAgB,CAAC,EACjBC,WACAvF,WAAU,EACVwF,aAAa,UACbC,aAAa,WACbC,cAAc,eACdrD,OAAO,KACPsD,UAAS,MAET,MAAOC,EAAYC,GAAiBzL,EAAAA,SAAS,CAAE,IACxCmD,EAAQuI,GAAa1L,EAAAA,SAAS,CAAE,IAChC2L,EAASC,GAAc5L,EAAAA,SAAS,CAAE,GAGzCC,EAAAA,WAAU,KACJ8K,IACYU,EAAA,IAAKP,IACnBQ,EAAU,CAAE,GACZE,EAAW,CAAE,GACf,GACC,CAACb,EAAQG,IAGN,MAAAW,EAAe,CAACC,EAASjF,KAC7B4E,GAAuBM,IAAA,IAClBA,EACHD,CAACA,GAAUjF,MAIb+E,GAAoBG,IAAA,IACfA,EACHD,CAACA,IAAU,MAIbE,EAAcF,EAASjF,EAAK,EAIxBmF,EAAgB,CAACF,EAASjF,KAC9B,MAAMD,EAAQqE,EAAOT,MAAUC,GAAAA,EAAElG,KAAOuH,IACpC,IAAClF,IAAUA,EAAMqF,WAAY,OAEjC,IAAI7I,EAAQ,KAiCZ,GA9BIwD,EAAMqF,WAAWC,YAAcrF,GAA2B,iBAAVA,GAAuC,KAAjBA,EAAMsF,UACtE/I,EAAAwD,EAAMqF,WAAWG,iBAAmB,6BAI1CvF,GAASD,EAAMqF,WAAWI,UAAYzF,EAAMqF,WAAWI,QAAQC,KAAKzF,KAC9DzD,EAAAwD,EAAMqF,WAAWM,gBAAkB,2BAIzC1F,GAASD,EAAMqF,WAAWO,WAAa3F,EAAM1F,OAASyF,EAAMqF,WAAWO,YACzEpJ,EAAQwD,EAAMqF,WAAWQ,kBAAoB,uBAAuB7F,EAAMqF,WAAWO,wBAInF3F,GAASD,EAAMqF,WAAWS,WAAa7F,EAAM1F,OAASyF,EAAMqF,WAAWS,YACzEtJ,EAAQwD,EAAMqF,WAAWU,kBAAoB,qBAAqB/F,EAAMqF,WAAWS,wBAIjF7F,QAAkC,IAAzBD,EAAMqF,WAAW/D,KAAqB0E,WAAW/F,GAASD,EAAMqF,WAAW/D,MACtF9E,EAAQwD,EAAMqF,WAAWY,YAAc,sBAAsBjG,EAAMqF,WAAW/D,OAI5ErB,QAAkC,IAAzBD,EAAMqF,WAAW7B,KAAqBwC,WAAW/F,GAASD,EAAMqF,WAAW7B,MACtFhH,EAAQwD,EAAMqF,WAAWa,YAAc,sBAAsBlG,EAAMqF,WAAW7B,OAI5ExD,EAAMqF,WAAWc,QAA6C,mBAA5BnG,EAAMqF,WAAWc,OAAuB,CAC5E,MAAMC,EAAcpG,EAAMqF,WAAWc,OAAOlG,EAAO2E,GAC/CwB,IACM5J,EAAA4J,EAEZ,CAOO,OALPtB,GAAmBK,IAAA,IACdA,EACHD,CAACA,GAAU1I,MAGNA,CAAA,EAmQP,OAAA5B,EAAAN,IAAC+L,EAAA,CACClC,SACAC,UACAvG,QACAwD,OAEAtI,SAAA6B,EAAAR,KAAC,OAAK,CAAAmK,SAhPYzB,IACpBA,EAAEwD,iBAGF,MAAMC,EAAa,CAAA,EACZlC,EAAA1K,SAAiBqG,IACH,YAAfA,EAAMnG,MAAqC,YAAfmG,EAAMnG,OACzB0M,EAAAvG,EAAMrC,KAAM,EACzB,IAEFqH,EAAWuB,GA/Ba,MACxB,MAAMC,EAAY,CAAA,EAClB,IAAIC,GAAU,EAeP,OAbApC,EAAA1K,SAAiBqG,IACtB,GAAmB,YAAfA,EAAMnG,MAAqC,YAAfmG,EAAMnG,KAAoB,OAEpD,MAAAoG,EAAQ2E,EAAW5E,EAAMrC,IACzBnB,EAAQ4I,EAAcpF,EAAMrC,GAAIsC,GAElCzD,IACQgK,EAAAxG,EAAMrC,IAAMnB,EACZiK,GAAA,EACZ,IAGF3B,EAAU0B,GACHC,CAAA,EAiBHC,IACFnC,EAASK,EACX,EAkOI7L,SAAA,CAAAuB,MAAC,OAAID,UAAU,YACZtB,SAAOsL,EAAA7J,KA/NKwF,UACb,MAAArC,GAAEA,EAAIwF,MAAAA,EAAAtJ,KAAOA,EAAMgJ,YAAAA,EAAAQ,QAAaA,WAASzF,EAAU+I,KAAAA,EAAAtM,UAAMA,EAAY,IAAO2F,EAC5EC,OAA2B,IAAnB2E,EAAWjH,GAAoBiH,EAAWjH,GAAM,GACxDnB,EAAQD,EAAOoB,GACfiJ,EAAY7B,EAAQpH,GACpBkJ,EAAYrK,GAASoK,EAG3B,MAAa,YAAT/M,EAECO,EAAAA,KAAA,MAAA,CAAaC,UAAW,QAAQA,IAC/BtB,SAAA,CAACuB,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAqCtB,SAAMoK,IACxDwD,KAAQrM,IAAC,IAAE,CAAAD,UAAU,6BAA8BtB,SAAK4N,MAFjDhJ,GAQD,YAAT9D,QAEC,MAAa,CAAAQ,UAAW,iCAAiCA,KAAhDsD,GAKXvD,EAAAA,KAAA,MAAA,CAAaC,UAAW,QAAQA,IAC9BtB,SAAA,CACCoK,GAAAvI,EAAAR,KAAC,QAAA,CACC8I,QAASvF,EACTtD,UAAU,+CAETtB,SAAA,CAAAoK,EAAM,KAAE,OAAA2D,EAAM9G,EAAAqF,iBAAY,EAAAyB,EAAAxB,iBAAa,OAAK,CAAAjL,UAAU,eAAetB,SAAC,SAIjE,SAATc,GAA4B,UAATA,GAA6B,WAATA,GAA8B,QAATA,GAA2B,aAATA,GAAgC,SAATA,GAA4B,SAATA,EACtHO,EAAAA,KAAA,MAAA,CAAIC,UAAU,WACbtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACCT,OACA8D,KACAyF,KAAMzF,EACNsC,QACAhH,SAAW6J,GAAMmC,EAAatH,EAAImF,EAAEC,OAAO9C,OAC3C4C,cACAjF,SAAUA,GAAYoB,EACtB3E,UAAW,+BACTwM,EACI,yDACA,gGAEJjJ,EAAW,iCAAmC,KAEhD0D,IAAKtB,EAAMsB,IACXkC,IAAKxD,EAAMwD,IACXuD,KAAM/G,EAAM+G,KACZjB,UAAW9F,EAAM8F,YAElBe,GACEjM,EAAAN,IAAA,MAAA,CAAID,UAAU,wEACbtB,eAAC,OAAK,CAAAsB,UAAU,eAAetB,SAAA,YAI1B,aAATc,EACFe,EAAAN,IAAC,WAAA,CACCqD,KACAyF,KAAMzF,EACNsC,QACAhH,SAAW6J,GAAMmC,EAAatH,EAAImF,EAAEC,OAAO9C,OAC3C4C,cACAjF,SAAUA,GAAYoB,EACtBgI,KAAMhH,EAAMgH,MAAQ,EACpB3M,UAAW,+BACTwM,EACI,yDACA,gGAEJjJ,EAAW,iCAAmC,KAEhDkI,UAAW9F,EAAM8F,YAER,WAATjM,EACFe,EAAAR,KAAC,SAAA,CACCuD,KACAyF,KAAMzF,EACNsC,QACAhH,SAAW6J,GAAMmC,EAAatH,EAAImF,EAAEC,OAAO9C,OAC3CrC,SAAUA,GAAYoB,EACtB3E,UAAW,+BACTwM,EACI,yDACA,gGAEJjJ,EAAW,iCAAmC,KAG/C7E,SAAA,CAAA8J,KACEvI,IAAA,SAAA,CAAO2F,MAAM,GAAIlH,SAAY8J,IAE/BQ,GAAWA,EAAQ7I,KAAK8I,GACtBhJ,EAAAA,IAAA,SAAA,CAA0B2F,MAAOqD,EAAOrD,MACtClH,SAAAuK,EAAOH,OADGG,EAAOrD,YAKb,UAATpG,EACFS,EAAAA,IAAC,OAAID,UAAU,iBACZtB,SAAWsK,GAAAA,EAAQ7I,KAAK8I,GACtBlJ,EAAAA,KAAA,MAAA,CAAuBC,UAAU,oBAChCtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACCqD,GAAI,GAAGA,KAAM2F,EAAOrD,QACpBmD,KAAMzF,EACN9D,KAAK,QACLoG,MAAOqD,EAAOrD,MACdgH,QAAShH,IAAUqD,EAAOrD,MAC1BhH,SAAU,IAAMgM,EAAatH,EAAI2F,EAAOrD,OACxCrC,SAAUA,GAAYoB,EACtB3E,UAAW,WACTwM,EACI,iDACA,uDACFjJ,EAAW,iCAAmC,OAEpDhD,EAAAN,IAAC,QAAA,CACC4I,QAAS,GAAGvF,KAAM2F,EAAOrD,QACzB5F,UAAW,iDACTuD,EAAW,gBAAkB,IAG9B7E,SAAOuK,EAAAH,UArBFG,EAAOrD,WA0BV,aAATpG,IACFO,KAAC,MAAI,CAAAC,UAAU,6BACbtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACCqD,KACAyF,KAAMzF,EACN9D,KAAK,WACLoN,UAAWhH,EACXhH,SAAW6J,GAAMmC,EAAatH,EAAImF,EAAEC,OAAOkE,SAC3CrJ,SAAUA,GAAYoB,EACtB3E,UAAW,mBACTwM,EACI,iDACA,wFAEJjJ,EAAW,iCAAmC,OAGjDoC,EAAMkH,eACLtM,EAAAN,IAAC,QAAA,CACC4I,QAASvF,EACTtD,UAAW,qCACTuD,EAAW,gBAAkB,IAG9B7E,SAAMiH,EAAAkH,mBAIF,kBAATrN,EACFS,EAAAA,IAAC,OAAID,UAAU,iBACZtB,SAAWsK,GAAAA,EAAQ7I,KAAK8I,GACtBlJ,EAAAA,KAAA,MAAA,CAAuBC,UAAU,oBAChCtB,SAAA,CAAA6B,EAAAN,IAAC,QAAA,CACCqD,GAAI,GAAGA,KAAM2F,EAAOrD,QACpBmD,KAAM,GAAGzF,KAAM2F,EAAOrD,QACtBpG,KAAK,WACLoN,QAAS1F,MAAM4F,QAAQlH,IAAUA,EAAMmH,SAAS9D,EAAOrD,OACvDhH,SAAW6J,IACH,MAAAmE,EAAUnE,EAAEC,OAAOkE,QACnBI,EAAgB9F,MAAM4F,QAAQlH,GAAS,IAAIA,GAAS,GACpDqH,EAAYL,EACd,IAAII,EAAe/D,EAAOrD,OAC1BoH,EAAcrE,QAAYC,GAAAA,IAAMK,EAAOrD,QAC3CgF,EAAatH,EAAI2J,EAAS,EAE5B1J,SAAUA,GAAYoB,EACtB3E,UAAW,mBACTwM,EACI,iDACA,wFAEJjJ,EAAW,iCAAmC,OAGlDhD,EAAAN,IAAC,QAAA,CACC4I,QAAS,GAAGvF,KAAM2F,EAAOrD,QACzB5F,UAAW,iDACTuD,EAAW,gBAAkB,IAG9B7E,SAAOuK,EAAAH,UA7BFG,EAAOrD,WAmCpB7F,OAAA,IAAA,CAAEC,UAAU,uBAAuBtB,SAAA,CAAA,+BAA6Bc,KAGlEgN,KACCvM,IAAC,IAAE,CAAAD,UAAU,4BAA6BtB,SAAMyD,IAGjDmK,IAASE,SACP,IAAE,CAAAxM,UAAU,6BAA8BtB,SAAK4N,MAvL1ChJ,EAyLV,QAgBEvD,KAAC,MAAI,CAAAC,UAAU,gEACbtB,SAAA,CAAA6B,EAAAN,IAAC6G,EAAA,CACCtH,KAAK,SACLuH,QAAQ,UACR1G,QAAS0J,EACTxG,SAAUoB,EACV3E,UAAU,OAETtB,SAAA0L,IAEH7J,EAAAN,IAAC6G,EAAA,CACCtH,KAAK,SACLuH,QAAQ,UACRxD,SAAUoB,EACV3E,UAAU,qBAETtB,SACCiG,SAAC,OAAK,CAAA3E,UAAU,mCACdtB,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,iCAAiCtB,SAAC,MACjD2L,KAGHC,EAAS,aAAeH,WAIhC,EC9YA+C,EAAkB,EAAGC,cACzB,MAAOC,EAAcC,GAAmBtO,EAAAA,SAAS,KAC1C4F,EAAS2I,GAAcvO,YAAS,IAChCwO,EAAaC,GAAkBzO,YAAS,IACxC0O,EAAoBC,GAAyB3O,WAAS,OACtDwL,EAAYC,GAAiBzL,EAASA,SAAA,CAAE4O,OAAQ,GAAIC,UAAW,GAAIC,SAAU,MAC7ErG,EAAYsG,GAAiB/O,WAAS,KACtCyF,EAAWuJ,GAAgBhP,WAAS,WACpC0F,EAAeuJ,GAAoBjP,WAAS,QAC5C6I,EAAcqG,GAAmBlP,EAAAA,SAAS,CAAE,GAoE7CmP,EAAoBC,EAAAA,aAAYzM,UACpC4L,GAAW,GACP,IAEI,MAAA/I,KAAEA,QAAMpC,EAAOiM,UAAAA,SAAoB7M,EAAwB8M,gBAAgB7J,EAAWC,GAE5F,GAAItC,EAIF,OAHQmM,QAAAnM,MAAM,iCAAkCA,GACjCH,EAAAG,EAAO,SAAU,sBAChCkL,EAAgB,IAKFA,EAAA9I,GAAQ,IAGpB6J,GACFG,EAAMjC,KAAK,2FAENnK,GACCmM,QAAAnM,MAAM,yCAA0CA,GACzCH,EAAAG,EAAO,SAAU,iBAChCkL,EAAgB,GAAE,CAClB,QACAC,GAAW,EACb,IACC,CAAC9I,EAAWC,IAGfzF,EAAAA,WAAU,WAEP,CAACkP,IAGE,MAAAM,EAAY,CAACC,EAAc,QAC/Bf,EAAsBe,GAGNjE,EADZiE,EACY,CACZd,OAAQc,EAAYd,OACpBC,UAAWa,EAAYb,WAAa,GACpCC,SAAUY,EAAYZ,UAAY,IAGtB,CACZF,OAAQ,GACRC,UAAW,GACXC,SAAU,KAIdL,GAAe,EAAI,EAIfkB,EAAa,KACjBlB,GAAe,EAAK,EA4DhBmB,EAAuBvB,EAAazE,QACxC8F,GAAAA,EAAYd,OAAOiB,cAAc7B,SAASvF,EAAWoH,gBACpDH,EAAYb,WAAaa,EAAYb,UAAUgB,cAAc7B,SAASvF,EAAWoH,gBACjFH,EAAYZ,UAAYY,EAAYZ,SAASe,cAAc7B,SAASvF,EAAWoH,iBA0DhF,SAAA7O,KAAC,MAAI,CAAAC,UAAU,YAEbtB,SAAA,CAAA6B,EAAAN,IAACsH,EAAA,CACCC,aACAC,eAAgBqG,EAChBpG,kBAAkB,0BAClBC,QA9LU,GA+LVC,eACAC,eAnHqB,CAACgH,EAAUjJ,KACpCqI,GAAyBnD,IAAA,IACpBA,EACH+D,CAACA,GAAWjJ,KACZ,EAgHEkC,eA5GqB,KACzBmG,EAAgB,CAAE,EAAA,EA4GdlG,SAAU,IAAMyG,IAChBxG,OAAQmF,EACRlF,cAAc,sBAIhB1H,EAAAN,IAACoE,EAAA,CACCC,QApQU,CACd,CACEqB,MAAO,SACPe,OAAQ,SACRF,UAAU,EACVK,WAAW,GAEb,CACElB,MAAO,YACPe,OAAQ,YACRF,UAAU,EACVX,WAAY,KAEd,CACEF,MAAO,WACPe,OAAQ,WACRF,UAAU,EACVX,WAAY,MAoPVtB,KAAMoK,EACNnK,YACAC,gBACAC,OA1IciB,IACdnB,IAAcmB,EACCqI,EAAkB,QAAlBvJ,EAA0B,OAAS,QAEpDsJ,EAAapI,GACbqI,EAAiB,OACnB,EAqIIrJ,UACAC,cAAeuI,EACftI,OAAQ2J,EACR1J,SAvHepD,MAAO4B,EAAImL,KAC9B,GAAKK,OAAOC,QAAQ,mDAAmDN,EAAYd,YAAnF,CAIAL,GAAW,GACP,IAEF,MAAMnL,MAAEA,EAAOiM,UAAAA,SAAoB7M,EAAwByN,kBAAkB1L,GAE7E,GAAInB,EAEF,YADeH,EAAAG,EAAO,WAAY,eAKlCL,EADEsM,EACe,0FAEA,kDAMZjM,GACCmM,QAAAnM,MAAM,iCAAkCA,GACjCH,EAAAG,EAAO,WAAY,cAAa,CAC/C,QACAmL,GAAW,EACb,CA1BA,CA0BA,EA2FIvI,cAAgBzB,GAAqB,iBAAPA,GAAmBA,EAAGmG,WAAW,SAC/DxE,aAAa,8CACbC,aAAc,CAAEC,KAAM,qBAAsBC,OAAQ,0BAItD7E,EAAAN,IAAC4J,EAAA,CACCC,OAAQyD,EACRxD,QAAS2E,EACTlL,MAAOiK,EAAqB,qBAAuB,oBACnDzD,OAjQa,CACjB,CACE1G,GAAI,SACJ9D,KAAM,OACNsJ,MAAO,SACPN,YAAa,2BACbwC,WAAY,CACVC,UAAU,EACVE,gBAAiB,2BACjBM,UAAW,IACXC,iBAAkB,+CAGtB,CACEpI,GAAI,YACJ9D,KAAM,OACNsJ,MAAO,YACPN,YAAa,sBACb8D,KAAM,qCACNtB,WAAY,CACVS,UAAW,IACXC,iBAAkB,kDAGtB,CACEpI,GAAI,WACJ9D,KAAM,MACNsJ,MAAO,WACPN,YAAa,mBACbwC,WAAY,CACVI,QAAS,kBACTE,eAAgB,kCAmOhBrB,cAAeM,EACfL,SA5FexI,MAAO4G,IAC1BgF,GAAW,GACP,IACF,MAAM2B,EAAkB,CACtBtB,OAAQrF,EAAOqF,OACfC,UAAWtF,EAAOsF,UAClBC,SAAUvF,EAAOuF,UAGf,IAAAlM,EAEJ,GAAI8L,EAAoB,CAItB,GAFA9L,QAAeJ,EAAwB2N,kBAAkBzB,EAAmBnK,GAAI2L,GAE5EtN,EAAOQ,MAET,YADeH,EAAAL,EAAOQ,MAAO,aAAc,eAIzCR,EAAOyM,UACTtM,EAAiB,6FAEjBA,EAAiB,yCACnB,KACK,CAIL,GAFSH,QAAMJ,EAAwB4N,kBAAkBF,GAErDtN,EAAOQ,MAET,YADeH,EAAAL,EAAOQ,MAAO,QAAS,eAIpCR,EAAOyM,UACTtM,EAAiB,wFAEjBA,EAAiB,oCAErB,eAMOK,GACCmM,QAAAnM,MAAM,gCAAiCA,GAC/CH,EAAeG,EAAOsL,EAAqB,aAAe,QAAS,cAAa,CAChF,QACAH,GAAW,EACb,GA2CI3I,UACAwF,WAAW,UACXG,SAAUmD,MAEd,ECzSE2B,EAAmB,EAAGjC,cAC1B,MAAOkC,EAAeC,GAAoBvQ,EAAAA,SAAS,KAC5CqO,EAAcC,GAAmBtO,EAAAA,SAAS,KAC1C4F,EAAS2I,GAAcvO,YAAS,IAChCwO,EAAaC,GAAkBzO,YAAS,IACxCwQ,EAAqBC,GAA0BzQ,WAAS,OACxDwL,EAAYC,GAAiBzL,WAAS,CAC3C4O,OAAQ,GACR8B,UAAW,GACXC,OAAQ,GACRC,MAAO,GACPC,oBAAqB,GACrB/B,SAAU,GACVgC,eAAgB,MAEXrI,EAAYsG,GAAiB/O,WAAS,KACtCyF,EAAWuJ,GAAgBhP,WAAS,WACpC0F,EAAeuJ,GAAoBjP,WAAS,QAC5C6I,EAAcqG,GAAmBlP,WAAS,CAC/C8Q,eAAgB,KAIZvL,EAAU,CACd,CACEqB,MAAO,SACPe,OAAQ,SACRF,UAAU,EACVK,WAAW,GAEb,CACElB,MAAO,YACPe,OAAQ,YACRF,UAAU,GAEZ,CACEb,MAAO,SACPe,OAAQ,SACRF,UAAU,EACVX,WAAY,KAEd,CACEF,MAAO,QACPe,OAAQ,QACRF,UAAU,GAEZ,CACEb,MAAO,sBACPe,OAAQ,YACRF,UAAU,GAEZ,CACEb,MAAO,WACPe,OAAQ,WACRF,UAAU,EACVX,WAAY,KAEd,CACEF,MAAO,cACPe,OAAQ,cACRF,UAAU,EACVd,OAAQ,CAAC0B,EAAG0I,aAEH,OAAA,OAAArD,EAAIqD,EAAA9M,oBAAe,EAAAyJ,EAAAkB,UACxB,OAAAoC,IAAaxG,MAAayG,GAAAA,EAAK1M,KAAOwM,EAAID,2BAAiBlC,SAAU,GAAA,IAMvEsC,EAAY9B,EAAAA,aAAYzM,UAC5B4L,GAAW,GACP,IAEF,MAAO4C,EAAqBC,SAA4BC,QAAQC,IAAI,CAClE9O,EAAwB+O,iBAAiB9L,EAAWC,GACpDlD,EAAwB8M,oBAItB6B,EAAoB/N,OACdmM,QAAAnM,MAAM,8BAA+B+N,EAAoB/N,OAClDH,EAAAkO,EAAoB/N,MAAO,SAAU,cACpDmN,EAAiB,MAEAA,EAAAY,EAAoB3L,MAAQ,IAGzC2L,EAAoB9B,WACtBG,EAAMjC,KAAK,oGAKX6D,EAAmBhO,OACbmM,QAAAnM,MAAM,iCAAkCgO,EAAmBhO,OACpDH,EAAAmO,EAAmBhO,MAAO,SAAU,iBACnDkL,EAAgB,MAEAA,EAAA8C,EAAmB5L,MAAQ,IAGvC4L,EAAmB/B,YAAc8B,EAAoB9B,WACvDG,EAAMjC,KAAK,mEAGRnK,GACCmM,QAAAnM,MAAM,oCAAqCA,GACpCH,EAAAG,EAAO,SAAU,SAChCmN,EAAiB,IACjBjC,EAAgB,GAAE,CAClB,QACAC,GAAW,EACb,IACC,CAAC9I,EAAWC,IAGfzF,EAAAA,WAAU,WAEP,CAACiR,IAGE,MAAAzB,EAAY,CAAC+B,EAAe,QAChCf,EAAuBe,GAGP/F,EADZ+F,EACY,CACZ5C,OAAQ4C,EAAa5C,OACrB8B,UAAWc,EAAad,UACxBC,OAAQa,EAAab,QAAU,GAC/BC,MAAOY,EAAaZ,MACpBC,oBAAqBW,EAAaX,oBAClC/B,SAAU0C,EAAa1C,UAAY,GACnCgC,eAAgBU,EAAaV,gBAGjB,CACZlC,OAAQ,GACR8B,UAAW,GACXC,OAAQ,GACRC,MAAO,GACPC,oBAAqB,GACrB/B,SAAU,GACVgC,eAAgBzC,EAAalN,OAAS,EAAIkN,EAAa,GAAG9J,GAAK,KAInEkK,GAAe,EAAI,EAIfkB,EAAa,KACjBlB,GAAe,EAAK,EA8DhBgD,EAAwBnB,EAAc1G,QAAuB4H,IAE3D,MAAAE,EACJF,EAAa5C,OAAOiB,cAAc7B,SAASvF,EAAWoH,gBACtD2B,EAAad,UAAUb,cAAc7B,SAASvF,EAAWoH,gBACxD2B,EAAab,QAAUa,EAAab,OAAOd,cAAc7B,SAASvF,EAAWoH,gBAC9E2B,EAAaZ,MAAMf,cAAc7B,SAASvF,EAAWoH,gBACrD2B,EAAaX,oBAAoBhB,cAAc7B,SAASvF,EAAWoH,gBAClE2B,EAAa1C,UAAY0C,EAAa1C,SAASe,cAAc7B,SAASvF,EAAWoH,eAG9E8B,GACH9I,EAAaiI,gBACdU,EAAaV,iBAAmBjI,EAAaiI,eAE/C,OAAOY,GAAiBC,CAAA,IAIpB/I,EAAU,CACd,CACErE,GAAI,iBACJ9D,KAAM,SACNsJ,MAAO,cACPN,YAAa,0BACbQ,QAASoE,EAAajN,KAAa6P,IAAA,CACjCpK,MAAOoK,EAAK1M,GACZwF,MAAOkH,EAAKrC,aAMZgD,EAAa,CACjB,CACErN,GAAI,SACJ9D,KAAM,OACNsJ,MAAO,SACPN,YAAa,WACbwC,WAAY,CACVC,UAAU,EACVE,gBAAiB,2BACjBM,UAAW,GACXC,iBAAkB,8CAGtB,CACEpI,GAAI,YACJ9D,KAAM,OACNsJ,MAAO,YACPN,YAAa,kBACbwC,WAAY,CACVC,UAAU,EACVE,gBAAiB,iCACjBM,UAAW,GACXC,iBAAkB,mDAGtB,CACEpI,GAAI,SACJ9D,KAAM,SACNsJ,MAAO,SACPN,YAAa,qBACbQ,QAAS,CACP,CAAEpD,MAAO,YAAakD,MAAO,aAC7B,CAAElD,MAAO,WAAYkD,MAAO,YAC5B,CAAElD,MAAO,OAAQkD,MAAO,QACxB,CAAElD,MAAO,kBAAmBkD,MAAO,oBAErCkC,WAAY,CACVC,UAAU,EACVE,gBAAiB,6BAGrB,CACE7H,GAAI,QACJ9D,KAAM,QACNsJ,MAAO,QACPN,YAAa,6BACbjF,WAAYgM,EACZjD,KAAMiD,EAAsB,8DAAgE,uCAC5FvE,WAAY,CACVC,UAAU,EACVE,gBAAiB,0BACjBC,QAAS,eACTE,eAAgB,4BAGpB,CACEhI,GAAI,sBACJ9D,KAAM,OACNsJ,MAAO,yBACPN,YAAa,iBACbwC,WAAY,CACVC,UAAU,EACVE,gBAAiB,2CACjBM,UAAW,GACXC,iBAAkB,iDAGtB,CACEpI,GAAI,WACJ9D,KAAM,MACNsJ,MAAO,WACPN,YAAa,mBACbwC,WAAY,CACVI,QAAS,kBACTE,eAAgB,iCAGpB,CACEhI,GAAI,iBACJ9D,KAAM,SACNsJ,MAAO,cACPN,YAAa,0BACbQ,QAASoE,EAAajN,KAAa6P,IAAA,CACjCpK,MAAOoK,EAAK1M,GACZwF,MAAOkH,EAAKrC,WAEd3C,WAAY,CACVC,UAAU,EACVE,gBAAiB,mCAyGrB,SAAApL,KAAC,MAAI,CAAAC,UAAU,YAEbtB,SAAA,CAAA6B,EAAAN,IAACsH,EAAA,CACCC,aACAC,eAAgBqG,EAChBpG,kBAAkB,uBAClBC,UACAC,eACAC,eA1RqB,CAACgH,EAAUjJ,KACpCqI,GAAyBnD,IAAA,IACpBA,EACH+D,CAACA,GAAWjJ,KACZ,EAuREkC,eAnRqB,KACTmG,EAAA,CACd4B,eAAgB,IACjB,EAiRG9H,SAAU,IAAMyG,IAChBxG,OAAQmF,EACRlF,cAAc,oBAIhB1H,EAAAN,IAACoE,EAAA,CACCC,UACAC,KAAMiM,EACNhM,YACAC,gBACAC,OAjTciB,IACdnB,IAAcmB,EACCqI,EAAkB,QAAlBvJ,EAA0B,OAAS,QAEpDsJ,EAAapI,GACbqI,EAAiB,OACnB,EA4SIrJ,UACAC,cAAeuI,EACftI,OAAQ2J,EACR1J,SA5RepD,MAAO4B,EAAIiN,KAC1B,GAACzB,OAAOC,QAAQ,iDAAiDwB,EAAa5C,UAAU4C,EAAad,iDAArG,CAIJnC,GAAW,GACP,IAEF,MAAMnL,MAAEA,EAAOiM,UAAAA,SAAoB7M,EAAwBqP,mBAAmBtN,GAE9E,GAAInB,EAEF,YADeH,EAAAG,EAAO,WAAY,aAKlCL,EADEsM,EACe,wFAEA,gDAMZjM,GACCmM,QAAAnM,MAAM,+BAAgCA,GAC/BH,EAAAG,EAAO,WAAY,YAAW,CAC7C,QACAmL,GAAW,EACb,CA1BA,CA0BA,EAgQIvI,cAAgBzB,GAAqB,iBAAPA,GAAmBA,EAAGmG,WAAW,SAC/DxE,aAAa,2CACbC,aAAc,CAAEC,KAAM,mBAAoBC,OAAQ,wBAIpD7E,EAAAN,IAAC4J,EAAA,CACCC,OAAQyD,EACRxD,QAAS2E,EACTlL,MAAO+L,EAAsB,mBAAqB,kBAClDvF,OAAQ2G,EACR1G,cAAeM,EACfL,SAzIexI,MAAO4G,IAC1BgF,GAAW,GACP,IACF,MAAMuD,EAAmB,CACvBlD,OAAQrF,EAAOqF,OACf8B,UAAWnH,EAAOmH,UAClBC,OAAQpH,EAAOoH,OACfC,MAAOrH,EAAOqH,MACdC,oBAAqBtH,EAAOsH,oBAC5B/B,SAAUvF,EAAOuF,SACjBgC,eAAgBvH,EAAOuH,gBAGzB,GAAIN,EAAqB,CAEjB,MAAAuB,EAAe,IAAKD,UACnBC,EAAanB,MAEd,MAAAxN,MAAEA,YAAOiM,SAAoB7M,EAAwBwP,mBAAmBxB,EAAoBjM,GAAIwN,GAEtG,GAAI3O,EAEF,YADeH,EAAAG,EAAO,aAAc,aAKpCL,EADEsM,EACe,0FAEA,uCACnB,MAGI,IAEME,QAAA0C,IAAI,gCAAiC1I,EAAOqH,OAC9C,MAAEpL,KAAM0M,EAAU9O,MAAO+O,SAAoBC,EAASC,KAAKC,OAAO,CACtE1B,MAAOrH,EAAOqH,MACd2B,SAAU,eACVtI,QAAS,CACPzE,KAAM,CACJgN,IAAK,YACLC,gBAAiB,GAAGlJ,EAAOqF,UAAUrF,EAAOmH,gBAKlD,GAAIyB,EAQF,OAPQ5C,QAAAnM,MAAM,kCAAmC+O,QAE7CA,EAAUjP,QAAQ8K,SAAS,2BAC7B/K,EAAe,CAAEC,QAAS,mDAAqD,QAAS,WAEzED,EAAAkP,EAAW,QAAS,YAKnC,IAACD,EAASQ,KACN,MAAA,IAAIC,MAAM,wDAIlB,MAAMC,EAAsB,IACvBd,EACHe,WAAYX,EAASQ,KAAKnO,KAGpBnB,MAAO0P,EAAazD,UAAAA,SAAoB7M,EAAwBuQ,mBAAmBH,GAE3F,GAAIE,EAGF,OAFQvD,QAAAnM,MAAM,wCAAyC0P,QACxC7P,EAAA6P,EAAa,UAAW,aAKvC/P,EADEsM,EACe,mFAEA,yCAEZ8C,GAGP,OAFQ5C,QAAAnM,MAAM,wDAAyD+O,QACxDlP,EAAAkP,EAAW,QAAS,YAErC,eAMK/O,GACCmM,QAAAnM,MAAM,+BAAgCA,GAC9CH,EAAeG,EAAOoN,EAAsB,aAAe,QAAS,YAAW,CAC/E,QACAjC,GAAW,EACb,GA2CI3I,UACAwF,WAAW,UACXG,SAAUiF,EACVvI,KAAK,SAET,ECreE+K,EAAc,EAAG5E,cACrB,MAAO6E,EAAUC,GAAelT,EAAAA,SAAS,KAClCqO,EAAcC,GAAmBtO,EAAAA,SAAS,KAC1CsQ,EAAeC,GAAoBvQ,EAAAA,SAAS,KAC5C4F,EAAS2I,GAAcvO,YAAS,IAChCwO,EAAaC,GAAkBzO,YAAS,IACxCmT,EAAgBC,GAAqBpT,WAAS,OAC9CwL,EAAYC,GAAiBzL,WAAS,CAC3C4O,OAAQ,GACR8B,UAAW,GACX2C,iBAAkB,GAClB1C,OAAQ,GACRE,oBAAqB,GACrBD,MAAO,GACP9B,SAAU,GACVgC,eAAgB,GAChBwC,aAAc,GACdC,MAAO,MAEF9K,EAAYsG,GAAiB/O,WAAS,KACtCyF,EAAWuJ,GAAgBhP,WAAS,WACpC0F,EAAeuJ,GAAoBjP,WAAS,QAC5C6I,EAAcqG,GAAmBlP,WAAS,CAC/C8Q,eAAgB,GAChBH,OAAQ,GACR2C,aAAc,GACdE,SAAU,GACVC,SAAU,KAINlO,EAAU,CACd,CACEqB,MAAO,SACPe,OAAQ,SACRF,UAAU,EACVK,WAAW,GAEb,CACElB,MAAO,YACPe,OAAQ,YACRF,UAAU,GAEZ,CACEb,MAAO,OACPe,OAAQ,OACRF,UAAU,EACVX,WAAY,IACZrG,KAAM,WAER,CACEmG,MAAO,SACPe,OAAQ,SACRF,UAAU,GAEZ,CACEb,MAAO,sBACPe,OAAQ,YACRF,UAAU,EACVX,WAAY,KAEd,CACEF,MAAO,QACPe,OAAQ,QACRF,UAAU,EACVX,WAAY,KAEd,CACEF,MAAO,WACPe,OAAQ,WACRF,UAAU,EACVX,WAAY,KAEd,CACEF,MAAO,cACPe,OAAQ,cACRF,UAAU,EACVd,OAAQ,CAAC0B,EAAG0I,aACH,OAAA,OAAArD,EAAIqD,EAAA9M,oBAAe,EAAAyJ,EAAAkB,UACxB,OAAAoC,IAAaxG,MAAayG,GAAAA,EAAK1M,KAAOwM,EAAID,2BAAiBlC,SAAU,GAAA,GAG3E,CACEhI,MAAO,YACPe,OAAQ,YACRF,UAAU,EACVd,OAAQ,CAAC0B,EAAG0I,KACV,IAAKA,EAAIuC,aAAqB,MAAA,IACxB,MAAAI,EAAQpD,EAAc9F,SAAUmJ,EAAEpP,KAAOwM,EAAIuC,eACnD,OAAOI,EAAQ,GAAGA,EAAM9E,UAAU8E,EAAMhD,YAAc,GAAA,IAMtDQ,EAAY9B,EAAAA,aAAYzM,UAC5B4L,GAAW,GACP,IAEF,MAAOqF,EAAgBxC,EAAoBD,SAA6BE,QAAQC,IAAI,CAClF9O,EAAwBqR,YAAYpO,EAAWC,GAC/ClD,EAAwB8M,kBACxB9M,EAAwB+O,qBAItBqC,EAAexQ,OACTmM,QAAAnM,MAAM,6BAA8BwQ,EAAexQ,OAC5CH,EAAA2Q,EAAexQ,MAAO,SAAU,aAC/C8P,EAAY,MAEAA,EAAAU,EAAepO,MAAQ,IAG/BoO,EAAevE,WACjBG,EAAMjC,KAAK,mGAKX6D,EAAmBhO,OACbmM,QAAAnM,MAAM,iCAAkCgO,EAAmBhO,OACpDH,EAAAmO,EAAmBhO,MAAO,SAAU,iBACnDkL,EAAgB,KAEAA,EAAA8C,EAAmB5L,MAAQ,IAIzC2L,EAAoB/N,OACdmM,QAAAnM,MAAM,8BAA+B+N,EAAoB/N,OAClDH,EAAAkO,EAAoB/N,MAAO,SAAU,cACpDmN,EAAiB,KAEAA,EAAAY,EAAoB3L,MAAQ,UAGxCpC,GACCmM,QAAAnM,MAAM,oCAAqCA,GACpCH,EAAAG,EAAO,SAAU,SAChC8P,EAAY,IACZ5E,EAAgB,IAChBiC,EAAiB,GAAE,CACnB,QACAhC,GAAW,EACb,IACC,CAAC9I,EAAWC,IAGfzF,EAAAA,WAAU,WAEP,CAACiR,IAGE,MAgBAzB,EAAY,CAACqE,EAAU,QAC3BV,EAAkBU,GAGFrI,EADZqI,EACY,CACZlF,OAAQkF,EAAQlF,OAChB8B,UAAWoD,EAAQpD,WAAa,GAChC2C,iBAAkBS,EAAQT,iBAC1B1C,OAAQmD,EAAQnD,OAChBE,oBAAqBiD,EAAQjD,qBAAuB,GACpDD,MAAOkD,EAAQlD,OAAS,GACxB9B,SAAUgF,EAAQhF,UAAY,GAC9BgC,eAAgBgD,EAAQhD,eACxBwC,aAAcQ,EAAQR,cAAgB,GACtCC,MAAOO,EAAQP,OAAS,IAGZ,CACZ3E,OAAQ,GACR8B,UAAW,GACX2C,iBAAkB,GAClB1C,OAAQ,GACRE,oBAAqB,GACrBD,MAAO,GACP9B,SAAU,GACVgC,eAAgBzC,EAAalN,OAAS,EAAIkN,EAAa,GAAG9J,GAAK,GAC/D+O,aAAc,GACdC,MAAO,KAIX9E,GAAe,EAAI,EAIfkB,EAAa,KACjBlB,GAAe,EAAK,EAkEhBsF,EAAmBd,EAASrJ,QAAkBkK,IAElD,MAAMpC,EACJoC,EAAQlF,OAAOiB,cAAc7B,SAASvF,EAAWoH,gBAChDiE,EAAQpD,WAAaoD,EAAQpD,UAAUb,cAAc7B,SAASvF,EAAWoH,gBACzEiE,EAAQjD,qBAAuBiD,EAAQjD,oBAAoBhB,cAAc7B,SAASvF,EAAWoH,gBAC7FiE,EAAQlD,OAASkD,EAAQlD,MAAMf,cAAc7B,SAASvF,EAAWoH,gBACjEiE,EAAQhF,UAAYgF,EAAQhF,SAASe,cAAc7B,SAASvF,EAAWoH,gBACvEiE,EAAQP,OAASO,EAAQP,MAAM1D,cAAc7B,SAASvF,EAAWoH,eAG9D8B,GACH9I,EAAaiI,gBAAkBgD,EAAQhD,iBAAmBjI,EAAaiI,eAGpEkD,GACHnL,EAAa8H,QAAUmD,EAAQnD,SAAW9H,EAAa8H,OAGpDsD,GACHpL,EAAayK,eACiB,SAA9BzK,EAAayK,cAA2BQ,EAAQR,aAAeQ,EAAQR,eAAiBzK,EAAayK,cAGlGY,GACHrL,EAAa2K,UAAaM,EAAQK,MAAQL,EAAQK,MAAQC,SAASvL,EAAa2K,UAG7Ea,GACHxL,EAAa4K,UAAaK,EAAQK,MAAQL,EAAQK,MAAQC,SAASvL,EAAa4K,UAGnF,OAAO/B,GAAiBC,GAAsBqC,GACvCC,GAAuBC,GAAiBG,CAAA,IAI3CzL,EAAU,CACd,CACErE,GAAI,iBACJ9D,KAAM,SACNsJ,MAAO,cACPN,YAAa,0BACbQ,QAASoE,EAAajN,KAAa6P,IAAA,CACjCpK,MAAOoK,EAAK1M,GACZwF,MAAOkH,EAAKrC,YAGhB,CACErK,GAAI,SACJ9D,KAAM,SACNsJ,MAAO,SACPN,YAAa,oBACbQ,QAAS,CACP,CAAEpD,MAAO,YAAakD,MAAO,aAC7B,CAAElD,MAAO,WAAYkD,MAAO,cAGhC,CACExF,GAAI,eACJ9D,KAAM,SACNsJ,MAAO,YACPN,YAAa,uBACbQ,QAAS,CACP,CAAEpD,MAAO,OAAQkD,MAAO,6BACrBuG,EAAclP,KAAcsS,IAAA,CAC7B7M,MAAO6M,EAAMnP,GACbwF,MAAO,GAAG2J,EAAM9E,UAAU8E,EAAMhD,kBAItC,CACEnM,GAAI,OACJ9D,KAAM,QACNsJ,MAAO,OACPI,eAAgB,SAChBE,eAAgB,SAChBnC,IAAK,EACLkC,IAAK,MAKHwH,EAAa,CACjB,CACErN,GAAI,SACJ9D,KAAM,OACNsJ,MAAO,SACPN,YAAa,WACbwC,WAAY,CACVC,UAAU,EACVE,gBAAiB,2BACjBM,UAAW,GACXC,iBAAkB,8CAGtB,CACEpI,GAAI,YACJ9D,KAAM,OACNsJ,MAAO,YACPN,YAAa,kBACbwC,WAAY,CACVC,UAAU,EACVE,gBAAiB,iCACjBM,UAAW,GACXC,iBAAkB,mDAGtB,CACEpI,GAAI,mBACJ9D,KAAM,OACNsJ,MAAO,sBACPkC,WAAY,CACVC,UAAU,EACVE,gBAAiB,0CAGrB,CACE7H,GAAI,SACJ9D,KAAM,SACNsJ,MAAO,SACPN,YAAa,qBACbQ,QAAS,CACP,CAAEpD,MAAO,YAAakD,MAAO,aAC7B,CAAElD,MAAO,WAAYkD,MAAO,YAC5B,CAAElD,MAAO,OAAQkD,MAAO,QACxB,CAAElD,MAAO,kBAAmBkD,MAAO,oBAErCkC,WAAY,CACVC,UAAU,EACVE,gBAAiB,6BAGrB,CACE7H,GAAI,sBACJ9D,KAAM,OACNsJ,MAAO,yBACPN,YAAa,iBACbwC,WAAY,CACVC,UAAU,EACVE,gBAAiB,2CACjBM,UAAW,GACXC,iBAAkB,iDAGtB,CACEpI,GAAI,QACJ9D,KAAM,QACNsJ,MAAO,QACPN,YAAa,6BACbwC,WAAY,CACVI,QAAS,eACTE,eAAgB,4BAGpB,CACEhI,GAAI,WACJ9D,KAAM,MACNsJ,MAAO,WACPN,YAAa,mBACbwC,WAAY,CACVI,QAAS,kBACTE,eAAgB,iCAGpB,CACEhI,GAAI,iBACJ9D,KAAM,SACNsJ,MAAO,cACPN,YAAa,0BACbQ,QAASoE,EAAajN,KAAa6P,IAAA,CACjCpK,MAAOoK,EAAK1M,GACZwF,MAAOkH,EAAKrC,WAEd3C,WAAY,CACVC,UAAU,EACVE,gBAAiB,kCAGrB,CACE7H,GAAI,eACJ9D,KAAM,SACNsJ,MAAO,qBACPN,YAAa,yBACbQ,QAASqG,EAAclP,KAAcsS,IAAA,CACnC7M,MAAO6M,EAAMnP,GACbwF,MAAO,GAAG2J,EAAM9E,UAAU8E,EAAMhD,iBAGpC,CACEnM,GAAI,QACJ9D,KAAM,WACNsJ,MAAO,QACPN,YAAa,0CACbmE,KAAM,IAqER,SAAA5M,KAAC,MAAI,CAAAC,UAAU,YAEbtB,SAAA,CAAA6B,EAAAN,IAACsH,EAAA,CACCC,aACAC,eAAgBqG,EAChBpG,kBAAkB,sBAClBC,UACAC,eACAC,eAnUqB,CAACgH,EAAUjJ,KACpCqI,GAAyBnD,IAAA,IACpBA,EACH+D,CAACA,GAAWjJ,KACZ,EAgUEkC,eA5TqB,KACTmG,EAAA,CACd4B,eAAgB,GAChBH,OAAQ,GACR2C,aAAc,GACdE,SAAU,GACVC,SAAU,IACX,EAsTGzK,SAAU,IAAMyG,IAChBxG,OAAQmF,EACRlF,cAAc,mBAIhB1H,EAAAN,IAACoE,EAAA,CACCC,UACAC,KAAMuO,EACNtO,YACAC,gBACAC,OA1VciB,IACdnB,IAAcmB,EACCqI,EAAkB,QAAlBvJ,EAA0B,OAAS,QAEpDsJ,EAAapI,GACbqI,EAAiB,OACnB,EAqVIrJ,UACAC,cAAeuI,EACftI,OAAQ2J,EACR1J,SAjUepD,MAAO4B,EAAIuP,KAC9B,GAAK/D,OAAOC,QAAQ,gDAAgD8D,EAAQlF,8CAA5E,CAIAL,GAAW,GACP,IAEF,MAAMnL,MAAEA,EAAOiM,UAAAA,SAAoB7M,EAAwB8R,cAAc/P,GAEzE,GAAInB,EAEF,YADeH,EAAAG,EAAO,WAAY,YAKjBL,EADfsM,EACe,aAAayE,EAAQlF,sFAErB,aAAakF,EAAQlF,8CAMjCxL,GACCmM,QAAAnM,MAAM,8BAA+BA,GAC9BH,EAAAG,EAAO,WAAY,WAAU,CAC5C,QACAmL,GAAW,EACb,CA1BA,CA0BA,EAqSIvI,cAAgBzB,GAAqB,iBAAPA,GAAmBA,EAAGmG,WAAW,SAC/DxE,aAAa,0CACbC,aAAc,CAAEC,KAAM,kBAAmBC,OAAQ,uBAInD7E,EAAAN,IAAC4J,EAAA,CACCC,OAAQyD,EACRxD,QAAS2E,EACTlL,MAAO0O,EAAiB,kBAAoB,iBAC5ClI,OAAQ2G,EACR1G,cAAeM,EACfL,SAtGexI,MAAO4G,IAC1BgF,GAAW,GACP,IAEI,MAAA4F,EAjUW,CAACI,IACpB,IAAKA,EAAkB,OAAA,KAEjB,MAAAC,MAAYjR,KACZkR,EAAQ,IAAIlR,KAAKgR,GACvB,IAAIG,EAAMF,EAAMG,cAAgBF,EAAME,cACtC,MAAMC,EAAYJ,EAAMK,WAAaJ,EAAMI,WAMpC,OAJHD,EAAY,GAAoB,IAAdA,GAAmBJ,EAAMM,UAAYL,EAAMK,YAC/DJ,IAGKA,CAAA,EAqTQK,CAAaxL,EAAO8J,kBAG3B2B,EAAc,CAClBpG,OAAQrF,EAAOqF,OACf8B,UAAWnH,EAAOmH,UAClB2C,iBAAkB9J,EAAO8J,iBACzB1C,OAAQpH,EAAOoH,OACfE,oBAAqBtH,EAAOsH,oBAC5BD,MAAOrH,EAAOqH,MACd9B,SAAUvF,EAAOuF,SACjBgC,eAAgBvH,EAAOuH,eACvBwC,aAAc/J,EAAO+J,cAAgB,KACrCC,MAAOhK,EAAOgK,MACdY,QAGF,GAAIhB,EAAgB,CAEZ,MAAA/P,MAAEA,YAAOiM,SAAoB7M,EAAwByS,cAAc9B,EAAe5O,GAAIyQ,GAE5F,GAAI5R,EAEF,YADeH,EAAAG,EAAO,aAAc,YAKnBL,EADfsM,EACe,aAAa9F,EAAOqF,wFAEpB,aAAarF,EAAOqF,qCACvC,KACK,CAEL,MAAMxL,MAAEA,EAAOiM,UAAAA,SAAoB7M,EAAwB0S,cAAcF,GAEzE,GAAI5R,EAEF,YADeH,EAAAG,EAAO,QAAS,YAKdL,EADfsM,EACe,aAAa9F,EAAOqF,iFAEpB,aAAarF,EAAOqF,gCAEzC,eAMOxL,GACCmM,QAAAnM,MAAM,6BAA8BA,GAC5CH,EAAeG,EAAO+P,EAAiB,aAAe,QAAS,WAAU,CACzE,QACA5E,GAAW,EACb,GA2CI3I,UACAwF,WAAW,UACXG,SAAU4H,EACVlL,KAAK,SAET,ECnkBEkN,EAAiB,KACrB,MAAOvV,EAAWwV,GAAgBpV,WAAS,IACpCqV,EAAUC,GAAetV,YAAS,GACnC0S,EAAO6C,GAAqBC,GAAAA,EAAMnD,KAAKK,OAEvCtE,EAAyB,WAAT,MAANsE,OAAM,EAAAA,EAAA+C,OAAmC,mBAAf,MAAA/C,OAAA,EAAAA,EAAM+C,QAA4B,EAG1E,SAAAzU,KAAC,MAAI,CAAAC,UAAU,8CAEbtB,SAAA,GAACqB,KAAA,SAAA,CAAOC,UAAU,OAChBtB,SAAA,GAACqB,KAAA,MAAA,CAAIC,UAAU,oEACbtB,SAAA,QAAC,MACC,CAAAA,SAAA,CAACuB,EAAAA,IAAA,KAAA,CAAGD,UAAU,wCAAwCtB,SAAuB,4BAC5EuB,EAAAA,IAAA,IAAA,CAAED,UAAU,gBAAgBtB,SAE7B,2DAEFqB,KAAC,MAAI,CAAAC,UAAU,iCACbtB,SAAA,CAAA6B,EAAAN,IAAC,SAAA,CACCI,QAAS,IAAMgU,GAAaD,GAC5BpU,UAAU,8EACVwD,MAAM,sBAEN9E,eAAC,OAAK,CAAA+V,MAAO,CAAEC,SAAU,WAAahW,SAAE,eAEzC8B,EAAmB,UAKvB4T,KACCnU,IAAC0U,EAAK,CAAA3U,UAAU,kCACdtB,SAAAuB,EAAAA,IAAC2U,EAAS,CAAA5U,UAAU,OAClBtB,SAAAqB,EAAAA,KAAC,MAAI,CAAAC,UAAU,wBACbtB,SAAA,CAACuB,EAAAA,IAAA,KAAA,CAAGD,UAAU,6BAA6BtB,SAAgC,qCAC1EuB,EAAAA,IAAA,IAAA,CAAED,UAAU,OAAOtB,SAAoE,2EACxFqB,KAAC,KAAG,CAAAC,UAAU,2BACZtB,SAAA,QAAC,KAAG,CAAAA,SAAA,GAAAuB,IAAC,UAAOvB,SAAc,mBAAS,iEAClC,KAAG,CAAAA,SAAA,GAAAuB,IAAC,UAAOvB,SAAW,gBAAS,wEAC/B,KAAG,CAAAA,SAAA,GAAAuB,IAAC,UAAOvB,SAAU,eAAS,qEAEjCqB,KAAC,IAAE,CAAAC,UAAU,qBACXtB,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,cAActB,SAAK,UAAO,2GAWtDqB,KAAC4U,EAAK,CAAA3U,UAAU,4BACdtB,SAAA,CAAAuB,EAAAA,IAAC4U,GAAW7U,UAAU,8CACpBtB,SAACqB,EAAAA,KAAA,MAAA,CAAIC,UAAU,oCACbtB,SAAA,GAACqB,KAAA,KAAA,CAAGC,UAAU,sDACZtB,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,qBAAqBtB,SAAE,OAAO,sCAG/C,MAAI,CAAAsB,UAAU,8BACZtB,SAAAyO,QACE,OAAK,CAAAnN,UAAU,yEAAyEtB,SAAA,oBAIxF6B,EAAAN,IAAA,OAAA,CAAKD,UAAU,2EAA2EtB,oCAOnGuB,EAAAA,IAAC2U,GAAS5U,UAAU,MAClBtB,gBAACD,EAAK,CAAAE,YAAsBC,SAAUuV,EACpCzV,SAAA,GAACqB,KAAAN,EAAA,CAAQO,UAAU,uBACjBtB,SAAA,CAAAuB,MAACK,EACC,CAAA5B,SAAAqB,EAAAA,KAAC,MAAI,CAAAC,UAAU,oBACbtB,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,OAAOtB,SAAE,OAAO,qBAInCuB,MAAAK,EAAA,CACC5B,SAACqB,EAAAA,KAAA,MAAA,CAAIC,UAAU,oBACbtB,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,OAAOtB,SAAK,UAAO,kBAItCuB,MAAAK,EAAA,CACC5B,SAACqB,EAAAA,KAAA,MAAA,CAAIC,UAAU,oBACbtB,SAAA,CAACuB,EAAAA,IAAA,OAAA,CAAKD,UAAU,OAAOtB,SAAE,OAAO,sBAMtCqB,KAAC,MAAI,CAAAC,UAAU,MACbtB,SAAA,OAACmB,EACC,CAAAnB,SAAAuB,EAAAA,IAACiN,EAAgB,CAAAC,oBAElBtN,EAAA,CACCnB,SAACuB,EAAAA,IAAAmP,EAAA,CAAiBjC,oBAEnBtN,EAAA,CACCnB,SAACuB,EAAAA,IAAA8R,EAAA,CAAY5E,2BAMzB"}