import{j as e}from"./index-165d7974.js";import{C as s,a,b as t}from"./Card-54419bd4.js";import{B as n}from"./Button-9c521291.js";const r=()=>e.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Test de Atención"}),e.jsx("p",{className:"text-gray-600",children:"Esta sección está en desarrollo."})]}),e.jsxs(s,{children:[e.jsx(a,{children:e.jsx("h2",{className:"text-lg font-medium",children:"Información"})}),e.jsxs(t,{children:[e.jsx("p",{className:"text-gray-700 mb-4",children:"El test de atención evalúa la capacidad para mantener la concentración y detectar estímulos relevantes. Este test se encuentra actualmente en desarrollo."}),e.jsx(n,{onClick:()=>window.history.back(),children:"Volver"})]})]})]});export{r as default};
//# sourceMappingURL=Atencion-a2c746e4.js.map
