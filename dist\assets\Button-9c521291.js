import{L as e,j as r}from"./index-165d7974.js";const s=({children:s,variant:t="primary",size:o="md",className:n="",disabled:i=!1,as:a="button",to:g,...c})=>{const d=`inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm",secondary:"bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-400 shadow-sm",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-400 shadow-sm",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm"}[t]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[o]} ${i?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${n}`;if(a===e||"Link"===a&&g)return r.jsx(e,{to:g,className:d,...c,children:s});if("function"==typeof a){const e=a;return r.jsx(e,{className:d,...c,children:s})}return r.jsx("button",{className:d,disabled:i,...c,children:s})};export{s as B};
//# sourceMappingURL=Button-9c521291.js.map
