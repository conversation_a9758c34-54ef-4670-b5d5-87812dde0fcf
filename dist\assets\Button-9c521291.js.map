{"version": 3, "file": "Button-9c521291.js", "sources": ["../../src/components/ui/Button.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nexport const Button = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  disabled = false,\n  as = 'button',\n  to,\n  ...props\n}) => {\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';\n\n  const variantStyles = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm',\n    secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-400 shadow-sm',\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-400 shadow-sm',\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm',\n    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm',\n  };\n\n  const sizeStyles = {\n    sm: 'text-xs px-3 py-1.5',\n    md: 'text-sm px-4 py-2',\n    lg: 'text-base px-6 py-3',\n  };\n\n  const disabledStyles = disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';\n\n  const combinedClassName = `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${disabledStyles} ${className}`;\n\n  // Si 'as' es Link, renderizar un componente Link de react-router-dom\n  if (as === Link || (as === 'Link' && to)) {\n    return (\n      <Link\n        to={to}\n        className={combinedClassName}\n        {...props}\n      >\n        {children}\n      </Link>\n    );\n  }\n\n  // Si 'as' es un componente personalizado\n  if (typeof as === 'function') {\n    const Component = as;\n    return (\n      <Component\n        className={combinedClassName}\n        {...props}\n      >\n        {children}\n      </Component>\n    );\n  }\n\n  // Por defecto, renderizar un botón\n  return (\n    <button\n      className={combinedClassName}\n      disabled={disabled}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n"], "names": ["<PERSON><PERSON>", "children", "variant", "size", "className", "disabled", "as", "to", "props", "combinedClassName", "primary", "secondary", "outline", "danger", "success", "sm", "md", "lg", "Link", "jsxRuntimeExports", "jsx", "Component"], "mappings": "+CAGO,MAAMA,EAAS,EACpBC,WACAC,UAAU,UACVC,OAAO,KACPC,YAAY,GACZC,YAAW,EACXC,KAAK,SACLC,QACGC,MAEH,MAkBMC,EAAoB,wIAhBJ,CACpBC,QAAS,yEACTC,UAAW,4EACXC,QAAS,+FACTC,OAAQ,sEACRC,QAAS,6EAW8CZ,MARtC,CACjBa,GAAI,sBACJC,GAAI,oBACJC,GAAI,uBAK0Ed,MAFzDE,EAAW,gCAAkC,oBAEuCD,IAG3G,GAAIE,IAAOY,GAAgB,SAAPZ,GAAiBC,EAEjC,OAAAY,EAAAC,IAACF,EAAA,CACCX,KACAH,UAAWK,KACPD,EAEHP,aAMH,GAAc,mBAAPK,EAAmB,CAC5B,MAAMe,EAAYf,EAEhB,OAAAa,EAAAC,IAACC,EAAA,CACCjB,UAAWK,KACPD,EAEHP,YAGP,CAIE,OAAAkB,EAAAC,IAAC,SAAA,CACChB,UAAWK,EACXJ,cACIG,EAEHP,YAAA"}