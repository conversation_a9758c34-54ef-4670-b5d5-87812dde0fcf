import{j as e,r as a}from"./index-165d7974.js";import{C as s,a as l,b as t}from"./Card-54419bd4.js";import{B as r}from"./Button-9c521291.js";import{u as i}from"./useToast-cda9a5e1.js";const n=({type:a="text",name:s,value:l,onChange:t,placeholder:r,disabled:i=!1,readOnly:n=!1,error:o,className:d="",...c})=>{const m=`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${o?"border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${i?"bg-gray-100 cursor-not-allowed":""} ${d}`;return e.jsx("input",{type:a,name:s,value:l,onChange:t,placeholder:r,disabled:i,readOnly:n,className:m,"aria-invalid":o?"true":"false",...c})},o=({options:s=[],value:l,onChange:t,placeholder:r="Seleccionar...",displayKey:i="label",valueKey:n="value",name:o,disabled:d=!1,error:c,className:m="",...x})=>{const[u,h]=a.useState(!1),g=a.useRef(null),p=s.find((e=>e[n]===l)),f=p?p[i]:r;a.useEffect((()=>{const e=e=>{g.current&&!g.current.contains(e.target)&&h(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[]);const b=`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${c?"border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${d?"bg-gray-100 cursor-not-allowed":"cursor-pointer"} ${m}`;return e.jsxs("div",{className:"relative",ref:g,children:[e.jsx("div",{className:b,onClick:()=>{d||h(!u)},tabIndex:0,role:"button","aria-haspopup":"listbox","aria-expanded":u,...x,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:p?"":"text-gray-500",children:f}),e.jsx("svg",{className:"w-5 h-5 text-gray-400 transition-transform "+(u?"transform rotate-180":""),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]})}),u&&e.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md max-h-60 overflow-auto border border-gray-200",children:e.jsxs("ul",{className:"py-1",role:"listbox",children:[s.map(((a,s)=>e.jsx("li",{className:"px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 "+(a[n]===l?"bg-primary-50 text-primary-700":""),onClick:()=>(e=>{t(e[n],o),h(!1)})(a),role:"option","aria-selected":a[n]===l,children:a[i]},s))),0===s.length&&e.jsx("li",{className:"px-3 py-2 text-sm text-gray-500",children:"No hay opciones disponibles"})]})})]})},d=({data:s=[],columns:l=[],pagination:t=null,searchable:r=!1,className:i="",...o})=>{const[d,c]=a.useState(1),[m,x]=a.useState(""),[u,h]=a.useState(s),g=(null==t?void 0:t.pageSize)||10,p=Math.ceil(u.length/g);a.useEffect((()=>{if(r&&m){const e=s.filter((e=>l.some((a=>{if(!a.accessor)return!1;const s=e[a.accessor];return null!=s&&String(s).toLowerCase().includes(m.toLowerCase())}))));h(e),c(1)}else h(s)}),[m,s,l,r]);const f=t?u.slice((d-1)*g,d*g):u,b=e=>{c(e)};return e.jsxs("div",{className:"overflow-hidden",children:[r&&e.jsx("div",{className:"mb-4",children:e.jsx(n,{type:"text",placeholder:"Buscar...",value:m,onChange:e=>x(e.target.value),className:"max-w-xs"})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:`min-w-full divide-y divide-gray-200 ${i}`,...o,children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:l.map(((a,s)=>e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:a.header},s)))})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:f.length>0?f.map(((a,s)=>e.jsx("tr",{className:"hover:bg-gray-50",children:l.map(((s,l)=>e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:s.cell?s.cell({value:a[s.accessor],row:a}):a[s.accessor]},l)))},s))):e.jsx("tr",{children:e.jsx("td",{colSpan:l.length,className:"px-6 py-4 text-center text-sm text-gray-500",children:"No hay datos disponibles"})})})]})}),t&&p>1&&e.jsxs("nav",{className:"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4",children:[e.jsx("div",{className:"hidden sm:block",children:e.jsxs("p",{className:"text-sm text-gray-700",children:["Mostrando ",e.jsx("span",{className:"font-medium",children:(d-1)*g+1})," ","a"," ",e.jsx("span",{className:"font-medium",children:Math.min(d*g,u.length)})," ","de ",e.jsx("span",{className:"font-medium",children:u.length})," resultados"]})}),e.jsxs("div",{className:"flex-1 flex justify-between sm:justify-end",children:[e.jsx("button",{onClick:()=>b(d-1),disabled:1===d,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md "+(1===d?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50"),children:"Anterior"}),e.jsx("div",{className:"hidden md:flex mx-2",children:[...Array(p)].map(((a,s)=>e.jsx("button",{onClick:()=>b(s+1),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+(d===s+1?"bg-primary-50 border-primary-500 text-primary-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:s+1},s)))}),e.jsx("button",{onClick:()=>b(d+1),disabled:d===p,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md "+(d===p?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50"),children:"Siguiente"})]})]})]})},c=()=>{const[c,m]=a.useState([]),[x,u]=a.useState(!1),[h,g]=a.useState(!1),[p,f]=a.useState(null),[b,j]=a.useState({id:null,firstName:"",lastName:"",email:"",gender:"",age:"",phone:"",education:"",position:""}),{showSuccess:N,showError:v,showInfo:y}=i();a.useEffect((()=>{u(!0),setTimeout((()=>{m([{id:1,firstName:"Juan",lastName:"Pérez",email:"<EMAIL>",gender:"male",age:28,phone:"+591 72345678",education:"Licenciatura en Psicología",position:"Psicólogo Clínico"},{id:2,firstName:"María",lastName:"González",email:"<EMAIL>",gender:"female",age:32,phone:"+591 73456789",education:"Maestría en Recursos Humanos",position:"Especialista en RRHH"},{id:3,firstName:"Carlos",lastName:"Rodríguez",email:"<EMAIL>",gender:"male",age:25,phone:"+591 74567890",education:"Ingeniería en Sistemas",position:"Desarrollador Web"}]),u(!1)}),800)}),[]);const w=e=>{const{name:a,value:s}=e.target;j({...b,[a]:s})},C=()=>{j({id:null,firstName:"",lastName:"",email:"",gender:"",age:"",phone:"",education:"",position:""}),f(null)},k=(e=null)=>{e?(j({id:e.id,firstName:e.firstName,lastName:e.lastName,email:e.email,gender:e.gender,age:e.age.toString(),phone:e.phone,education:e.education,position:e.position}),f(e)):C(),g(!0)},E=()=>{g(!1),C()},S=[{header:"",accessor:"gender",cell:({value:a})=>e.jsx("div",{className:"flex justify-center",children:"male"===a?e.jsx("div",{className:"w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center",children:e.jsx("i",{className:"fas fa-mars text-blue-600"})}):"female"===a?e.jsx("div",{className:"w-8 h-8 rounded-lg bg-pink-100 flex items-center justify-center",children:e.jsx("i",{className:"fas fa-venus text-pink-600"})}):e.jsx("div",{className:"w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center",children:e.jsx("i",{className:"fas fa-genderless text-purple-600"})})})},{header:"Nombre",accessor:"firstName",cell:({value:a,row:s})=>e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium text-gray-900",children:[a," ",s.lastName]}),e.jsx("div",{className:"text-xs text-gray-500",children:s.position||"Sin cargo asignado"})]})},{header:"Email",accessor:"email",cell:({value:a})=>e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-900",children:a}),e.jsxs("div",{className:"text-xs text-gray-500",children:[e.jsx("i",{className:"fas fa-envelope mr-1"})," Contacto principal"]})]})},{header:"Teléfono",accessor:"phone"},{header:"Edad",accessor:"age",cell:({value:a})=>e.jsx("div",{className:"text-center bg-gray-100 rounded-lg py-1 px-2 w-12",children:a})},{header:"Formación",accessor:"education"},{header:"Acciones",accessor:"id",cell:({value:a,row:s})=>e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>k(s),className:"text-blue-600 hover:text-blue-800 bg-blue-100 p-2 rounded-lg focus:outline-none transition-all duration-200",title:"Editar",children:e.jsx("i",{className:"fas fa-edit"})}),e.jsx("button",{onClick:()=>{return e=a,void(window.confirm("¿Está seguro de eliminar este candidato?")&&(u(!0),setTimeout((()=>{m(c.filter((a=>a.id!==e))),y("Candidato eliminado correctamente"),u(!1)}),600)));var e},className:"text-red-600 hover:text-red-800 bg-red-100 p-2 rounded-lg focus:outline-none transition-all duration-200",title:"Eliminar",children:e.jsx("i",{className:"fas fa-trash-alt"})})]})}];return e.jsxs("div",{className:"container mx-auto py-6",children:[e.jsxs("div",{className:"mb-6 flex flex-col md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Gestión de Candidatos"}),e.jsx("p",{className:"text-gray-600",children:"Administre la información de los candidatos para pruebas psicométricas"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsxs(r,{variant:"primary",onClick:()=>k(),className:"flex items-center",children:[e.jsx("i",{className:"fas fa-plus mr-2"}),"Nuevo Candidato"]})})]}),e.jsxs(s,{className:"overflow-hidden shadow-lg border-0 rounded-xl",children:[e.jsx(l,{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white border-0",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-white/20 p-2 rounded-lg mr-3",children:e.jsx("i",{className:"fas fa-user-tie text-xl"})}),e.jsx("h2",{className:"text-xl font-semibold",children:"Lista de Candidatos"})]})}),e.jsx(t,{children:x?e.jsx("div",{className:"py-16 text-center",children:e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Cargando datos..."})]})}):e.jsx(d,{data:c,columns:S,pagination:{pageSize:5},searchable:!0})})]}),h&&e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center",children:e.jsx("div",{className:"relative mx-auto p-5 w-full max-w-md md:max-w-lg",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-2xl overflow-hidden",children:[e.jsxs("div",{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white py-4 px-6 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-white/20 h-8 w-8 rounded-lg flex items-center justify-center mr-3",children:e.jsx("i",{className:"fas fa-user-tie"})}),e.jsx("h3",{className:"text-lg font-medium",children:p?"Editar Candidato":"Nuevo Candidato"})]}),e.jsx("button",{onClick:E,className:"text-white hover:text-gray-200 focus:outline-none",children:e.jsx("i",{className:"fas fa-times"})})]}),e.jsxs("form",{onSubmit:e=>{e.preventDefault(),(b.firstName.trim()?b.lastName.trim()?b.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(b.email)?b.gender?b.age.trim()||(v("La edad es obligatoria"),0):(v("El género es obligatorio"),0):(v("El formato de email no es válido"),0):(v("El email es obligatorio"),0):(v("El apellido es obligatorio"),0):(v("El nombre es obligatorio"),0))&&(u(!0),setTimeout((()=>{const e={...b,id:b.id||Date.now(),age:parseInt(b.age,10)};p?(m(c.map((a=>a.id===e.id?e:a))),N("Candidato actualizado correctamente")):(m([...c,e]),N("Candidato creado correctamente")),u(!1),E()}),600))},className:"p-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Nombre ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(n,{type:"text",id:"firstName",name:"firstName",value:b.firstName,onChange:w,placeholder:"Nombre"})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Apellido ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(n,{type:"text",id:"lastName",name:"lastName",value:b.lastName,onChange:w,placeholder:"Apellido"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["Email ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(n,{type:"email",id:"email",name:"email",value:b.email,onChange:w,placeholder:"Email"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"gender",className:"block text-sm font-medium text-gray-700 mb-1",children:["Género ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(o,{id:"gender",name:"gender",value:b.gender,onChange:(e,a)=>{j({...b,[a]:e})},options:[{value:"male",label:"Masculino"},{value:"female",label:"Femenino"},{value:"other",label:"Otro"}],placeholder:"Seleccionar género"})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"age",className:"block text-sm font-medium text-gray-700 mb-1",children:["Edad ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(n,{type:"number",id:"age",name:"age",value:b.age,onChange:w,placeholder:"Edad",min:"18",max:"100"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"}),e.jsx(n,{type:"text",id:"phone",name:"phone",value:b.phone,onChange:w,placeholder:"Teléfono"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"education",className:"block text-sm font-medium text-gray-700 mb-1",children:"Formación Académica"}),e.jsx(n,{type:"text",id:"education",name:"education",value:b.education,onChange:w,placeholder:"Formación académica"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cargo o Posición"}),e.jsx(n,{type:"text",id:"position",name:"position",value:b.position,onChange:w,placeholder:"Cargo o posición"})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx(r,{type:"button",variant:"outline",onClick:E,children:"Cancelar"}),e.jsx(r,{type:"submit",variant:"primary",disabled:x,children:x?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Guardando..."]}):"Guardar"})]})]})]})})})]})};export{c as default};
//# sourceMappingURL=Candidates-b57ee93d.js.map
