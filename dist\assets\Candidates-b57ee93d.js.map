{"version": 3, "file": "Candidates-b57ee93d.js", "sources": ["../../src/components/ui/Input.jsx", "../../src/components/ui/Select.jsx", "../../src/components/ui/Table.jsx", "../../src/pages/candidate/Candidates.jsx"], "sourcesContent": ["import React from 'react';\n\nexport const Input = ({\n  type = 'text',\n  name,\n  value,\n  onChange,\n  placeholder,\n  disabled = false,\n  readOnly = false,\n  error,\n  className = '',\n  ...props\n}) => {\n  const baseStyles = 'block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm';\n  const errorStyles = error ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300';\n  const disabledStyles = disabled ? 'bg-gray-100 cursor-not-allowed' : '';\n  const styles = `${baseStyles} ${errorStyles} ${disabledStyles} ${className}`;\n\n  return (\n    <input\n      type={type}\n      name={name}\n      value={value}\n      onChange={onChange}\n      placeholder={placeholder}\n      disabled={disabled}\n      readOnly={readOnly}\n      className={styles}\n      aria-invalid={error ? 'true' : 'false'}\n      {...props}\n    />\n  );\n};\n\nexport default Input;", "import React, { useState, useRef, useEffect } from 'react';\n\nexport const Select = ({\n  options = [],\n  value,\n  onChange,\n  placeholder = 'Seleccionar...',\n  displayKey = 'label',\n  valueKey = 'value',\n  name,\n  disabled = false,\n  error,\n  className = '',\n  ...props\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const selectRef = useRef(null);\n  \n  const selectedOption = options.find(option => \n    option[valueKey] === value\n  );\n  \n  const displayValue = selectedOption \n    ? selectedOption[displayKey] \n    : placeholder;\n\n  const handleSelectOption = (option) => {\n    onChange(option[valueKey], name);\n    setIsOpen(false);\n  };\n\n  const toggleDropdown = () => {\n    if (!disabled) {\n      setIsOpen(!isOpen);\n    }\n  };\n\n  // Cerrar el dropdown cuando se hace clic fuera\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (selectRef.current && !selectRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const baseStyles = 'block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm';\n  const errorStyles = error ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500' : 'border-gray-300';\n  const disabledStyles = disabled ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer';\n  const styles = `${baseStyles} ${errorStyles} ${disabledStyles} ${className}`;\n\n  return (\n    <div className=\"relative\" ref={selectRef}>\n      <div\n        className={styles}\n        onClick={toggleDropdown}\n        tabIndex={0}\n        role=\"button\"\n        aria-haspopup=\"listbox\"\n        aria-expanded={isOpen}\n        {...props}\n      >\n        <div className=\"flex items-center justify-between\">\n          <span className={!selectedOption ? 'text-gray-500' : ''}>\n            {displayValue}\n          </span>\n          <svg \n            className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'transform rotate-180' : ''}`} \n            xmlns=\"http://www.w3.org/2000/svg\" \n            viewBox=\"0 0 20 20\" \n            fill=\"currentColor\"\n          >\n            <path \n              fillRule=\"evenodd\" \n              d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" \n              clipRule=\"evenodd\" \n            />\n          </svg>\n        </div>\n      </div>\n      \n      {isOpen && (\n        <div className=\"absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md max-h-60 overflow-auto border border-gray-200\">\n          <ul className=\"py-1\" role=\"listbox\">\n            {options.map((option, index) => (\n              <li\n                key={index}\n                className={`px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 ${\n                  option[valueKey] === value ? 'bg-primary-50 text-primary-700' : ''\n                }`}\n                onClick={() => handleSelectOption(option)}\n                role=\"option\"\n                aria-selected={option[valueKey] === value}\n              >\n                {option[displayKey]}\n              </li>\n            ))}\n            {options.length === 0 && (\n              <li className=\"px-3 py-2 text-sm text-gray-500\">\n                No hay opciones disponibles\n              </li>\n            )}\n          </ul>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Select;", "import React, { useState, useEffect } from 'react';\nimport { Input } from './Input';\n\nexport const Table = ({\n  data = [],\n  columns = [],\n  pagination = null,\n  searchable = false,\n  className = '',\n  ...props\n}) => {\n  const [currentPage, setCurrentPage] = useState(1);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filteredData, setFilteredData] = useState(data);\n  \n  const pageSize = pagination?.pageSize || 10;\n  const totalPages = Math.ceil(filteredData.length / pageSize);\n  \n  useEffect(() => {\n    // Filtrar datos cuando cambia la consulta de búsqueda\n    if (searchable && searchQuery) {\n      const filtered = data.filter(item => {\n        return columns.some(column => {\n          if (!column.accessor) return false;\n          \n          const value = item[column.accessor];\n          if (value === undefined || value === null) return false;\n          \n          return String(value).toLowerCase().includes(searchQuery.toLowerCase());\n        });\n      });\n      \n      setFilteredData(filtered);\n      setCurrentPage(1); // Resetear a la primera página al buscar\n    } else {\n      setFilteredData(data);\n    }\n  }, [searchQuery, data, columns, searchable]);\n  \n  // Paginar datos\n  const paginatedData = pagination\n    ? filteredData.slice((currentPage - 1) * pageSize, currentPage * pageSize)\n    : filteredData;\n  \n  const handlePageChange = (page) => {\n    setCurrentPage(page);\n  };\n  \n  return (\n    <div className=\"overflow-hidden\">\n      {searchable && (\n        <div className=\"mb-4\">\n          <Input\n            type=\"text\"\n            placeholder=\"Buscar...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"max-w-xs\"\n          />\n        </div>\n      )}\n      \n      <div className=\"overflow-x-auto\">\n        <table className={`min-w-full divide-y divide-gray-200 ${className}`} {...props}>\n          <thead className=\"bg-gray-50\">\n            <tr>\n              {columns.map((column, index) => (\n                <th\n                  key={index}\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                >\n                  {column.header}\n                </th>\n              ))}\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {paginatedData.length > 0 ? (\n              paginatedData.map((row, rowIndex) => (\n                <tr key={rowIndex} className=\"hover:bg-gray-50\">\n                  {columns.map((column, colIndex) => (\n                    <td key={colIndex} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {column.cell \n                        ? column.cell({ value: row[column.accessor], row })\n                        : row[column.accessor]}\n                    </td>\n                  ))}\n                </tr>\n              ))\n            ) : (\n              <tr>\n                <td\n                  colSpan={columns.length}\n                  className=\"px-6 py-4 text-center text-sm text-gray-500\"\n                >\n                  No hay datos disponibles\n                </td>\n              </tr>\n            )}\n          </tbody>\n        </table>\n      </div>\n      \n      {pagination && totalPages > 1 && (\n        <nav className=\"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\">\n          <div className=\"hidden sm:block\">\n            <p className=\"text-sm text-gray-700\">\n              Mostrando <span className=\"font-medium\">{((currentPage - 1) * pageSize) + 1}</span>{' '}\n              a{' '}\n              <span className=\"font-medium\">\n                {Math.min(currentPage * pageSize, filteredData.length)}\n              </span>{' '}\n              de <span className=\"font-medium\">{filteredData.length}</span> resultados\n            </p>\n          </div>\n          <div className=\"flex-1 flex justify-between sm:justify-end\">\n            <button\n              onClick={() => handlePageChange(currentPage - 1)}\n              disabled={currentPage === 1}\n              className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${\n                currentPage === 1\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-white text-gray-700 hover:bg-gray-50'\n              }`}\n            >\n              Anterior\n            </button>\n            <div className=\"hidden md:flex mx-2\">\n              {[...Array(totalPages)].map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => handlePageChange(index + 1)}\n                  className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\n                    currentPage === index + 1\n                      ? 'bg-primary-50 border-primary-500 text-primary-700'\n                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  {index + 1}\n                </button>\n              ))}\n            </div>\n            <button\n              onClick={() => handlePageChange(currentPage + 1)}\n              disabled={currentPage === totalPages}\n              className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${\n                currentPage === totalPages\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-white text-gray-700 hover:bg-gray-50'\n              }`}\n            >\n              Siguiente\n            </button>\n          </div>\n        </nav>\n      )}\n    </div>\n  );\n};\n\nexport default Table;", "import React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, CardBody, CardFooter } from '../../components/ui/Card';\nimport { Button } from '../../components/ui/Button';\nimport { Input } from '../../components/ui/Input';\nimport { Select } from '../../components/ui/Select';\nimport { Table } from '../../components/ui/Table';\nimport { useToast } from '../../hooks/useToast';\n\nconst Candidates = () => {\n  const [candidates, setCandidates] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showModal, setShowModal] = useState(false);\n  const [currentCandidate, setCurrentCandidate] = useState(null);\n  const [formData, setFormData] = useState({\n    id: null,\n    firstName: '',\n    lastName: '',\n    email: '',\n    gender: '',\n    age: '',\n    phone: '',\n    education: '',\n    position: ''\n  });\n  const { showSuccess, showError, showInfo } = useToast();\n\n  // Datos de ejemplo para simular una API\n  useEffect(() => {\n    setIsLoading(true);\n    // Simulación de carga de datos\n    setTimeout(() => {\n      const mockCandidates = [\n        {\n          id: 1,\n          firstName: 'Juan',\n          lastName: 'Pérez',\n          email: '<EMAIL>',\n          gender: 'male',\n          age: 28,\n          phone: '+591 72345678',\n          education: 'Licenciatura en Psicología',\n          position: 'Psicólogo Clínico'\n        },\n        {\n          id: 2,\n          firstName: 'María',\n          lastName: 'González',\n          email: '<EMAIL>',\n          gender: 'female',\n          age: 32,\n          phone: '+591 73456789',\n          education: 'Maestría en Recursos Humanos',\n          position: 'Especialista en RRHH'\n        },\n        {\n          id: 3,\n          firstName: 'Carlos',\n          lastName: 'Rodríguez',\n          email: '<EMAIL>',\n          gender: 'male',\n          age: 25,\n          phone: '+591 74567890',\n          education: 'Ingeniería en Sistemas',\n          position: 'Desarrollador Web'\n        }\n      ];\n      setCandidates(mockCandidates);\n      setIsLoading(false);\n    }, 800);\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  const handleSelectChange = (value, name) => {\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  const resetForm = () => {\n    setFormData({\n      id: null,\n      firstName: '',\n      lastName: '',\n      email: '',\n      gender: '',\n      age: '',\n      phone: '',\n      education: '',\n      position: ''\n    });\n    setCurrentCandidate(null);\n  };\n\n  const openModal = (candidate = null) => {\n    if (candidate) {\n      setFormData({\n        id: candidate.id,\n        firstName: candidate.firstName,\n        lastName: candidate.lastName,\n        email: candidate.email,\n        gender: candidate.gender,\n        age: candidate.age.toString(),\n        phone: candidate.phone,\n        education: candidate.education,\n        position: candidate.position\n      });\n      setCurrentCandidate(candidate);\n    } else {\n      resetForm();\n    }\n    setShowModal(true);\n  };\n\n  const closeModal = () => {\n    setShowModal(false);\n    resetForm();\n  };\n\n  const validateForm = () => {\n    if (!formData.firstName.trim()) {\n      showError('El nombre es obligatorio');\n      return false;\n    }\n    if (!formData.lastName.trim()) {\n      showError('El apellido es obligatorio');\n      return false;\n    }\n    if (!formData.email.trim()) {\n      showError('El email es obligatorio');\n      return false;\n    }\n    // Validación básica de email\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(formData.email)) {\n      showError('El formato de email no es válido');\n      return false;\n    }\n    if (!formData.gender) {\n      showError('El género es obligatorio');\n      return false;\n    }\n    if (!formData.age.trim()) {\n      showError('La edad es obligatoria');\n      return false;\n    }\n    return true;\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    // Simulación de operación con la API\n    setIsLoading(true);\n    \n    setTimeout(() => {\n      const newCandidate = {\n        ...formData,\n        id: formData.id || Date.now(),\n        age: parseInt(formData.age, 10)\n      };\n      \n      if (currentCandidate) {\n        // Actualizar candidato existente\n        setCandidates(candidates.map(c => \n          c.id === newCandidate.id ? newCandidate : c\n        ));\n        showSuccess('Candidato actualizado correctamente');\n      } else {\n        // Agregar nuevo candidato\n        setCandidates([...candidates, newCandidate]);\n        showSuccess('Candidato creado correctamente');\n      }\n      \n      setIsLoading(false);\n      closeModal();\n    }, 600);\n  };\n\n  const handleDelete = (candidateId) => {\n    if (window.confirm('¿Está seguro de eliminar este candidato?')) {\n      setIsLoading(true);\n      \n      // Simulación de eliminación\n      setTimeout(() => {\n        setCandidates(candidates.filter(c => c.id !== candidateId));\n        showInfo('Candidato eliminado correctamente');\n        setIsLoading(false);\n      }, 600);\n    }\n  };\n\n  // Definir las columnas para la tabla\n  const columns = [\n    {\n      header: '',\n      accessor: 'gender',\n      cell: ({ value }) => (\n        <div className=\"flex justify-center\">\n          {value === 'male' ? (\n            <div className=\"w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center\">\n              <i className=\"fas fa-mars text-blue-600\"></i>\n            </div>\n          ) : value === 'female' ? (\n            <div className=\"w-8 h-8 rounded-lg bg-pink-100 flex items-center justify-center\">\n              <i className=\"fas fa-venus text-pink-600\"></i>\n            </div>\n          ) : (\n            <div className=\"w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center\">\n              <i className=\"fas fa-genderless text-purple-600\"></i>\n            </div>\n          )}\n        </div>\n      )\n    },\n    {\n      header: 'Nombre',\n      accessor: 'firstName',\n      cell: ({ value, row }) => (\n        <div>\n          <div className=\"font-medium text-gray-900\">\n            {value} {row.lastName}\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            {row.position || 'Sin cargo asignado'}\n          </div>\n        </div>\n      )\n    },\n    {\n      header: 'Email',\n      accessor: 'email',\n      cell: ({ value }) => (\n        <div>\n          <div className=\"text-gray-900\">{value}</div>\n          <div className=\"text-xs text-gray-500\">\n            <i className=\"fas fa-envelope mr-1\"></i> Contacto principal\n          </div>\n        </div>\n      )\n    },\n    {\n      header: 'Teléfono',\n      accessor: 'phone'\n    },\n    {\n      header: 'Edad',\n      accessor: 'age',\n      cell: ({ value }) => (\n        <div className=\"text-center bg-gray-100 rounded-lg py-1 px-2 w-12\">\n          {value}\n        </div>\n      )\n    },\n    {\n      header: 'Formación',\n      accessor: 'education'\n    },\n    {\n      header: 'Acciones',\n      accessor: 'id',\n      cell: ({ value, row }) => (\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => openModal(row)}\n            className=\"text-blue-600 hover:text-blue-800 bg-blue-100 p-2 rounded-lg focus:outline-none transition-all duration-200\"\n            title=\"Editar\"\n          >\n            <i className=\"fas fa-edit\"></i>\n          </button>\n          <button\n            onClick={() => handleDelete(value)}\n            className=\"text-red-600 hover:text-red-800 bg-red-100 p-2 rounded-lg focus:outline-none transition-all duration-200\"\n            title=\"Eliminar\"\n          >\n            <i className=\"fas fa-trash-alt\"></i>\n          </button>\n        </div>\n      )\n    }\n  ];\n\n  // Opciones para el select de género\n  const genderOptions = [\n    { value: 'male', label: 'Masculino' },\n    { value: 'female', label: 'Femenino' },\n    { value: 'other', label: 'Otro' }\n  ];\n\n  return (\n    <div className=\"container mx-auto py-6\">\n      <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Gestión de Candidatos</h1>\n          <p className=\"text-gray-600\">Administre la información de los candidatos para pruebas psicométricas</p>\n        </div>\n        <div className=\"mt-4 md:mt-0\">\n          <Button\n            variant=\"primary\"\n            onClick={() => openModal()}\n            className=\"flex items-center\"\n          >\n            <i className=\"fas fa-plus mr-2\"></i>\n            Nuevo Candidato\n          </Button>\n        </div>\n      </div>\n\n      <Card className=\"overflow-hidden shadow-lg border-0 rounded-xl\">\n        <CardHeader className=\"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white border-0\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-white/20 p-2 rounded-lg mr-3\">\n              <i className=\"fas fa-user-tie text-xl\"></i>\n            </div>\n            <h2 className=\"text-xl font-semibold\">Lista de Candidatos</h2>\n          </div>\n        </CardHeader>\n        <CardBody>\n          {isLoading ? (\n            <div className=\"py-16 text-center\">\n              <div className=\"flex flex-col items-center justify-center\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4\"></div>\n                <p className=\"text-gray-500\">Cargando datos...</p>\n              </div>\n            </div>\n          ) : (\n            <Table\n              data={candidates}\n              columns={columns}\n              pagination={{ pageSize: 5 }}\n              searchable={true}\n            />\n          )}\n        </CardBody>\n      </Card>\n\n      {/* Modal para crear/editar candidato */}\n      {showModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\">\n          <div className=\"relative mx-auto p-5 w-full max-w-md md:max-w-lg\">\n            <div className=\"bg-white rounded-xl shadow-2xl overflow-hidden\">\n              <div className=\"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white py-4 px-6 flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <div className=\"bg-white/20 h-8 w-8 rounded-lg flex items-center justify-center mr-3\">\n                    <i className=\"fas fa-user-tie\"></i>\n                  </div>\n                  <h3 className=\"text-lg font-medium\">\n                    {currentCandidate ? 'Editar Candidato' : 'Nuevo Candidato'}\n                  </h3>\n                </div>\n                <button \n                  onClick={closeModal}\n                  className=\"text-white hover:text-gray-200 focus:outline-none\"\n                >\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              </div>\n              \n              <form onSubmit={handleSubmit} className=\"p-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Nombre <span className=\"text-red-500\">*</span>\n                    </label>\n                    <Input\n                      type=\"text\"\n                      id=\"firstName\"\n                      name=\"firstName\"\n                      value={formData.firstName}\n                      onChange={handleInputChange}\n                      placeholder=\"Nombre\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Apellido <span className=\"text-red-500\">*</span>\n                    </label>\n                    <Input\n                      type=\"text\"\n                      id=\"lastName\"\n                      name=\"lastName\"\n                      value={formData.lastName}\n                      onChange={handleInputChange}\n                      placeholder=\"Apellido\"\n                    />\n                  </div>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Email <span className=\"text-red-500\">*</span>\n                  </label>\n                  <Input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    placeholder=\"Email\"\n                  />\n                </div>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <label htmlFor=\"gender\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Género <span className=\"text-red-500\">*</span>\n                    </label>\n                    <Select\n                      id=\"gender\"\n                      name=\"gender\"\n                      value={formData.gender}\n                      onChange={handleSelectChange}\n                      options={genderOptions}\n                      placeholder=\"Seleccionar género\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"age\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Edad <span className=\"text-red-500\">*</span>\n                    </label>\n                    <Input\n                      type=\"number\"\n                      id=\"age\"\n                      name=\"age\"\n                      value={formData.age}\n                      onChange={handleInputChange}\n                      placeholder=\"Edad\"\n                      min=\"18\"\n                      max=\"100\"\n                    />\n                  </div>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Teléfono\n                  </label>\n                  <Input\n                    type=\"text\"\n                    id=\"phone\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleInputChange}\n                    placeholder=\"Teléfono\"\n                  />\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"education\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Formación Académica\n                  </label>\n                  <Input\n                    type=\"text\"\n                    id=\"education\"\n                    name=\"education\"\n                    value={formData.education}\n                    onChange={handleInputChange}\n                    placeholder=\"Formación académica\"\n                  />\n                </div>\n                \n                <div className=\"mb-6\">\n                  <label htmlFor=\"position\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Cargo o Posición\n                  </label>\n                  <Input\n                    type=\"text\"\n                    id=\"position\"\n                    name=\"position\"\n                    value={formData.position}\n                    onChange={handleInputChange}\n                    placeholder=\"Cargo o posición\"\n                  />\n                </div>\n                \n                <div className=\"flex justify-end space-x-3\">\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    onClick={closeModal}\n                  >\n                    Cancelar\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    variant=\"primary\"\n                    disabled={isLoading}\n                  >\n                    {isLoading ? (\n                      <>\n                        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                        </svg>\n                        Guardando...\n                      </>\n                    ) : (\n                      'Guardar'\n                    )}\n                  </Button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Candidates;"], "names": ["Input", "type", "name", "value", "onChange", "placeholder", "disabled", "readOnly", "error", "className", "props", "styles", "jsxRuntimeExports", "jsx", "Select", "options", "displayKey", "valueKey", "isOpen", "setIsOpen", "useState", "selectRef", "useRef", "selectedOption", "find", "option", "displayValue", "useEffect", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "jsxs", "ref", "children", "onClick", "tabIndex", "role", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "map", "index", "handleSelectOption", "length", "Table", "data", "columns", "pagination", "searchable", "currentPage", "setCurrentPage", "searchQuery", "setSearch<PERSON>uery", "filteredData", "setFilteredData", "pageSize", "totalPages", "Math", "ceil", "filtered", "filter", "item", "some", "column", "accessor", "String", "toLowerCase", "includes", "paginatedData", "slice", "handlePageChange", "page", "e", "scope", "header", "row", "rowIndex", "colIndex", "cell", "colSpan", "min", "Array", "_", "Candidates", "candidates", "setCandidates", "isLoading", "setIsLoading", "showModal", "setShowModal", "currentCandidate", "setCurrentCandidate", "formData", "setFormData", "id", "firstName", "lastName", "email", "gender", "age", "phone", "education", "position", "showSuccess", "showError", "showInfo", "useToast", "setTimeout", "handleInputChange", "resetForm", "openModal", "candidate", "toString", "closeModal", "title", "handleDelete", "candidateId", "window", "confirm", "c", "<PERSON><PERSON>", "variant", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardBody", "onSubmit", "preventDefault", "trim", "test", "newCandidate", "Date", "now", "parseInt", "htmlFor", "label", "max", "Fragment", "cx", "cy", "r", "stroke", "strokeWidth"], "mappings": "wLAEO,MAAMA,EAAQ,EACnBC,OAAO,OACPC,OACAC,QACAC,WACAC,cACAC,YAAW,EACXC,YAAW,EACXC,QACAC,YAAY,MACTC,MAEH,MAGMC,EAAS,oIAFKH,EAAQ,0FAA4F,qBACjGF,EAAW,iCAAmC,MACJG,IAG/D,OAAAG,EAAAC,IAAC,QAAA,CACCZ,OACAC,OACAC,QACAC,WACAC,cACAC,WACAC,WACAE,UAAWE,EACX,eAAcH,EAAQ,OAAS,WAC3BE,GAAA,EC5BGI,EAAS,EACpBC,UAAU,GACVZ,QACAC,WACAC,cAAc,iBACdW,aAAa,QACbC,WAAW,QACXf,OACAI,YAAW,EACXE,QACAC,YAAY,MACTC,MAEH,MAAOQ,EAAQC,GAAaC,YAAS,GAC/BC,EAAYC,SAAO,MAEnBC,EAAiBR,EAAQS,MAAKC,GAClCA,EAAOR,KAAcd,IAGjBuB,EAAeH,EACjBA,EAAeP,GACfX,EAcJsB,EAAAA,WAAU,KACF,MAAAC,EAAsBC,IACtBR,EAAUS,UAAYT,EAAUS,QAAQC,SAASF,EAAMG,SACzDb,GAAU,EACZ,EAIF,OADSc,SAAAC,iBAAiB,YAAaN,GAChC,KACIK,SAAAE,oBAAoB,YAAaP,EAAkB,CAAA,GAE7D,IAEH,MAGMjB,EAAS,oIAFKH,EAAQ,sEAAwE,qBAC7EF,EAAW,iCAAmC,oBACJG,IAG9D2B,OAAAA,EAAAA,KAAA,MAAA,CAAI3B,UAAU,WAAW4B,IAAKhB,EAC7BiB,SAAA,CAAA1B,EAAAC,IAAC,MAAA,CACCJ,UAAWE,EACX4B,QA7BiB,KAChBjC,GACHa,GAAWD,EACb,EA2BIsB,SAAU,EACVC,KAAK,SACL,gBAAc,UACd,gBAAevB,KACXR,EAEJ4B,SAAA1B,EAAAwB,KAAC,MAAI,CAAA3B,UAAU,oCACb6B,SAAA,CAAAzB,MAAC,QAAKJ,UAAYc,EAAmC,GAAlB,gBAChCe,SACHZ,IACAd,EAAAC,IAAC,MAAA,CACCJ,UAAW,+CAA8CS,EAAS,uBAAyB,IAC3FwB,MAAM,6BACNC,QAAQ,YACRC,KAAK,eAELN,SAAA1B,EAAAC,IAAC,OAAA,CACCgC,SAAS,UACTC,EAAE,qHACFC,SAAS,mBAMhB7B,GACEL,EAAAA,IAAA,MAAA,CAAIJ,UAAU,wGACb6B,gBAAC,KAAG,CAAA7B,UAAU,OAAOgC,KAAK,UACvBH,SAAA,CAAQvB,EAAAiC,KAAI,CAACvB,EAAQwB,IACpBrC,EAAAC,IAAC,KAAA,CAECJ,UAAW,uDACTgB,EAAOR,KAAcd,EAAQ,iCAAmC,IAElEoC,QAAS,IArEI,CAACd,IACjBrB,EAAAqB,EAAOR,GAAWf,GAC3BiB,GAAU,EAAK,EAmEY+B,CAAmBzB,GAClCgB,KAAK,SACL,gBAAehB,EAAOR,KAAcd,EAEnCmC,WAAOtB,IARHiC,KAWW,IAAnBlC,EAAQoC,cACN,KAAG,CAAA1C,UAAU,kCAAkC6B,SAEhD,uCAKV,EC3GSc,EAAQ,EACnBC,OAAO,GACPC,UAAU,GACVC,aAAa,KACbC,cAAa,EACb/C,YAAY,MACTC,MAEH,MAAO+C,EAAaC,GAAkBtC,WAAS,IACxCuC,EAAaC,GAAkBxC,WAAS,KACxCyC,EAAcC,GAAmB1C,WAASiC,GAE3CU,SAAWR,WAAYQ,WAAY,GACnCC,EAAaC,KAAKC,KAAKL,EAAaV,OAASY,GAEnDpC,EAAAA,WAAU,KAER,GAAI6B,GAAcG,EAAa,CACvB,MAAAQ,EAAWd,EAAKe,QAAeC,GAC5Bf,EAAQgB,MAAeC,IAC5B,IAAKA,EAAOC,SAAiB,OAAA,EAEvB,MAAArE,EAAQkE,EAAKE,EAAOC,UACtB,OAAArE,SAEGsE,OAAOtE,GAAOuE,cAAcC,SAAShB,EAAYe,cAAa,MAIzEZ,EAAgBK,GAChBT,EAAe,EAAC,MAEhBI,EAAgBT,EAClB,GACC,CAACM,EAAaN,EAAMC,EAASE,IAG1B,MAAAoB,EAAgBrB,EAClBM,EAAagB,OAAOpB,EAAc,GAAKM,EAAUN,EAAcM,GAC/DF,EAEEiB,EAAoBC,IACxBrB,EAAeqB,EAAI,EAInB,SAAA3C,KAAC,MAAI,CAAA3B,UAAU,kBACZ6B,SAAA,CACCkB,KAAA3C,IAAC,MAAI,CAAAJ,UAAU,OACb6B,SAAA1B,EAAAC,IAACb,EAAA,CACCC,KAAK,OACLI,YAAY,YACZF,MAAOwD,EACPvD,SAAW4E,GAAMpB,EAAeoB,EAAEhD,OAAO7B,OACzCM,UAAU,iBAKhBI,IAAC,MAAI,CAAAJ,UAAU,kBACb6B,SAAAF,EAAAA,KAAC,QAAM,CAAA3B,UAAW,uCAAuCA,OAAiBC,EACxE4B,SAAA,GAACzB,IAAA,QAAA,CAAMJ,UAAU,aACf6B,SAAAzB,EAAAA,IAAC,MACEyB,SAAQgB,EAAAN,KAAI,CAACuB,EAAQtB,IACpBrC,EAAAC,IAAC,KAAA,CAECoE,MAAM,MACNxE,UAAU,iFAET6B,SAAOiC,EAAAW,QAJHjC,SASbpC,EAAAA,IAAC,SAAMJ,UAAU,oCACd6B,WAAca,OAAS,EACtByB,EAAc5B,KAAI,CAACmC,EAAKC,IACrBvE,EAAAA,IAAA,KAAA,CAAkBJ,UAAU,mBAC1B6B,SAAAgB,EAAQN,KAAI,CAACuB,EAAQc,IACpBxE,EAAAA,IAAC,KAAkB,CAAAJ,UAAU,oDAC1B6B,SAAOiC,EAAAe,KACJf,EAAOe,KAAK,CAAEnF,MAAOgF,EAAIZ,EAAOC,UAAWW,QAC3CA,EAAIZ,EAAOC,WAHRa,MAFJD,KAWXxE,EAAAC,IAAC,KACC,CAAAyB,SAAA1B,EAAAC,IAAC,KAAA,CACC0E,QAASjC,EAAQH,OACjB1C,UAAU,8CACX6B,SAAA,sCASViB,GAAcS,EAAa,GACzB5B,EAAAA,KAAA,MAAA,CAAI3B,UAAU,oFACb6B,SAAA,CAAAzB,EAAAA,IAAC,OAAIJ,UAAU,kBACb6B,SAACF,EAAAA,KAAA,IAAA,CAAE3B,UAAU,wBAAwB6B,SAAA,CAAA,mBACxB,OAAK,CAAA7B,UAAU,cAAiB6B,UAAcmB,EAAA,GAAKM,EAAY,IAAU,IAAI,IACtF,IACFlD,EAAAA,IAAC,OAAK,CAAAJ,UAAU,cACb6B,SAAA2B,KAAKuB,IAAI/B,EAAcM,EAAUF,EAAaV,UACzC,IAAI,MACRtC,EAAAA,IAAA,OAAA,CAAKJ,UAAU,cAAe6B,WAAaa,SAAc,qBAGjEf,KAAC,MAAI,CAAA3B,UAAU,6CACb6B,SAAA,CAAA1B,EAAAC,IAAC,SAAA,CACC0B,QAAS,IAAMuC,EAAiBrB,EAAc,GAC9CnD,SAA0B,IAAhBmD,EACVhD,UAAW,sGACO,IAAhBgD,EACI,+CACA,2CAEPnB,SAAA,eAGAzB,IAAA,MAAA,CAAIJ,UAAU,sBACZ6B,SAAC,IAAGmD,MAAMzB,IAAahB,KAAI,CAAC0C,EAAGzC,IAC9BrC,EAAAC,IAAC,SAAA,CAEC0B,QAAS,IAAMuC,EAAiB7B,EAAQ,GACxCxC,UAAW,2EACTgD,IAAgBR,EAAQ,EACpB,oDACA,2DAGLX,SAAQW,EAAA,GARJA,OAYXrC,EAAAC,IAAC,SAAA,CACC0B,QAAS,IAAMuC,EAAiBrB,EAAc,GAC9CnD,SAAUmD,IAAgBO,EAC1BvD,UAAW,2GACTgD,IAAgBO,EACZ,+CACA,2CAEP1B,SAAA,sBAMT,ECrJEqD,EAAa,KACjB,MAAOC,EAAYC,GAAiBzE,EAAAA,SAAS,KACtC0E,EAAWC,GAAgB3E,YAAS,IACpC4E,EAAWC,GAAgB7E,YAAS,IACpC8E,EAAkBC,GAAuB/E,WAAS,OAClDgF,EAAUC,GAAejF,WAAS,CACvCkF,GAAI,KACJC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,OAAQ,GACRC,IAAK,GACLC,MAAO,GACPC,UAAW,GACXC,SAAU,MAENC,YAAEA,EAAAC,UAAaA,EAAWC,SAAAA,GAAaC,IAG7CvF,EAAAA,WAAU,KACRoE,GAAa,GAEboB,YAAW,KAoCTtB,EAnCuB,CACrB,CACES,GAAI,EACJC,UAAW,OACXC,SAAU,QACVC,MAAO,yBACPC,OAAQ,OACRC,IAAK,GACLC,MAAO,gBACPC,UAAW,6BACXC,SAAU,qBAEZ,CACER,GAAI,EACJC,UAAW,QACXC,SAAU,WACVC,MAAO,6BACPC,OAAQ,SACRC,IAAK,GACLC,MAAO,gBACPC,UAAW,+BACXC,SAAU,wBAEZ,CACER,GAAI,EACJC,UAAW,SACXC,SAAU,YACVC,MAAO,+BACPC,OAAQ,OACRC,IAAK,GACLC,MAAO,gBACPC,UAAW,yBACXC,SAAU,uBAIdf,GAAa,EAAK,GACjB,IAAG,GACL,IAEG,MAAAqB,EAAqBpC,IACzB,MAAM9E,KAAEA,EAAAC,MAAMA,GAAU6E,EAAEhD,OACdqE,EAAA,IACPD,EACHlG,CAACA,GAAOC,GACT,EAUGkH,EAAY,KACJhB,EAAA,CACVC,GAAI,KACJC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,OAAQ,GACRC,IAAK,GACLC,MAAO,GACPC,UAAW,GACXC,SAAU,KAEZX,EAAoB,KAAI,EAGpBmB,EAAY,CAACC,EAAY,QACzBA,GACUlB,EAAA,CACVC,GAAIiB,EAAUjB,GACdC,UAAWgB,EAAUhB,UACrBC,SAAUe,EAAUf,SACpBC,MAAOc,EAAUd,MACjBC,OAAQa,EAAUb,OAClBC,IAAKY,EAAUZ,IAAIa,WACnBZ,MAAOW,EAAUX,MACjBC,UAAWU,EAAUV,UACrBC,SAAUS,EAAUT,WAEtBX,EAAoBoB,QAItBtB,GAAa,EAAI,EAGbwB,EAAa,KACjBxB,GAAa,QAgFT3C,EAAU,CACd,CACE4B,OAAQ,GACRV,SAAU,SACVc,KAAM,EAAGnF,WACPU,MAAC,OAAIJ,UAAU,sBACZ6B,SAAU,WACTzB,EAAAA,IAAC,OAAIJ,UAAU,kEACb6B,eAAC,IAAE,CAAA7B,UAAU,gCAEH,WAAVN,QACD,MAAI,CAAAM,UAAU,kEACb6B,eAAC,IAAA,CAAE7B,UAAU,iCAGdI,MAAA,MAAA,CAAIJ,UAAU,oEACb6B,WAAAzB,IAAC,KAAEJ,UAAU,2CAMvB,CACEyE,OAAQ,SACRV,SAAU,YACVc,KAAM,EAAGnF,QAAOgF,gBACb,MACC,CAAA7C,SAAA,GAACF,KAAA,MAAA,CAAI3B,UAAU,4BACZ6B,SAAA,CAAAnC,EAAM,IAAEgF,EAAIqB,kBAEd,MAAI,CAAA/F,UAAU,wBACZ6B,SAAA6C,EAAI2B,UAAY,2BAKzB,CACE5B,OAAQ,QACRV,SAAU,QACVc,KAAM,EAAGnF,kBACN,MACC,CAAAmC,SAAA,CAACzB,EAAAA,IAAA,MAAA,CAAIJ,UAAU,gBAAiB6B,SAAMnC,MACtCiC,KAAC,MAAI,CAAA3B,UAAU,wBACb6B,SAAA,GAACzB,IAAA,IAAA,CAAEJ,UAAU,yBAA2B,6BAKhD,CACEyE,OAAQ,WACRV,SAAU,SAEZ,CACEU,OAAQ,OACRV,SAAU,MACVc,KAAM,EAAGnF,WACNU,MAAA,MAAA,CAAIJ,UAAU,oDACZ6B,SACHnC,KAGJ,CACE+E,OAAQ,YACRV,SAAU,aAEZ,CACEU,OAAQ,WACRV,SAAU,KACVc,KAAM,EAAGnF,QAAOgF,gBACb,MAAI,CAAA1E,UAAU,iBACb6B,SAAA,CAAA1B,EAAAC,IAAC,SAAA,CACC0B,QAAS,IAAM+E,EAAUnC,GACzB1E,UAAU,8GACViH,MAAM,SAENpF,SAAAzB,EAAAA,IAAC,IAAE,CAAAJ,UAAU,kBAEfG,EAAAC,IAAC,SAAA,CACC0B,QAAS,KAAMoF,OA5FHC,EA4FgBzH,OA3FhC0H,OAAOC,QAAQ,8CACjB/B,GAAa,GAGboB,YAAW,KACTtB,EAAcD,EAAWxB,QAAO2D,GAAKA,EAAEzB,KAAOsB,KAC9CX,EAAS,qCACTlB,GAAa,EAAK,GACjB,OATc,IAAC6B,CA4FqB,EACjCnH,UAAU,2GACViH,MAAM,WAENpF,SAAAzB,EAAAA,IAAC,IAAE,CAAAJ,UAAU,4BAerB,SAAA2B,KAAC,MAAI,CAAA3B,UAAU,yBACb6B,SAAA,GAACF,KAAA,MAAA,CAAI3B,UAAU,oEACb6B,SAAA,QAAC,MACC,CAAAA,SAAA,CAACzB,EAAAA,IAAA,KAAA,CAAGJ,UAAU,wCAAwC6B,SAAqB,0BAC1EzB,EAAAA,IAAA,IAAA,CAAEJ,UAAU,gBAAgB6B,SAAsE,gFAErGzB,IAAC,MAAI,CAAAJ,UAAU,eACb6B,SAAA1B,EAAAwB,KAAC4F,EAAA,CACCC,QAAQ,UACR1F,QAAS,IAAM+E,IACf7G,UAAU,oBAEV6B,SAAA,GAACzB,IAAA,IAAA,CAAEJ,UAAU,qBAAuB,4BAM1C2B,KAAC8F,EAAK,CAAAzH,UAAU,gDACd6B,SAAA,CAAAzB,EAAAA,IAACsH,GAAW1H,UAAU,kFACpB6B,SAACF,EAAAA,KAAA,MAAA,CAAI3B,UAAU,oBACb6B,SAAA,CAAAzB,EAAAA,IAAC,OAAIJ,UAAU,kCACb6B,eAAC,IAAE,CAAA7B,UAAU,8BAEdI,EAAAA,IAAA,KAAA,CAAGJ,UAAU,wBAAwB6B,SAAmB,+BAG7DzB,IAACuH,EACE,CAAA9F,SAAAwD,IACEjF,IAAA,MAAA,CAAIJ,UAAU,oBACb6B,SAAAF,EAAAA,KAAC,MAAI,CAAA3B,UAAU,4CACb6B,SAAA,GAACzB,IAAA,MAAA,CAAIJ,UAAU,wEACdI,EAAAA,IAAA,IAAA,CAAEJ,UAAU,gBAAgB6B,SAAiB,2BAIlD1B,EAAAC,IAACuC,EAAA,CACCC,KAAMuC,EACNtC,UACAC,WAAY,CAAEQ,SAAU,GACxBP,YAAY,SAOnBwC,KACCnF,IAAC,MAAI,CAAAJ,UAAU,8GACb6B,SAAAzB,EAAAA,IAAC,MAAI,CAAAJ,UAAU,mDACb6B,SAAAF,EAAAA,KAAC,MAAI,CAAA3B,UAAU,iDACb6B,SAAA,GAACF,KAAA,MAAA,CAAI3B,UAAU,qHACb6B,SAAA,GAACF,KAAA,MAAA,CAAI3B,UAAU,oBACb6B,SAAA,CAAAzB,EAAAA,IAAC,OAAIJ,UAAU,uEACb6B,eAAC,IAAE,CAAA7B,UAAU,4BAEd,KAAG,CAAAA,UAAU,sBACX6B,SAAA4D,EAAmB,mBAAqB,uBAG7CtF,EAAAC,IAAC,SAAA,CACC0B,QAASkF,EACThH,UAAU,oDAEV6B,SAAAzB,EAAAA,IAAC,IAAE,CAAAJ,UAAU,sBAIhB2B,EAAAA,KAAA,OAAA,CAAKiG,SAnNIrD,IACpBA,EAAEsD,kBA9BGlC,EAASG,UAAUgC,OAInBnC,EAASI,SAAS+B,OAIlBnC,EAASK,MAAM8B,OAKD,6BACHC,KAAKpC,EAASK,OAIzBL,EAASM,OAITN,EAASO,IAAI4B,SAChBvB,EAAU,0BACH,IALPA,EAAU,4BACH,IALPA,EAAU,oCACH,IAPPA,EAAU,2BACH,IALPA,EAAU,8BACH,IALPA,EAAU,4BACH,MAiCTjB,GAAa,GAEboB,YAAW,KACT,MAAMsB,EAAe,IAChBrC,EACHE,GAAIF,EAASE,IAAMoC,KAAKC,MACxBhC,IAAKiC,SAASxC,EAASO,IAAK,KAG1BT,GAEFL,EAAcD,EAAW5C,KACvB+E,GAAAA,EAAEzB,KAAOmC,EAAanC,GAAKmC,EAAeV,KAE5ChB,EAAY,yCAGZlB,EAAc,IAAID,EAAY6C,IAC9B1B,EAAY,mCAGdhB,GAAa,SAEZ,KAAG,EAsLkCtF,UAAU,MACtC6B,SAAA,GAACF,KAAA,MAAA,CAAI3B,UAAU,6CACb6B,SAAA,QAAC,MACC,CAAAA,SAAA,CAAAF,EAAAA,KAAC,QAAM,CAAAyG,QAAQ,YAAYpI,UAAU,+CAA+C6B,SAAA,CAAA,UAC1EzB,EAAAA,IAAA,OAAA,CAAKJ,UAAU,eAAe6B,SAAC,SAEzC1B,EAAAC,IAACb,EAAA,CACCC,KAAK,OACLqG,GAAG,YACHpG,KAAK,YACLC,MAAOiG,EAASG,UAChBnG,SAAUgH,EACV/G,YAAY,qBAIf,MACC,CAAAiC,SAAA,CAAAF,EAAAA,KAAC,QAAM,CAAAyG,QAAQ,WAAWpI,UAAU,+CAA+C6B,SAAA,CAAA,YACvEzB,EAAAA,IAAA,OAAA,CAAKJ,UAAU,eAAe6B,SAAC,SAE3C1B,EAAAC,IAACb,EAAA,CACCC,KAAK,OACLqG,GAAG,WACHpG,KAAK,WACLC,MAAOiG,EAASI,SAChBpG,SAAUgH,EACV/G,YAAY,qBAKlB+B,KAAC,MAAI,CAAA3B,UAAU,OACb6B,SAAA,CAAAF,EAAAA,KAAC,QAAM,CAAAyG,QAAQ,QAAQpI,UAAU,+CAA+C6B,SAAA,CAAA,SACvEzB,EAAAA,IAAA,OAAA,CAAKJ,UAAU,eAAe6B,SAAC,SAExC1B,EAAAC,IAACb,EAAA,CACCC,KAAK,QACLqG,GAAG,QACHpG,KAAK,QACLC,MAAOiG,EAASK,MAChBrG,SAAUgH,EACV/G,YAAY,eAIhB+B,KAAC,MAAI,CAAA3B,UAAU,6CACb6B,SAAA,QAAC,MACC,CAAAA,SAAA,CAAAF,EAAAA,KAAC,QAAM,CAAAyG,QAAQ,SAASpI,UAAU,+CAA+C6B,SAAA,CAAA,UACvEzB,EAAAA,IAAA,OAAA,CAAKJ,UAAU,eAAe6B,SAAC,SAEzC1B,EAAAC,IAACC,EAAA,CACCwF,GAAG,SACHpG,KAAK,SACLC,MAAOiG,EAASM,OAChBtG,SAtVO,CAACD,EAAOD,KACrBmG,EAAA,IACPD,EACHlG,CAACA,GAAOC,GACT,EAmViBY,QAlIE,CACpB,CAAEZ,MAAO,OAAQ2I,MAAO,aACxB,CAAE3I,MAAO,SAAU2I,MAAO,YAC1B,CAAE3I,MAAO,QAAS2I,MAAO,SAgIPzI,YAAY,iCAIf,MACC,CAAAiC,SAAA,CAAAF,EAAAA,KAAC,QAAM,CAAAyG,QAAQ,MAAMpI,UAAU,+CAA+C6B,SAAA,CAAA,QACtEzB,EAAAA,IAAA,OAAA,CAAKJ,UAAU,eAAe6B,SAAC,SAEvC1B,EAAAC,IAACb,EAAA,CACCC,KAAK,SACLqG,GAAG,MACHpG,KAAK,MACLC,MAAOiG,EAASO,IAChBvG,SAAUgH,EACV/G,YAAY,OACZmF,IAAI,KACJuD,IAAI,gBAKV3G,KAAC,MAAI,CAAA3B,UAAU,OACb6B,SAAA,CAAAzB,MAAC,QAAM,CAAAgI,QAAQ,QAAQpI,UAAU,+CAA+C6B,SAEhF,aACA1B,EAAAC,IAACb,EAAA,CACCC,KAAK,OACLqG,GAAG,QACHpG,KAAK,QACLC,MAAOiG,EAASQ,MAChBxG,SAAUgH,EACV/G,YAAY,kBAIhB+B,KAAC,MAAI,CAAA3B,UAAU,OACb6B,SAAA,CAAAzB,MAAC,QAAM,CAAAgI,QAAQ,YAAYpI,UAAU,+CAA+C6B,SAEpF,wBACA1B,EAAAC,IAACb,EAAA,CACCC,KAAK,OACLqG,GAAG,YACHpG,KAAK,YACLC,MAAOiG,EAASS,UAChBzG,SAAUgH,EACV/G,YAAY,6BAIhB+B,KAAC,MAAI,CAAA3B,UAAU,OACb6B,SAAA,CAAAzB,MAAC,QAAM,CAAAgI,QAAQ,WAAWpI,UAAU,+CAA+C6B,SAEnF,qBACA1B,EAAAC,IAACb,EAAA,CACCC,KAAK,OACLqG,GAAG,WACHpG,KAAK,WACLC,MAAOiG,EAASU,SAChB1G,SAAUgH,EACV/G,YAAY,0BAIhB+B,KAAC,MAAI,CAAA3B,UAAU,6BACb6B,SAAA,CAAA1B,EAAAC,IAACmH,EAAA,CACC/H,KAAK,SACLgI,QAAQ,UACR1F,QAASkF,EACVnF,SAAA,aAGD1B,EAAAC,IAACmH,EAAA,CACC/H,KAAK,SACLgI,QAAQ,UACR3H,SAAUwF,EAETxD,WAEGF,EAAAA,KAAA4G,EAAAA,SAAA,CAAA1G,SAAA,CAACF,EAAAA,KAAA,MAAA,CAAI3B,UAAU,6CAA6CiC,MAAM,6BAA6BE,KAAK,OAAOD,QAAQ,YACjHL,SAAA,GAAAzB,IAAC,SAAO,CAAAJ,UAAU,aAAawI,GAAG,KAAKC,GAAG,KAAKC,EAAE,KAAKC,OAAO,eAAeC,YAAY,YACvF,OAAK,CAAA5I,UAAU,aAAamC,KAAK,eAAeE,EAAE,uHAC/C,kBAIR,2BASlB"}