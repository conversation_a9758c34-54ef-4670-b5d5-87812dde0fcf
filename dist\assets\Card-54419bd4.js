import{j as s}from"./index-165d7974.js";const r=({children:r,className:a="",...e})=>s.jsx("div",{className:`bg-white rounded-lg shadow-sm border border-gray-200 ${a}`,...e,children:r}),a=({children:r,className:a="",...e})=>s.jsx("div",{className:`px-6 py-4 border-b border-gray-200 ${a}`,...e,children:r}),e=({children:r,className:a="",...e})=>s.jsx("div",{className:`px-6 py-4 ${a}`,...e,children:r}),d=({children:r,className:a="",...e})=>s.jsx("div",{className:`px-6 py-4 border-t border-gray-200 ${a}`,...e,children:r});export{r as C,a,e as b,d as c};
//# sourceMappingURL=Card-54419bd4.js.map
