{"version": 3, "file": "Card-54419bd4.js", "sources": ["../../src/components/ui/Card.jsx"], "sourcesContent": ["import React from 'react';\n\nexport const Card = ({ children, className = '', ...props }) => {\n  return (\n    <div \n      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport const CardHeader = ({ children, className = '', ...props }) => {\n  return (\n    <div \n      className={`px-6 py-4 border-b border-gray-200 ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport const CardBody = ({ children, className = '', ...props }) => {\n  return (\n    <div \n      className={`px-6 py-4 ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport const CardFooter = ({ children, className = '', ...props }) => {\n  return (\n    <div \n      className={`px-6 py-4 border-t border-gray-200 ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n"], "names": ["Card", "children", "className", "props", "jsxRuntimeExports", "jsx", "<PERSON><PERSON><PERSON><PERSON>", "CardBody", "<PERSON><PERSON><PERSON>er"], "mappings": "wCAEa,MAAAA,EAAO,EAAGC,WAAUC,YAAY,MAAOC,KAEhDC,EAAAC,IAAC,MAAA,CACCH,UAAW,wDAAwDA,OAC/DC,EAEHF,aAKMK,EAAa,EAAGL,WAAUC,YAAY,MAAOC,KAEtDC,EAAAC,IAAC,MAAA,CACCH,UAAW,sCAAsCA,OAC7CC,EAEHF,aAKMM,EAAW,EAAGN,WAAUC,YAAY,MAAOC,KAEpDC,EAAAC,IAAC,MAAA,CACCH,UAAW,aAAaA,OACpBC,EAEHF,aAKMO,EAAa,EAAGP,WAAUC,YAAY,MAAOC,KAEtDC,EAAAC,IAAC,MAAA,CACCH,UAAW,sCAAsCA,OAC7CC,EAEHF"}