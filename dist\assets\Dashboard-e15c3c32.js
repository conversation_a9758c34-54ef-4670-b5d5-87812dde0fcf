import{b as e,j as s}from"./index-165d7974.js";const t=()=>{const t=e((e=>e.auth.user));return s.jsxs("div",{children:[s.jsx("div",{className:"mb-6",children:s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:t?`Bienvenido, ${t.name}`:"Bienvenido a BAT-7"})}),(()=>{switch(null==t?void 0:t.role){case"admin":return s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Panel de Administrador"}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:l.map(((e,t)=>s.jsx(i,{...e},t)))})]});case"professional":return s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Panel de Profesional"}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:a.map(((e,t)=>s.jsx(i,{...e},t)))})]});default:return s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Mis Tests"}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:n.map(((e,t)=>s.jsx(i,{...e},t)))})]})}})(),s.jsxs("div",{className:"mt-10",children:[s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Actividad Reciente"}),s.jsx("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden",children:d.length>0?s.jsx("ul",{className:"divide-y divide-gray-200",children:d.map(((e,t)=>s.jsx("li",{className:"p-4 hover:bg-gray-50",children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${e.iconBg}`,children:s.jsx("i",{className:`fas fa-${e.icon} text-white`})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),s.jsx("p",{className:"text-sm text-gray-500",children:e.timestamp})]})]})},t)))}):s.jsx("div",{className:"p-4 text-center text-gray-500",children:"No hay actividad reciente"})})]})]})},i=({title:e,value:t,icon:i,iconBg:l,link:a})=>s.jsxs("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:[s.jsx("div",{className:"px-4 py-5 sm:p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:`flex-shrink-0 rounded-md p-3 ${l}`,children:s.jsx("i",{className:`fas fa-${i} text-white text-xl`})}),s.jsx("div",{className:"ml-5 w-0 flex-1",children:s.jsxs("dl",{children:[s.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e}),s.jsx("dd",{children:s.jsx("div",{className:"text-lg font-medium text-gray-900",children:t})})]})})]})}),a&&s.jsx("div",{className:"bg-gray-50 px-4 py-4 sm:px-6",children:s.jsx("div",{className:"text-sm",children:s.jsxs("a",{href:a.url,className:"font-medium text-blue-600 hover:text-blue-500",children:[a.text," ",s.jsx("span",{"aria-hidden":"true",children:"→"})]})})})]}),l=[{title:"Total de usuarios",value:"267",icon:"users",iconBg:"bg-blue-500",link:{text:"Ver todos los usuarios",url:"/admin/users"}},{title:"Instituciones",value:"12",icon:"building",iconBg:"bg-green-500",link:{text:"Administrar instituciones",url:"/admin/institutions"}},{title:"Tests completados",value:"1,254",icon:"clipboard-check",iconBg:"bg-purple-500",link:{text:"Ver todos los resultados",url:"/admin/reports"}}],a=[{title:"Candidatos",value:"42",icon:"user-tie",iconBg:"bg-blue-500",link:{text:"Gestionar candidatos",url:"/professional/candidates"}},{title:"Estudiantes activos",value:"38",icon:"users",iconBg:"bg-green-500",link:{text:"Ver todos los estudiantes",url:"/professional/students"}},{title:"Tests asignados",value:"18",icon:"clipboard-list",iconBg:"bg-yellow-500",link:{text:"Administrar tests",url:"/professional/tests"}},{title:"Tests completados",value:"127",icon:"clipboard-check",iconBg:"bg-green-500",link:{text:"Ver todos los resultados",url:"/professional/reports"}}],n=[{title:"Batería Completa BAT-7",value:"Pendiente",icon:"clipboard-list",iconBg:"bg-blue-500",link:{text:"Iniciar test",url:"/test/instructions/bat7"}},{title:"Test de Aptitud Verbal",value:"8 días restantes",icon:"file-alt",iconBg:"bg-yellow-500",link:{text:"Iniciar test",url:"/test/instructions/verbal"}}],d=[{title:"Has completado el Test de Aptitud Espacial",icon:"check-circle",iconBg:"bg-green-500",timestamp:"Hace 2 días"},{title:"Se te ha asignado un nuevo test: Batería Completa BAT-7",icon:"clipboard-list",iconBg:"bg-blue-500",timestamp:"Hace 3 días"},{title:"Has completado el Test de Razonamiento",icon:"check-circle",iconBg:"bg-green-500",timestamp:"Hace 1 semana"}];export{t as default};
//# sourceMappingURL=Dashboard-e15c3c32.js.map
