{"version": 3, "file": "Dashboard-e15c3c32.js", "sources": ["../../src/pages/dashboard/Dashboard.jsx"], "sourcesContent": ["import React from 'react';\nimport { useSelector } from 'react-redux';\n\nconst Dashboard = () => {\n  const user = useSelector(state => state.auth.user);\n\n  // Determinar el contenido basado en el rol\n  const renderRoleSpecificContent = () => {\n    switch (user?.role) {\n      case 'admin':\n        return (\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Panel de Administrador</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {adminCards.map((card, index) => (\n                <DashboardCard key={index} {...card} />\n              ))}\n            </div>\n          </div>\n        );\n      case 'professional':\n        return (\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Panel de Profesional</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {professionalCards.map((card, index) => (\n                <DashboardCard key={index} {...card} />\n              ))}\n            </div>\n          </div>\n        );\n      default:\n        return (\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Mis Tests</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {studentCards.map((card, index) => (\n                <DashboardCard key={index} {...card} />\n              ))}\n            </div>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div>\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">\n          {user ? `Bienvenido, ${user.name}` : 'Bienvenido a BAT-7'}\n        </h2>\n      </div>\n\n      {renderRoleSpecificContent()}\n\n      <div className=\"mt-10\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Actividad Reciente</h3>\n        <div className=\"bg-white shadow-sm rounded-lg overflow-hidden\">\n          {activityItems.length > 0 ? (\n            <ul className=\"divide-y divide-gray-200\">\n              {activityItems.map((item, index) => (\n                <li key={index} className=\"p-4 hover:bg-gray-50\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${item.iconBg}`}>\n                      <i className={`fas fa-${item.icon} text-white`}></i>\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 truncate\">\n                        {item.title}\n                      </p>\n                      <p className=\"text-sm text-gray-500\">\n                        {item.timestamp}\n                      </p>\n                    </div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n          ) : (\n            <div className=\"p-4 text-center text-gray-500\">\n              No hay actividad reciente\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Componente de tarjeta reutilizable\nconst DashboardCard = ({ title, value, icon, iconBg, link }) => {\n  return (\n    <div className=\"bg-white overflow-hidden shadow-sm rounded-lg\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <div className=\"flex items-center\">\n          <div className={`flex-shrink-0 rounded-md p-3 ${iconBg}`}>\n            <i className={`fas fa-${icon} text-white text-xl`}></i>\n          </div>\n          <div className=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt className=\"text-sm font-medium text-gray-500 truncate\">{title}</dt>\n              <dd>\n                <div className=\"text-lg font-medium text-gray-900\">{value}</div>\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      {link && (\n        <div className=\"bg-gray-50 px-4 py-4 sm:px-6\">\n          <div className=\"text-sm\">\n            <a href={link.url} className=\"font-medium text-blue-600 hover:text-blue-500\">\n              {link.text} <span aria-hidden=\"true\">&rarr;</span>\n            </a>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Datos de ejemplo para las tarjetas\nconst adminCards = [\n  {\n    title: 'Total de usuarios',\n    value: '267',\n    icon: 'users',\n    iconBg: 'bg-blue-500',\n    link: { text: 'Ver todos los usuarios', url: '/admin/users' }\n  },\n  {\n    title: 'Instituciones',\n    value: '12',\n    icon: 'building',\n    iconBg: 'bg-green-500',\n    link: { text: 'Administrar instituciones', url: '/admin/institutions' }\n  },\n  {\n    title: 'Tests completados',\n    value: '1,254',\n    icon: 'clipboard-check',\n    iconBg: 'bg-purple-500',\n    link: { text: 'Ver todos los resultados', url: '/admin/reports' }\n  }\n];\n\nconst professionalCards = [\n  {\n    title: 'Candidatos',\n    value: '42',\n    icon: 'user-tie',\n    iconBg: 'bg-blue-500',\n    link: { text: 'Gestionar candidatos', url: '/professional/candidates' }\n  },\n  {\n    title: 'Estudiantes activos',\n    value: '38',\n    icon: 'users',\n    iconBg: 'bg-green-500',\n    link: { text: 'Ver todos los estudiantes', url: '/professional/students' }\n  },\n  {\n    title: 'Tests asignados',\n    value: '18',\n    icon: 'clipboard-list',\n    iconBg: 'bg-yellow-500',\n    link: { text: 'Administrar tests', url: '/professional/tests' }\n  },\n  {\n    title: 'Tests completados',\n    value: '127',\n    icon: 'clipboard-check',\n    iconBg: 'bg-green-500',\n    link: { text: 'Ver todos los resultados', url: '/professional/reports' }\n  }\n];\n\nconst studentCards = [\n  {\n    title: 'Batería Completa BAT-7',\n    value: 'Pendiente',\n    icon: 'clipboard-list',\n    iconBg: 'bg-blue-500',\n    link: { text: 'Iniciar test', url: '/test/instructions/bat7' }\n  },\n  {\n    title: 'Test de Aptitud Verbal',\n    value: '8 días restantes',\n    icon: 'file-alt',\n    iconBg: 'bg-yellow-500',\n    link: { text: 'Iniciar test', url: '/test/instructions/verbal' }\n  }\n];\n\n// Datos de ejemplo para la actividad reciente\nconst activityItems = [\n  {\n    title: 'Has completado el Test de Aptitud Espacial',\n    icon: 'check-circle',\n    iconBg: 'bg-green-500',\n    timestamp: 'Hace 2 días'\n  },\n  {\n    title: 'Se te ha asignado un nuevo test: Batería Completa BAT-7',\n    icon: 'clipboard-list',\n    iconBg: 'bg-blue-500',\n    timestamp: 'Hace 3 días'\n  },\n  {\n    title: 'Has completado el Test de Razonamiento',\n    icon: 'check-circle',\n    iconBg: 'bg-green-500',\n    timestamp: 'Hace 1 semana'\n  }\n];\n\nexport default Dashboard;"], "names": ["Dashboard", "user", "useSelector", "state", "auth", "children", "jsx", "className", "jsxRuntimeExports", "name", "role", "map", "card", "index", "DashboardCard", "renderRoleSpecificContent", "jsxs", "length", "item", "iconBg", "icon", "title", "timestamp", "value", "link", "href", "url", "text", "adminCards", "professionalCards", "studentCards", "activityItems"], "mappings": "+CAGA,MAAMA,EAAY,KAChB,MAAMC,EAAOC,GAAqBC,GAAAA,EAAMC,KAAKH,OAyC7C,cACG,MACC,CAAAI,SAAA,CAAAC,MAAC,MAAI,CAAAC,UAAU,OACbF,SAAAG,EAAAF,IAAC,KAAG,CAAAC,UAAU,mCACXF,SAAAJ,EAAO,eAAeA,EAAKQ,OAAS,yBA1CX,MAChC,aAAQR,WAAMS,MACZ,IAAK,QACH,cACG,MACC,CAAAL,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGC,UAAU,yCAAyCF,SAAsB,2BAC5EC,EAAAA,IAAA,MAAA,CAAIC,UAAU,wCACZF,WAAWM,KAAI,CAACC,EAAMC,UACpBC,EAA2B,IAAGF,GAAXC,UAK9B,IAAK,eACH,cACG,MACC,CAAAR,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGC,UAAU,yCAAyCF,SAAoB,yBAC1EC,EAAAA,IAAA,MAAA,CAAIC,UAAU,wCACZF,WAAkBM,KAAI,CAACC,EAAMC,UAC3BC,EAA2B,IAAGF,GAAXC,UAK9B,QACE,cACG,MACC,CAAAR,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGC,UAAU,yCAAyCF,SAAS,cAC/DC,EAAAA,IAAA,MAAA,CAAIC,UAAU,wCACZF,WAAaM,KAAI,CAACC,EAAMC,UACtBC,EAA2B,IAAGF,GAAXC,UAKhC,EAWGE,KAEDC,KAAC,MAAI,CAAAT,UAAU,QACbF,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGC,UAAU,yCAAyCF,SAAkB,uBACzEC,EAAAA,IAAC,OAAIC,UAAU,gDACZF,WAAcY,OAAS,EACrBT,EAAAF,IAAA,KAAA,CAAGC,UAAU,2BACXF,WAAcM,KAAI,CAACO,EAAML,IACvBP,EAAAA,IAAA,KAAA,CAAeC,UAAU,uBACxBF,WAAAW,KAAC,MAAI,CAAAT,UAAU,8BACbF,SAAA,CAAAC,MAAC,MAAI,CAAAC,UAAW,2DAA2DW,EAAKC,SAC9Ed,WAACC,IAAA,IAAA,CAAEC,UAAW,UAAUW,EAAKE,wBAE/BJ,KAAC,MAAI,CAAAT,UAAU,iBACbF,SAAA,CAAAC,EAAAA,IAAC,IAAE,CAAAC,UAAU,6CACVF,SAAAa,EAAKG,QAEPf,EAAAA,IAAA,IAAA,CAAEC,UAAU,wBACVF,WAAKiB,mBAVLT,aAkBZ,MAAI,CAAAN,UAAU,gCAAgCF,SAAA,qCAMvD,EAKES,EAAgB,EAAGO,QAAOE,QAAOH,OAAMD,SAAQK,YAEjDR,KAAC,MAAI,CAAAT,UAAU,gDACbF,SAAA,CAAAC,EAAAA,IAAC,OAAIC,UAAU,mBACbF,SAACW,EAAAA,KAAA,MAAA,CAAIT,UAAU,oBACbF,SAAA,GAACC,IAAA,MAAA,CAAIC,UAAW,gCAAgCY,IAC9Cd,WAACC,IAAA,IAAA,CAAEC,UAAW,UAAUa,2BAEzBd,MAAA,MAAA,CAAIC,UAAU,kBACbF,gBAAC,KACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGC,UAAU,6CAA8CF,SAAMgB,UACjE,KACC,CAAAhB,SAAAG,EAAAF,IAAC,OAAIC,UAAU,oCAAqCF,yBAM7DmB,GACClB,EAAAA,IAAC,MAAI,CAAAC,UAAU,+BACbF,SAACC,EAAAA,IAAA,MAAA,CAAIC,UAAU,UACbF,gBAAC,IAAE,CAAAoB,KAAMD,EAAKE,IAAKnB,UAAU,gDAC1BF,SAAA,CAAKmB,EAAAG,KAAK,IAAErB,EAAAA,IAAA,OAAA,CAAK,cAAY,OAAOD,SAAM,gBAUnDuB,EAAa,CACjB,CACEP,MAAO,oBACPE,MAAO,MACPH,KAAM,QACND,OAAQ,cACRK,KAAM,CAAEG,KAAM,yBAA0BD,IAAK,iBAE/C,CACEL,MAAO,gBACPE,MAAO,KACPH,KAAM,WACND,OAAQ,eACRK,KAAM,CAAEG,KAAM,4BAA6BD,IAAK,wBAElD,CACEL,MAAO,oBACPE,MAAO,QACPH,KAAM,kBACND,OAAQ,gBACRK,KAAM,CAAEG,KAAM,2BAA4BD,IAAK,oBAI7CG,EAAoB,CACxB,CACER,MAAO,aACPE,MAAO,KACPH,KAAM,WACND,OAAQ,cACRK,KAAM,CAAEG,KAAM,uBAAwBD,IAAK,6BAE7C,CACEL,MAAO,sBACPE,MAAO,KACPH,KAAM,QACND,OAAQ,eACRK,KAAM,CAAEG,KAAM,4BAA6BD,IAAK,2BAElD,CACEL,MAAO,kBACPE,MAAO,KACPH,KAAM,iBACND,OAAQ,gBACRK,KAAM,CAAEG,KAAM,oBAAqBD,IAAK,wBAE1C,CACEL,MAAO,oBACPE,MAAO,MACPH,KAAM,kBACND,OAAQ,eACRK,KAAM,CAAEG,KAAM,2BAA4BD,IAAK,2BAI7CI,EAAe,CACnB,CACET,MAAO,yBACPE,MAAO,YACPH,KAAM,iBACND,OAAQ,cACRK,KAAM,CAAEG,KAAM,eAAgBD,IAAK,4BAErC,CACEL,MAAO,yBACPE,MAAO,mBACPH,KAAM,WACND,OAAQ,gBACRK,KAAM,CAAEG,KAAM,eAAgBD,IAAK,+BAKjCK,EAAgB,CACpB,CACEV,MAAO,6CACPD,KAAM,eACND,OAAQ,eACRG,UAAW,eAEb,CACED,MAAO,0DACPD,KAAM,iBACND,OAAQ,cACRG,UAAW,eAEb,CACED,MAAO,yCACPD,KAAM,eACND,OAAQ,eACRG,UAAW"}