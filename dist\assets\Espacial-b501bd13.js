import{j as a}from"./index-165d7974.js";import{C as s,a as e,b as t}from"./Card-54419bd4.js";import{B as l}from"./Button-9c521291.js";const i=()=>a.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[a.jsxs("div",{className:"mb-6",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Test de Aptitud Espacial"}),a.jsx("p",{className:"text-gray-600",children:"Esta sección está en desarrollo."})]}),a.jsxs(s,{children:[a.jsx(e,{children:a.jsx("h2",{className:"text-lg font-medium",children:"Información"})}),a.jsxs(t,{children:[a.jsx("p",{className:"text-gray-700 mb-4",children:"El test de aptitud espacial evalúa la capacidad para visualizar y manipular objetos en el espacio tridimensional. Este test se encuentra actualmente en desarrollo."}),a.jsx(l,{onClick:()=>window.history.back(),children:"Volver"})]})]})]});export{i as default};
//# sourceMappingURL=Espacial-b501bd13.js.map
