import{j as s}from"./index-165d7974.js";import{C as e,a as t,b as i}from"./Card-54419bd4.js";const a=()=>s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Administración de Instituciones"}),s.jsxs(e,{children:[s.jsx(t,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Lista de Instituciones"})}),s.jsx(i,{children:s.jsx("p",{className:"text-gray-600",children:"Esta sección permitirá gestionar las instituciones registradas en el sistema (componente en desarrollo)."})})]})]});export{a as default};
//# sourceMappingURL=Institutions-6f791b10.js.map
