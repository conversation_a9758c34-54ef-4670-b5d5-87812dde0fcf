{"version": 3, "file": "Institutions-6f791b10.js", "sources": ["../../src/pages/admin/Institutions.jsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\n\nconst Institutions = () => {\n  return (\n    <div className=\"container mx-auto py-6\">\n      <h1 className=\"text-2xl font-bold text-gray-800 mb-6\">Administración de Instituciones</h1>\n      \n      <Card>\n        <CardHeader>\n          <h2 className=\"text-lg font-medium\">Lista de Instituciones</h2>\n        </CardHeader>\n        <CardBody>\n          <p className=\"text-gray-600\">\n            Esta sección permitirá gestionar las instituciones registradas en el sistema (componente en desarrollo).\n          </p>\n        </CardBody>\n      </Card>\n    </div>\n  );\n};\n\nexport default Institutions;"], "names": ["Institutions", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody"], "mappings": "6FAGA,MAAMA,EAAe,MAEjBC,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAA+B,2CAEpFE,EACC,CAAAF,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,4CAErCK,EACC,CAAAL,SAAAI,EAAAH,IAAC,KAAEF,UAAU,gBAAgBC"}