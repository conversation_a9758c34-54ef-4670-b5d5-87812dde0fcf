import{c as e,u as a,r as s,j as t}from"./index-165d7974.js";import{C as i,a as n,b as r,c}from"./Card-54419bd4.js";import{B as l}from"./Button-9c521291.js";import{u as d}from"./useToast-cda9a5e1.js";const o=()=>{const{testId:o}=e(),m=a(),[p,u]=s.useState(null),[x,h]=s.useState(!0),[g,b]=s.useState(!1),{showInfo:j,showWarning:f,showError:y}=d();s.useEffect((()=>{(async()=>{try{if(await new Promise((e=>setTimeout(e,800))),"verbal"===o){u({id:"verbal",name:"Test de Aptitud Verbal",type:"verbal",description:"Evaluación de comprensión, razonamiento y habilidades lingüísticas",duration:30,numberOfQuestions:20,instructions:["Lee cada pregunta detenidamente antes de responder.","Presta especial atención a los textos de comprensión lectora, analizándolos en detalle.","En las preguntas de sinónimos y antónimos, considera todos los posibles significados de las palabras.","Para las analogías verbales, identifica la relación exacta entre el primer par de palabras.","En las preguntas de completar oraciones, lee la oración completa antes de seleccionar la respuesta.","Administra bien tu tiempo, dedicando más atención a los textos de comprensión lectora que son más extensos.","Si alguna pregunta te resulta muy difícil, márcala y continúa. Podrás volver a ella más tarde."],additionalInfo:"Este test evalúa tu capacidad para comprender conceptos expresados a través de palabras. Implica el dominio del lenguaje y la habilidad para entender relaciones entre conceptos verbales, procesando y analizando información expresada mediante el lenguaje.",components:[{name:"Sinónimos y Antónimos",description:"Evalúa tu vocabulario y comprensión de significados"},{name:"Analogías Verbales",description:"Mide tu capacidad para identificar relaciones entre conceptos"},{name:"Comprensión Lectora",description:"Evalúa tu habilidad para entender e interpretar textos"},{name:"Clasificación de Palabras",description:"Evalúa tu conocimiento de la estructura del lenguaje"},{name:"Completar Oraciones",description:"Mide tu sentido de la coherencia y contexto lingüístico"},{name:"Definiciones",description:"Evalúa tu precisión en la comprensión de términos"},{name:"Expresiones y Refranes",description:"Mide tu comprensión del lenguaje figurado"}],careerRelevance:[{career:"Derecho",relevance:"Alta",description:"Crucial para la interpretación de textos legales y argumentación"},{career:"Periodismo",relevance:"Alta",description:"Fundamental para la redacción y comunicación efectiva"},{career:"Educación",relevance:"Alta",description:"Esencial para la transmisión clara de conocimientos"},{career:"Psicología",relevance:"Media-Alta",description:"Importante para la comunicación terapéutica"},{career:"Marketing",relevance:"Media-Alta",description:"Valioso para la creación de mensajes persuasivos"},{career:"Ciencias",relevance:"Media",description:"Útil para la comunicación de conceptos complejos"},{career:"Ingeniería",relevance:"Media-Baja",description:"Complementario para documentación técnica"}]})}else{u({id:o,name:"Batería Completa BAT-7",type:"battery",description:"Evaluación completa de aptitudes y habilidades",duration:120,numberOfQuestions:150,instructions:["Lee atentamente cada pregunta antes de responder.","Responde a todas las preguntas, aunque no estés seguro/a de la respuesta.","Administra bien tu tiempo. Si una pregunta te resulta difícil, pasa a la siguiente y vuelve a ella más tarde.","No uses calculadora ni ningún otro dispositivo o material durante el test.","Una vez iniciado el test, no podrás pausarlo. Asegúrate de disponer del tiempo necesario para completarlo.","Responde con honestidad. Este test está diseñado para evaluar tus habilidades actuales."],additionalInfo:"La batería BAT-7 está compuesta por siete pruebas independientes que evalúan diferentes aptitudes: verbal, espacial, numérica, mecánica, razonamiento, atención y ortografía. Cada prueba tiene un tiempo específico de realización.",subtests:[{id:101,name:"Test Verbal",duration:30,questions:20,path:"verbal"},{id:102,name:"Test Espacial",duration:20,questions:25,path:"espacial"},{id:103,name:"Test de Atención",duration:15,questions:40,path:"atencion"},{id:104,name:"Test de Razonamiento",duration:20,questions:25,path:"razonamiento"},{id:105,name:"Test Numérico",duration:25,questions:30,path:"numerico"},{id:106,name:"Test Mecánico",duration:15,questions:20,path:"mecanico"},{id:107,name:"Test de Ortografía",duration:20,questions:40,path:"ortografia"}]})}h(!1)}catch(e){console.error("Error al cargar datos del test:",e),y("Error al cargar la información del test"),h(!1)}})()}),[o]);return t.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[t.jsxs("div",{className:"mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Instrucciones del Test"}),!x&&p&&t.jsx("p",{className:"text-gray-600",children:p.name})]}),x?t.jsx("div",{className:"py-16 text-center",children:t.jsxs("div",{className:"flex flex-col items-center justify-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),t.jsx("p",{className:"text-gray-500",children:"Cargando instrucciones del test..."})]})}):p?t.jsxs(t.Fragment,{children:[t.jsxs(i,{className:"mb-6",children:[t.jsx(n,{children:t.jsx("h2",{className:"text-lg font-medium",children:"Información General"})}),t.jsxs(r,{children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"text-md font-medium mb-2",children:"Descripción"}),t.jsx("p",{className:"text-gray-700",children:p.description})]}),t.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"text-md font-medium mb-2",children:"Duración"}),t.jsxs("p",{className:"text-gray-700",children:[p.duration," minutos"]})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-md font-medium mb-2",children:"Preguntas"}),t.jsxs("p",{className:"text-gray-700",children:[p.numberOfQuestions," preguntas"]})]})]})]}),p.additionalInfo&&t.jsx("div",{className:"bg-blue-50 border-l-4 border-blue-500 p-4 mb-4",children:t.jsx("p",{className:"text-blue-700",children:p.additionalInfo})}),"verbal"===p.type&&p.components&&t.jsxs("div",{className:"mt-6",children:[t.jsx("h3",{className:"text-md font-medium mb-3",children:"Componentes Evaluados"}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:p.components.map(((e,a)=>t.jsxs("div",{className:"border rounded p-3",children:[t.jsx("p",{className:"font-medium",children:e.name}),t.jsx("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]},a)))})]}),"verbal"===p.type&&p.careerRelevance&&t.jsxs("div",{className:"mt-6",children:[t.jsx("h3",{className:"text-md font-medium mb-3",children:"Relevancia para Carreras Profesionales"}),t.jsx("div",{className:"overflow-hidden border rounded-lg",children:t.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[t.jsx("thead",{className:"bg-gray-50",children:t.jsxs("tr",{children:[t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Carrera"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Relevancia"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Descripción"})]})}),t.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:p.careerRelevance.map(((e,a)=>t.jsxs("tr",{className:a%2==0?"bg-white":"bg-gray-50",children:[t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.career}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.jsx("span",{className:"px-2 py-1 rounded-full text-xs font-medium "+("Alta"===e.relevance?"bg-green-100 text-green-800":"Media-Alta"===e.relevance?"bg-blue-100 text-blue-800":"Media"===e.relevance?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.relevance})}),t.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.description})]},a)))})]})})]}),p.subtests&&p.subtests.length>0&&t.jsxs("div",{className:"mt-6",children:[t.jsx("h3",{className:"text-md font-medium mb-3",children:"Subtests"}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:p.subtests.map(((e,a)=>t.jsxs("div",{className:"border rounded p-3",children:[t.jsxs("p",{className:"font-medium",children:[a+1,". ",e.name]}),t.jsxs("div",{className:"flex justify-between mt-2 text-sm text-gray-600",children:[t.jsxs("span",{children:[e.duration," min"]}),t.jsxs("span",{children:[e.questions," preguntas"]})]})]},e.id)))})]})]})]}),t.jsxs(i,{className:"mb-6",children:[t.jsx(n,{children:t.jsx("h2",{className:"text-lg font-medium",children:"Instrucciones"})}),t.jsxs(r,{children:[t.jsx("ul",{className:"space-y-3",children:p.instructions.map(((e,a)=>t.jsxs("li",{className:"flex items-start",children:[t.jsx("div",{className:"flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 mr-3 mt-0.5",children:a+1}),t.jsx("p",{className:"text-gray-700",children:e})]},a)))}),"verbal"===p.type&&t.jsxs("div",{className:"mt-6 bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[t.jsx("h3",{className:"text-md font-medium text-yellow-800 mb-2",children:"Recomendaciones Adicionales"}),t.jsxs("ul",{className:"space-y-2 text-yellow-700",children:[t.jsx("li",{children:"• Concéntrate especialmente en la comprensión de los textos complejos, que suelen ser más desafiantes."}),t.jsx("li",{children:"• Intenta expandir tu vocabulario regularmente para mejorar en las secciones de sinónimos, antónimos y definiciones."}),t.jsx("li",{children:"• Practica la identificación de relaciones lógicas entre conceptos para mejorar en las analogías verbales."}),t.jsx("li",{children:"• Lee el contexto completo antes de responder preguntas de comprensión lectora."})]})]})]})]}),t.jsxs(i,{children:[t.jsx(r,{children:t.jsxs("div",{className:"flex items-start mb-4",children:[t.jsx("input",{type:"checkbox",id:"accept-conditions",checked:g,onChange:e=>b(e.target.checked),className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500 mt-1"}),t.jsx("label",{htmlFor:"accept-conditions",className:"ml-3 text-gray-700",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."})]})}),t.jsx(c,{className:"flex justify-end",children:t.jsx(l,{variant:g?"primary":"outline",onClick:()=>{if(g)if(j("Iniciando test..."),"battery"!==p.type)m(`/test/${p.type}`);else if(p.subtests&&p.subtests.length>0){const e=p.subtests[0];m(`/test/${e.path}`)}else m("/student/tests");else f("Debes aceptar las condiciones para continuar")},disabled:!g,children:"Iniciar Test"})})]})]}):t.jsx(i,{children:t.jsx(r,{children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-gray-500",children:"No se encontró información para el test solicitado."})})})})]})};export{o as default};
//# sourceMappingURL=Instructions-74de233b.js.map
