{"version": 3, "file": "Instructions-74de233b.js", "sources": ["../../src/pages/test/Instructions.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, CardHeader, CardBody, CardFooter } from '../../components/ui/Card';\nimport { Button } from '../../components/ui/Button';\nimport { useToast } from '../../hooks/useToast';\n\nconst Instructions = () => {\n  const { testId } = useParams();\n  const navigate = useNavigate();\n  const [test, setTest] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [accepted, setAccepted] = useState(false);\n  const { showInfo, showWarning, showError } = useToast();\n\n  useEffect(() => {\n    // Simular carga de datos del test\n    const fetchData = async () => {\n      try {\n        // Aquí se implementaría la llamada a la API para obtener el test\n        // Por ahora usamos datos de ejemplo\n        await new Promise(resolve => setTimeout(resolve, 800)); // Simular tiempo de carga\n        \n        // Verificamos si es una petición para un test específico\n        if (testId === \"verbal\") {\n          const verbalTest = {\n            id: \"verbal\",\n            name: 'Test de Aptitud Verbal',\n            type: 'verbal',\n            description: 'Evaluación de comprensión, razonamiento y habilidades lingüísticas',\n            duration: 30,\n            numberOfQuestions: 20,\n            instructions: [\n              'Lee cada pregunta detenidamente antes de responder.',\n              'Presta especial atención a los textos de comprensión lectora, analizándolos en detalle.',\n              'En las preguntas de sinónimos y antónimos, considera todos los posibles significados de las palabras.',\n              'Para las analogías verbales, identifica la relación exacta entre el primer par de palabras.',\n              'En las preguntas de completar oraciones, lee la oración completa antes de seleccionar la respuesta.',\n              'Administra bien tu tiempo, dedicando más atención a los textos de comprensión lectora que son más extensos.',\n              'Si alguna pregunta te resulta muy difícil, márcala y continúa. Podrás volver a ella más tarde.',\n            ],\n            additionalInfo: 'Este test evalúa tu capacidad para comprender conceptos expresados a través de palabras. Implica el dominio del lenguaje y la habilidad para entender relaciones entre conceptos verbales, procesando y analizando información expresada mediante el lenguaje.',\n            components: [\n              { name: 'Sinónimos y Antónimos', description: 'Evalúa tu vocabulario y comprensión de significados' },\n              { name: 'Analogías Verbales', description: 'Mide tu capacidad para identificar relaciones entre conceptos' },\n              { name: 'Comprensión Lectora', description: 'Evalúa tu habilidad para entender e interpretar textos' },\n              { name: 'Clasificación de Palabras', description: 'Evalúa tu conocimiento de la estructura del lenguaje' },\n              { name: 'Completar Oraciones', description: 'Mide tu sentido de la coherencia y contexto lingüístico' },\n              { name: 'Definiciones', description: 'Evalúa tu precisión en la comprensión de términos' },\n              { name: 'Expresiones y Refranes', description: 'Mide tu comprensión del lenguaje figurado' },\n            ],\n            careerRelevance: [\n              { career: 'Derecho', relevance: 'Alta', description: 'Crucial para la interpretación de textos legales y argumentación' },\n              { career: 'Periodismo', relevance: 'Alta', description: 'Fundamental para la redacción y comunicación efectiva' },\n              { career: 'Educación', relevance: 'Alta', description: 'Esencial para la transmisión clara de conocimientos' },\n              { career: 'Psicología', relevance: 'Media-Alta', description: 'Importante para la comunicación terapéutica' },\n              { career: 'Marketing', relevance: 'Media-Alta', description: 'Valioso para la creación de mensajes persuasivos' },\n              { career: 'Ciencias', relevance: 'Media', description: 'Útil para la comunicación de conceptos complejos' },\n              { career: 'Ingeniería', relevance: 'Media-Baja', description: 'Complementario para documentación técnica' },\n            ],\n          };\n          setTest(verbalTest);\n        } else {\n          // Datos para la batería completa\n          const mockTest = {\n            id: testId,\n            name: 'Batería Completa BAT-7',\n            type: 'battery',\n            description: 'Evaluación completa de aptitudes y habilidades',\n            duration: 120,\n            numberOfQuestions: 150,\n            instructions: [\n              'Lee atentamente cada pregunta antes de responder.',\n              'Responde a todas las preguntas, aunque no estés seguro/a de la respuesta.',\n              'Administra bien tu tiempo. Si una pregunta te resulta difícil, pasa a la siguiente y vuelve a ella más tarde.',\n              'No uses calculadora ni ningún otro dispositivo o material durante el test.',\n              'Una vez iniciado el test, no podrás pausarlo. Asegúrate de disponer del tiempo necesario para completarlo.',\n              'Responde con honestidad. Este test está diseñado para evaluar tus habilidades actuales.',\n            ],\n            additionalInfo: 'La batería BAT-7 está compuesta por siete pruebas independientes que evalúan diferentes aptitudes: verbal, espacial, numérica, mecánica, razonamiento, atención y ortografía. Cada prueba tiene un tiempo específico de realización.',\n            subtests: [\n              { id: 101, name: 'Test Verbal', duration: 30, questions: 20, path: 'verbal' },\n              { id: 102, name: 'Test Espacial', duration: 20, questions: 25, path: 'espacial' },\n              { id: 103, name: 'Test de Atención', duration: 15, questions: 40, path: 'atencion' },\n              { id: 104, name: 'Test de Razonamiento', duration: 20, questions: 25, path: 'razonamiento' },\n              { id: 105, name: 'Test Numérico', duration: 25, questions: 30, path: 'numerico' },\n              { id: 106, name: 'Test Mecánico', duration: 15, questions: 20, path: 'mecanico' },\n              { id: 107, name: 'Test de Ortografía', duration: 20, questions: 40, path: 'ortografia' },\n            ]\n          };\n          \n          setTest(mockTest);\n        }\n        \n        setLoading(false);\n      } catch (error) {\n        console.error('Error al cargar datos del test:', error);\n        showError('Error al cargar la información del test');\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [testId]);\n\n  const handleStartTest = () => {\n    if (!accepted) {\n      showWarning('Debes aceptar las condiciones para continuar');\n      return;\n    }\n    \n    // En un caso real, aquí se navegaría a la página del primer test o subtest\n    showInfo('Iniciando test...');\n    \n    // Si es un test específico, navegamos directamente a él\n    if (test.type !== 'battery') {\n      navigate(`/test/${test.type}`);\n    } \n    // Si es una batería, redirigimos al primer subtest\n    else if (test.subtests && test.subtests.length > 0) {\n      const firstTest = test.subtests[0];\n      navigate(`/test/${firstTest.path}`);\n    } else {\n      // Fallback\n      navigate('/student/tests');\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto py-6 max-w-4xl\">\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">Instrucciones del Test</h1>\n        {!loading && test && (\n          <p className=\"text-gray-600\">{test.name}</p>\n        )}\n      </div>\n\n      {loading ? (\n        <div className=\"py-16 text-center\">\n          <div className=\"flex flex-col items-center justify-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4\"></div>\n            <p className=\"text-gray-500\">Cargando instrucciones del test...</p>\n          </div>\n        </div>\n      ) : test ? (\n        <>\n          <Card className=\"mb-6\">\n            <CardHeader>\n              <h2 className=\"text-lg font-medium\">Información General</h2>\n            </CardHeader>\n            <CardBody>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <h3 className=\"text-md font-medium mb-2\">Descripción</h3>\n                  <p className=\"text-gray-700\">{test.description}</p>\n                </div>\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <h3 className=\"text-md font-medium mb-2\">Duración</h3>\n                    <p className=\"text-gray-700\">{test.duration} minutos</p>\n                  </div>\n                  <div>\n                    <h3 className=\"text-md font-medium mb-2\">Preguntas</h3>\n                    <p className=\"text-gray-700\">{test.numberOfQuestions} preguntas</p>\n                  </div>\n                </div>\n              </div>\n\n              {test.additionalInfo && (\n                <div className=\"bg-blue-50 border-l-4 border-blue-500 p-4 mb-4\">\n                  <p className=\"text-blue-700\">{test.additionalInfo}</p>\n                </div>\n              )}\n\n              {/* Componentes específicos del test verbal */}\n              {test.type === 'verbal' && test.components && (\n                <div className=\"mt-6\">\n                  <h3 className=\"text-md font-medium mb-3\">Componentes Evaluados</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                    {test.components.map((component, index) => (\n                      <div key={index} className=\"border rounded p-3\">\n                        <p className=\"font-medium\">{component.name}</p>\n                        <p className=\"text-sm text-gray-600 mt-1\">{component.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Relevancia para carreras en test verbal */}\n              {test.type === 'verbal' && test.careerRelevance && (\n                <div className=\"mt-6\">\n                  <h3 className=\"text-md font-medium mb-3\">Relevancia para Carreras Profesionales</h3>\n                  <div className=\"overflow-hidden border rounded-lg\">\n                    <table className=\"min-w-full divide-y divide-gray-200\">\n                      <thead className=\"bg-gray-50\">\n                        <tr>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Carrera</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Relevancia</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Descripción</th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {test.careerRelevance.map((career, index) => (\n                          <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">{career.career}</td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                career.relevance === 'Alta' ? 'bg-green-100 text-green-800' :\n                                career.relevance === 'Media-Alta' ? 'bg-blue-100 text-blue-800' :\n                                career.relevance === 'Media' ? 'bg-yellow-100 text-yellow-800' :\n                                'bg-gray-100 text-gray-800'\n                              }`}>\n                                {career.relevance}\n                              </span>\n                            </td>\n                            <td className=\"px-6 py-4 text-sm text-gray-500\">{career.description}</td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              )}\n\n              {/* Subtests para la batería completa */}\n              {test.subtests && test.subtests.length > 0 && (\n                <div className=\"mt-6\">\n                  <h3 className=\"text-md font-medium mb-3\">Subtests</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                    {test.subtests.map((subtest, index) => (\n                      <div key={subtest.id} className=\"border rounded p-3\">\n                        <p className=\"font-medium\">{index + 1}. {subtest.name}</p>\n                        <div className=\"flex justify-between mt-2 text-sm text-gray-600\">\n                          <span>{subtest.duration} min</span>\n                          <span>{subtest.questions} preguntas</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </CardBody>\n          </Card>\n\n          <Card className=\"mb-6\">\n            <CardHeader>\n              <h2 className=\"text-lg font-medium\">Instrucciones</h2>\n            </CardHeader>\n            <CardBody>\n              <ul className=\"space-y-3\">\n                {test.instructions.map((instruction, index) => (\n                  <li key={index} className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 mr-3 mt-0.5\">\n                      {index + 1}\n                    </div>\n                    <p className=\"text-gray-700\">{instruction}</p>\n                  </li>\n                ))}\n              </ul>\n              \n              {/* Información específica para test verbal */}\n              {test.type === 'verbal' && (\n                <div className=\"mt-6 bg-yellow-50 border-l-4 border-yellow-500 p-4\">\n                  <h3 className=\"text-md font-medium text-yellow-800 mb-2\">Recomendaciones Adicionales</h3>\n                  <ul className=\"space-y-2 text-yellow-700\">\n                    <li>• Concéntrate especialmente en la comprensión de los textos complejos, que suelen ser más desafiantes.</li>\n                    <li>• Intenta expandir tu vocabulario regularmente para mejorar en las secciones de sinónimos, antónimos y definiciones.</li>\n                    <li>• Practica la identificación de relaciones lógicas entre conceptos para mejorar en las analogías verbales.</li>\n                    <li>• Lee el contexto completo antes de responder preguntas de comprensión lectora.</li>\n                  </ul>\n                </div>\n              )}\n            </CardBody>\n          </Card>\n\n          <Card>\n            <CardBody>\n              <div className=\"flex items-start mb-4\">\n                <input\n                  type=\"checkbox\"\n                  id=\"accept-conditions\"\n                  checked={accepted}\n                  onChange={(e) => setAccepted(e.target.checked)}\n                  className=\"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500 mt-1\"\n                />\n                <label htmlFor=\"accept-conditions\" className=\"ml-3 text-gray-700\">\n                  He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad.\n                </label>\n              </div>\n            </CardBody>\n            <CardFooter className=\"flex justify-end\">\n              <Button\n                variant={accepted ? 'primary' : 'outline'}\n                onClick={handleStartTest}\n                disabled={!accepted}\n              >\n                Iniciar Test\n              </Button>\n            </CardFooter>\n          </Card>\n        </>\n      ) : (\n        <Card>\n          <CardBody>\n            <div className=\"py-8 text-center\">\n              <p className=\"text-gray-500\">No se encontró información para el test solicitado.</p>\n            </div>\n          </CardBody>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default Instructions;"], "names": ["Instructions", "testId", "useParams", "navigate", "useNavigate", "test", "setTest", "useState", "loading", "setLoading", "accepted", "setAccepted", "showInfo", "showWarning", "showError", "useToast", "useEffect", "async", "Promise", "resolve", "setTimeout", "id", "name", "type", "description", "duration", "numberOfQuestions", "instructions", "additionalInfo", "components", "careerRelevance", "career", "relevance", "subtests", "questions", "path", "error", "console", "jsxs", "className", "children", "jsx", "Fragment", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody", "map", "component", "index", "length", "subtest", "instruction", "checked", "onChange", "e", "target", "htmlFor", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "variant", "onClick", "firstTest", "disabled"], "mappings": "wMAMA,MAAMA,EAAe,KACb,MAAAC,OAAEA,GAAWC,IACbC,EAAWC,KACVC,EAAMC,GAAWC,WAAS,OAC1BC,EAASC,GAAcF,YAAS,IAChCG,EAAUC,GAAeJ,YAAS,IACnCK,SAAEA,EAAAC,YAAUA,EAAaC,UAAAA,GAAcC,IAE7CC,EAAAA,WAAU,KAEUC,WACZ,IAMF,SAHM,IAAIC,SAAQC,GAAWC,WAAWD,EAAS,OAGlC,WAAXlB,EAAqB,CAqCvBK,EApCmB,CACjBe,GAAI,SACJC,KAAM,yBACNC,KAAM,SACNC,YAAa,qEACbC,SAAU,GACVC,kBAAmB,GACnBC,aAAc,CACZ,sDACA,0FACA,wGACA,8FACA,sGACA,8GACA,kGAEFC,eAAgB,iQAChBC,WAAY,CACV,CAAEP,KAAM,wBAAyBE,YAAa,uDAC9C,CAAEF,KAAM,qBAAsBE,YAAa,iEAC3C,CAAEF,KAAM,sBAAuBE,YAAa,0DAC5C,CAAEF,KAAM,4BAA6BE,YAAa,wDAClD,CAAEF,KAAM,sBAAuBE,YAAa,2DAC5C,CAAEF,KAAM,eAAgBE,YAAa,qDACrC,CAAEF,KAAM,yBAA0BE,YAAa,8CAEjDM,gBAAiB,CACf,CAAEC,OAAQ,UAAWC,UAAW,OAAQR,YAAa,oEACrD,CAAEO,OAAQ,aAAcC,UAAW,OAAQR,YAAa,yDACxD,CAAEO,OAAQ,YAAaC,UAAW,OAAQR,YAAa,uDACvD,CAAEO,OAAQ,aAAcC,UAAW,aAAcR,YAAa,+CAC9D,CAAEO,OAAQ,YAAaC,UAAW,aAAcR,YAAa,oDAC7D,CAAEO,OAAQ,WAAYC,UAAW,QAASR,YAAa,oDACvD,CAAEO,OAAQ,aAAcC,UAAW,aAAcR,YAAa,+CAGhD,KACb,CA6BLlB,EA3BiB,CACfe,GAAIpB,EACJqB,KAAM,yBACNC,KAAM,UACNC,YAAa,iDACbC,SAAU,IACVC,kBAAmB,IACnBC,aAAc,CACZ,oDACA,4EACA,gHACA,6EACA,6GACA,2FAEFC,eAAgB,uOAChBK,SAAU,CACR,CAAEZ,GAAI,IAAKC,KAAM,cAAeG,SAAU,GAAIS,UAAW,GAAIC,KAAM,UACnE,CAAEd,GAAI,IAAKC,KAAM,gBAAiBG,SAAU,GAAIS,UAAW,GAAIC,KAAM,YACrE,CAAEd,GAAI,IAAKC,KAAM,mBAAoBG,SAAU,GAAIS,UAAW,GAAIC,KAAM,YACxE,CAAEd,GAAI,IAAKC,KAAM,uBAAwBG,SAAU,GAAIS,UAAW,GAAIC,KAAM,gBAC5E,CAAEd,GAAI,IAAKC,KAAM,gBAAiBG,SAAU,GAAIS,UAAW,GAAIC,KAAM,YACrE,CAAEd,GAAI,IAAKC,KAAM,gBAAiBG,SAAU,GAAIS,UAAW,GAAIC,KAAM,YACrE,CAAEd,GAAI,IAAKC,KAAM,qBAAsBG,SAAU,GAAIS,UAAW,GAAIC,KAAM,gBAKhF,CAEA1B,GAAW,SACJ2B,GACCC,QAAAD,MAAM,kCAAmCA,GACjDtB,EAAU,2CACVL,GAAW,EACb,QAID,CAACR,IA0BF,SAAAqC,KAAC,MAAI,CAAAC,UAAU,mCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,OACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAsB,4BAC1EhC,GAAWH,GACXoC,EAAAA,IAAC,KAAEF,UAAU,gBAAiBC,WAAKlB,UAItCd,QACE,MAAI,CAAA+B,UAAU,oBACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,4CACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,wEACdE,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAAkC,4CAGjEnC,EAEAiC,EAAAA,KAAAI,EAAAA,SAAA,CAAAF,SAAA,GAACF,KAAAK,EAAA,CAAKJ,UAAU,OACdC,SAAA,CAAAC,EAAAA,IAACG,GACCJ,SAACK,EAAAJ,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,0CAErCM,EACC,CAAAN,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,6CACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,2BAA2BC,SAAW,gBACnDC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAiBC,WAAKhB,mBAErCc,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,2BAA2BC,SAAQ,eACjDF,KAAC,IAAE,CAAAC,UAAU,gBAAiBC,SAAA,CAAKnC,EAAAoB,SAAS,wBAE7C,MACC,CAAAe,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,2BAA2BC,SAAS,gBAClDF,KAAC,IAAE,CAAAC,UAAU,gBAAiBC,SAAA,CAAKnC,EAAAqB,kBAAkB,yBAK1DrB,EAAKuB,sBACH,MAAA,CAAIW,UAAU,iDACbC,SAACC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAiBC,SAAKnC,EAAAuB,mBAKxB,WAAdvB,EAAKkB,MAAqBlB,EAAKwB,mBAC7B,MAAA,CAAIU,UAAU,OACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,2BAA2BC,SAAqB,4BAC7DC,IAAA,MAAA,CAAIF,UAAU,wCACZC,SAAKnC,EAAAwB,WAAWkB,KAAI,CAACC,EAAWC,WAC9B,MAAA,CAAgBV,UAAU,qBACzBC,SAAA,CAAAC,EAAAA,IAAC,IAAE,CAAAF,UAAU,cAAeC,SAAAQ,EAAU1B,OACrCmB,EAAAA,IAAA,IAAA,CAAEF,UAAU,6BAA8BC,WAAUhB,gBAF7CyB,UAUH,WAAd5C,EAAKkB,MAAqBlB,EAAKyB,wBAC7B,MAAA,CAAIS,UAAU,OACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,2BAA2BC,SAAsC,iDAC9E,MAAI,CAAAD,UAAU,oCACbC,SAACF,EAAAA,KAAA,QAAA,CAAMC,UAAU,sCACfC,SAAA,CAAAC,MAAC,QAAM,CAAAF,UAAU,aACfC,SAAAK,EAAAP,KAAC,KACC,CAAAE,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,iFAAiFC,SAAO,YACrGC,EAAAA,IAAA,KAAA,CAAGF,UAAU,iFAAiFC,SAAU,eACxGC,EAAAA,IAAA,KAAA,CAAGF,UAAU,iFAAiFC,SAAW,2BAG7G,QAAM,CAAAD,UAAU,oCACdC,SAAAnC,EAAKyB,gBAAgBiB,KAAI,CAAChB,EAAQkB,WAChC,KAAe,CAAAV,UAAWU,EAAQ,GAAM,EAAI,WAAa,aACxDT,SAAA,CAAAC,EAAAA,IAAC,KAAG,CAAAF,UAAU,gEAAiEC,SAAAT,EAAOA,SACtFU,EAAAA,IAAC,KAAG,CAAAF,UAAU,oDACZC,SAAAK,EAAAJ,IAAC,QAAKF,UAAW,+CACM,SAArBR,EAAOC,UAAuB,8BACT,eAArBD,EAAOC,UAA6B,4BACf,UAArBD,EAAOC,UAAwB,gCAC/B,6BAECQ,SAAOT,EAAAC,cAGXS,EAAAA,IAAA,KAAA,CAAGF,UAAU,kCAAmCC,WAAOhB,gBAZjDyB,eAsBpB5C,EAAK4B,UAAY5B,EAAK4B,SAASiB,OAAS,GACvCL,EAAAP,KAAC,MAAI,CAAAC,UAAU,OACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,2BAA2BC,SAAQ,eAChDC,IAAA,MAAA,CAAIF,UAAU,uDACZC,SAAKnC,EAAA4B,SAASc,KAAI,CAACI,EAASF,WAC1B,MAAA,CAAqBV,UAAU,qBAC9BC,SAAA,GAACF,KAAA,IAAA,CAAEC,UAAU,cAAeC,SAAA,CAAQS,EAAA,EAAE,KAAGE,EAAQ7B,UACjDgB,KAAC,MAAI,CAAAC,UAAU,kDACbC,SAAA,QAAC,OAAM,CAAAA,SAAA,CAAQW,EAAA1B,SAAS,iBACvB,OAAM,CAAAe,SAAA,CAAQW,EAAAjB,UAAU,qBAJnBiB,EAAQ9B,mBAc9BiB,KAACK,EAAK,CAAAJ,UAAU,OACdC,SAAA,CAAAC,EAAAA,IAACG,GACCJ,SAACK,EAAAJ,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,oCAErCM,EACC,CAAAN,SAAA,GAAAC,IAAC,KAAG,CAAAF,UAAU,YACXC,SAAAnC,EAAKsB,aAAaoB,KAAI,CAACK,EAAaH,WAClC,KAAe,CAAAV,UAAU,mBACxBC,SAAA,CAAAC,EAAAA,IAAC,MAAI,CAAAF,UAAU,4GACZC,SAAAS,EAAQ,IAEVR,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAiBC,SAAYY,MAJnCH,OAUE,WAAd5C,EAAKkB,MACHe,EAAAA,KAAA,MAAA,CAAIC,UAAU,qDACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,2CAA2CC,SAA2B,kCACpFF,KAAC,KAAG,CAAAC,UAAU,4BACZC,SAAA,GAAAC,IAAC,MAAGD,SAAsG,6GAC1GC,IAAC,MAAGD,SAAoH,2HACxHC,IAAC,MAAGD,SAA0G,iHAC9GC,IAAC,MAAGD,SAA+E,uGAO5FG,EACC,CAAAH,SAAA,CAAAC,MAACK,EACC,CAAAN,SAAAF,EAAAA,KAAC,MAAI,CAAAC,UAAU,wBACbC,SAAA,CAAAK,EAAAJ,IAAC,QAAA,CACClB,KAAK,WACLF,GAAG,oBACHgC,QAAS3C,EACT4C,SAAWC,GAAM5C,EAAY4C,EAAEC,OAAOH,SACtCd,UAAU,iFAEX,QAAM,CAAAkB,QAAQ,oBAAoBlB,UAAU,qBAAqBC,SAElE,8IAGJC,IAACiB,EAAW,CAAAnB,UAAU,mBACpBC,SAAAK,EAAAJ,IAACkB,EAAA,CACCC,QAASlD,EAAW,UAAY,UAChCmD,QA7LU,KACtB,GAAKnD,EASD,GAHJE,EAAS,qBAGS,YAAdP,EAAKkB,KACEpB,EAAA,SAASE,EAAKkB,gBAGhBlB,EAAK4B,UAAY5B,EAAK4B,SAASiB,OAAS,EAAG,CAC5C,MAAAY,EAAYzD,EAAK4B,SAAS,GACvB9B,EAAA,SAAS2D,EAAU3B,OAAM,MAGlChC,EAAS,uBAjBTU,EAAY,+CAkBd,EA0KYkD,UAAWrD,EACZ8B,SAAA,yBAONC,EAAAA,IAAAE,EAAA,CACCH,SAACC,MAAAK,EAAA,CACCN,eAAC,MAAI,CAAAD,UAAU,mBACbC,SAAAC,EAAAA,IAAC,KAAEF,UAAU,gBAAgBC,SAAmD,gEAK1F"}