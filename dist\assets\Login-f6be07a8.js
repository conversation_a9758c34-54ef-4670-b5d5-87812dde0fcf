import{r as e,u as s,a,j as r,L as t,s as i,l}from"./index-165d7974.js";const n=()=>{const[n,o]=e.useState({email:"",password:""}),[d,c]=e.useState(!1),[m,x]=e.useState(""),[u,f]=e.useState(!1),b=s(),h=a(),p=e=>{const{name:s,value:a}=e.target;o((e=>({...e,[s]:a})))};return r.jsxs("div",{className:"min-h-screen bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[r.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[r.jsxs("h2",{className:"text-center text-3xl font-extrabold text-gray-900",children:["Activatu",r.jsx("span",{className:"text-[#ffda0a]",children:"mente"})]}),r.jsx("h3",{className:"mt-2 text-center text-xl text-gray-600",children:"BAT-7"}),r.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Plataforma de Evaluación Psicométrica"})]}),r.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:r.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[r.jsxs("form",{className:"space-y-6",onSubmit:async e=>{var s,a;e.preventDefault(),f(!0),x("");try{if(!n.email||!n.password)throw new Error("Por favor ingrese email y contraseña");const e="<EMAIL>"===n.email||"<EMAIL>"===n.email||"<EMAIL>"===n.email;let t="student";n.email.includes("admin")?t="admin":n.email.includes("profesional")&&(t="professional");let o,c,m="Usuario";if(m="admin"===t?"Administrador Test":"professional"===t?"Profesional Test":"Estudiante Test",e)o={id:`test-${Date.now()}`,name:m,email:n.email,role:t},c=`simulated-token-${Date.now()}`,console.log("Login successful (simulated)",{userData:o});else try{const{data:e,error:r}=await i.auth.signInWithPassword({email:n.email,password:n.password});if(r)throw r;const{user:l,session:d}=e;t=(null==(s=l.user_metadata)?void 0:s.role)||"student",m=(null==(a=l.user_metadata)?void 0:a.name)||"Usuario",o={id:l.id,name:m,email:l.email,role:t},c=d.access_token,console.log("Login successful (Supabase)",{userData:o})}catch(r){if(console.error("Error de Supabase:",r),"Email not confirmed"===r.message)throw new Error("El correo electrónico no ha sido confirmado. Por favor, verifica tu bandeja de entrada o usa un usuario de prueba.");throw r}d?(localStorage.setItem("user",JSON.stringify(o)),localStorage.setItem("token",c)):(sessionStorage.setItem("user",JSON.stringify(o)),sessionStorage.setItem("token",c)),h(l({user:o,token:c})),b("admin"===t?"/admin/dashboard":"professional"===t?"/professional/dashboard":"/student/dashboard")}catch(t){console.error("Error de inicio de sesión:",t),x(t.message||"Error al iniciar sesión")}finally{f(!1)}},children:[m&&r.jsx("div",{className:"bg-red-50 border-l-4 border-red-400 p-4",children:r.jsx("p",{className:"text-sm text-red-700",children:m})}),r.jsxs("div",{children:[r.jsx("label",{htmlFor:"email",className:"form-label",children:"Correo electrónico"}),r.jsx("div",{className:"mt-1",children:r.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"form-input",onChange:p,value:n.email})})]}),r.jsxs("div",{children:[r.jsx("label",{htmlFor:"password",className:"form-label",children:"Contraseña"}),r.jsx("div",{className:"mt-1",children:r.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"form-input",onChange:p,value:n.password})})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center",children:[r.jsx("input",{id:"remember_me",name:"remember_me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:d,onChange:e=>c(e.target.checked)}),r.jsx("label",{htmlFor:"remember_me",className:"ml-2 block text-sm text-gray-900",children:"Recordarme"})]}),r.jsx("div",{className:"text-sm",children:r.jsx("a",{href:"#",className:"font-medium text-primary-600 hover:text-primary-500",children:"¿Olvidó su contraseña?"})})]}),r.jsx("div",{children:r.jsx("button",{type:"submit",disabled:u,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:u?"Iniciando sesión...":"Iniciar sesión"})})]}),r.jsxs("div",{className:"mt-6",children:[r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-0 flex items-center",children:r.jsx("div",{className:"w-full border-t border-gray-300"})}),r.jsx("div",{className:"relative flex justify-center text-sm",children:r.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Usuarios de prueba"})})]}),r.jsxs("div",{className:"mt-4 bg-blue-50 p-4 rounded-md border border-blue-200",children:[r.jsxs("div",{className:"flex items-center mb-2",children:[r.jsx("i",{className:"fas fa-info-circle text-blue-500 mr-2"}),r.jsx("h3",{className:"text-sm font-medium text-blue-700",children:"Cuentas para pruebas (Acceso Directo)"})]}),r.jsxs("div",{className:"space-y-3 text-xs text-gray-600",children:[r.jsx("div",{className:"bg-white p-2 rounded border border-gray-200 hover:border-blue-300 transition-colors",children:r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsxs("div",{className:"flex items-center",children:[r.jsx("span",{className:"inline-flex items-center justify-center h-6 w-6 rounded-full bg-red-100 text-red-600 mr-2",children:r.jsx("i",{className:"fas fa-user-shield text-xs"})}),r.jsx("span",{className:"font-medium",children:"Administrador"})]}),r.jsxs("button",{type:"button",className:"bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-2 rounded text-xs transition-colors",onClick:()=>{o({email:"<EMAIL>",password:"Admin123!"}),c(!0)},children:[r.jsx("i",{className:"fas fa-sign-in-alt mr-1"})," Acceder como Admin"]})]})}),r.jsx("div",{className:"bg-white p-2 rounded border border-gray-200 hover:border-blue-300 transition-colors",children:r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsxs("div",{className:"flex items-center",children:[r.jsx("span",{className:"inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 mr-2",children:r.jsx("i",{className:"fas fa-user-md text-xs"})}),r.jsx("span",{className:"font-medium",children:"Profesional"})]}),r.jsxs("button",{type:"button",className:"bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-2 rounded text-xs transition-colors",onClick:()=>{o({email:"<EMAIL>",password:"Prof123!"}),c(!0)},children:[r.jsx("i",{className:"fas fa-sign-in-alt mr-1"})," Acceder como Profesional"]})]})}),r.jsx("div",{className:"bg-white p-2 rounded border border-gray-200 hover:border-blue-300 transition-colors",children:r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsxs("div",{className:"flex items-center",children:[r.jsx("span",{className:"inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-600 mr-2",children:r.jsx("i",{className:"fas fa-user-graduate text-xs"})}),r.jsx("span",{className:"font-medium",children:"Estudiante"})]}),r.jsxs("button",{type:"button",className:"bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-2 rounded text-xs transition-colors",onClick:()=>{o({email:"<EMAIL>",password:"Estud123!"}),c(!0)},children:[r.jsx("i",{className:"fas fa-sign-in-alt mr-1"})," Acceder como Estudiante"]})]})}),r.jsx("div",{className:"bg-yellow-50 p-2 rounded border border-yellow-200 mt-3",children:r.jsxs("p",{className:"text-yellow-700 text-xs flex items-start",children:[r.jsx("i",{className:"fas fa-exclamation-triangle mr-2 mt-0.5"}),r.jsxs("span",{children:[r.jsx("strong",{children:"Nota:"})," Estos usuarios son solo para pruebas y no requieren confirmación de correo electrónico."]})]})})]})]}),r.jsxs("div",{className:"relative mt-6",children:[r.jsx("div",{className:"absolute inset-0 flex items-center",children:r.jsx("div",{className:"w-full border-t border-gray-300"})}),r.jsx("div",{className:"relative flex justify-center text-sm",children:r.jsx("span",{className:"px-2 bg-white text-gray-500",children:"¿No tiene una cuenta?"})})]}),r.jsx("div",{className:"mt-6",children:r.jsx(t,{to:"/register",className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"Registrarse"})})]})]})})]})};export{n as default};
//# sourceMappingURL=Login-f6be07a8.js.map
