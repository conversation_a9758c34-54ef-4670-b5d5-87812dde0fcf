{"version": 3, "file": "Login-f6be07a8.js", "sources": ["../../src/pages/auth/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { loginSuccess } from '../../store/slices/authSlice';\nimport { supabase } from '../../api/supabase';\n\nconst Login = () => {\n  const [credentials, setCredentials] = useState({\n    email: '',\n    password: ''\n  });\n  const [remember, setRemember] = useState(false);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setCredentials(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      // Validación básica\n      if (!credentials.email || !credentials.password) {\n        throw new Error('Por favor ingrese email y contraseña');\n      }\n\n      // Verificar si estamos usando usuarios de prueba\n      const isTestUser =\n        credentials.email === '<EMAIL>' ||\n        credentials.email === '<EMAIL>' ||\n        credentials.email === '<EMAIL>';\n\n      // Determinar el rol basado en el correo electrónico\n      let role = 'student';\n      if (credentials.email.includes('admin')) {\n        role = 'admin';\n      } else if (credentials.email.includes('profesional')) {\n        role = 'professional';\n      }\n\n      // Nombre basado en el rol\n      let name = 'Usuario';\n      if (role === 'admin') {\n        name = 'Administrador Test';\n      } else if (role === 'professional') {\n        name = 'Profesional Test';\n      } else {\n        name = 'Estudiante Test';\n      }\n\n      let userData;\n      let token;\n\n      // Si es un usuario de prueba, usar autenticación simulada\n      if (isTestUser) {\n        // Simular datos de usuario\n        userData = {\n          id: `test-${Date.now()}`,\n          name: name,\n          email: credentials.email,\n          role: role\n        };\n\n        token = `simulated-token-${Date.now()}`;\n\n        console.log('Login successful (simulated)', { userData });\n      } else {\n        // Intentar iniciar sesión con Supabase para usuarios reales\n        try {\n          const { data, error } = await supabase.auth.signInWithPassword({\n            email: credentials.email,\n            password: credentials.password,\n          });\n\n          if (error) {\n            throw error;\n          }\n\n          // Obtener datos del usuario\n          const { user, session } = data;\n\n          // Extraer el rol del usuario de los metadatos\n          role = user.user_metadata?.role || 'student';\n          name = user.user_metadata?.name || 'Usuario';\n\n          // Crear objeto de usuario para Redux\n          userData = {\n            id: user.id,\n            name: name,\n            email: user.email,\n            role: role\n          };\n\n          token = session.access_token;\n\n          console.log('Login successful (Supabase)', { userData });\n        } catch (supabaseError) {\n          console.error('Error de Supabase:', supabaseError);\n\n          // Si hay un error de correo no confirmado, mostrar un mensaje específico\n          if (supabaseError.message === 'Email not confirmed') {\n            throw new Error('El correo electrónico no ha sido confirmado. Por favor, verifica tu bandeja de entrada o usa un usuario de prueba.');\n          }\n\n          throw supabaseError;\n        }\n      }\n\n      // Guardar usuario en localStorage si \"recordarme\" está activo\n      if (remember) {\n        localStorage.setItem('user', JSON.stringify(userData));\n        localStorage.setItem('token', token);\n      } else {\n        sessionStorage.setItem('user', JSON.stringify(userData));\n        sessionStorage.setItem('token', token);\n      }\n\n      // Dispatch para actualizar el estado Redux con la información del usuario\n      dispatch(loginSuccess({\n        user: userData,\n        token: token\n      }));\n\n      // Redirigir según el rol\n      if (role === 'admin') {\n        navigate('/admin/dashboard');\n      } else if (role === 'professional') {\n        navigate('/professional/dashboard');\n      } else {\n        navigate('/student/dashboard');\n      }\n    } catch (error) {\n      console.error('Error de inicio de sesión:', error);\n      setError(error.message || 'Error al iniciar sesión');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <h2 className=\"text-center text-3xl font-extrabold text-gray-900\">\n          Activatu<span className=\"text-[#ffda0a]\">mente</span>\n        </h2>\n        <h3 className=\"mt-2 text-center text-xl text-gray-600\">BAT-7</h3>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Plataforma de Evaluación Psicométrica\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border-l-4 border-red-400 p-4\">\n                <p className=\"text-sm text-red-700\">{error}</p>\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"form-label\">\n                Correo electrónico\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  className=\"form-input\"\n                  onChange={handleChange}\n                  value={credentials.email}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"form-label\">\n                Contraseña\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"current-password\"\n                  required\n                  className=\"form-input\"\n                  onChange={handleChange}\n                  value={credentials.password}\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"remember_me\"\n                  name=\"remember_me\"\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  checked={remember}\n                  onChange={(e) => setRemember(e.target.checked)}\n                />\n                <label htmlFor=\"remember_me\" className=\"ml-2 block text-sm text-gray-900\">\n                  Recordarme\n                </label>\n              </div>\n\n              <div className=\"text-sm\">\n                <a href=\"#\" className=\"font-medium text-primary-600 hover:text-primary-500\">\n                  ¿Olvidó su contraseña?\n                </a>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                {loading ? 'Iniciando sesión...' : 'Iniciar sesión'}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\"></div>\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">\n                  Usuarios de prueba\n                </span>\n              </div>\n            </div>\n\n            <div className=\"mt-4 bg-blue-50 p-4 rounded-md border border-blue-200\">\n              <div className=\"flex items-center mb-2\">\n                <i className=\"fas fa-info-circle text-blue-500 mr-2\"></i>\n                <h3 className=\"text-sm font-medium text-blue-700\">Cuentas para pruebas (Acceso Directo)</h3>\n              </div>\n              <div className=\"space-y-3 text-xs text-gray-600\">\n                <div className=\"bg-white p-2 rounded border border-gray-200 hover:border-blue-300 transition-colors\">\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <span className=\"inline-flex items-center justify-center h-6 w-6 rounded-full bg-red-100 text-red-600 mr-2\">\n                        <i className=\"fas fa-user-shield text-xs\"></i>\n                      </span>\n                      <span className=\"font-medium\">Administrador</span>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-2 rounded text-xs transition-colors\"\n                      onClick={() => {\n                        setCredentials({\n                          email: '<EMAIL>',\n                          password: 'Admin123!'\n                        });\n                        setRemember(true);\n                      }}\n                    >\n                      <i className=\"fas fa-sign-in-alt mr-1\"></i> Acceder como Admin\n                    </button>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-2 rounded border border-gray-200 hover:border-blue-300 transition-colors\">\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <span className=\"inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 mr-2\">\n                        <i className=\"fas fa-user-md text-xs\"></i>\n                      </span>\n                      <span className=\"font-medium\">Profesional</span>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-2 rounded text-xs transition-colors\"\n                      onClick={() => {\n                        setCredentials({\n                          email: '<EMAIL>',\n                          password: 'Prof123!'\n                        });\n                        setRemember(true);\n                      }}\n                    >\n                      <i className=\"fas fa-sign-in-alt mr-1\"></i> Acceder como Profesional\n                    </button>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-2 rounded border border-gray-200 hover:border-blue-300 transition-colors\">\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <span className=\"inline-flex items-center justify-center h-6 w-6 rounded-full bg-green-100 text-green-600 mr-2\">\n                        <i className=\"fas fa-user-graduate text-xs\"></i>\n                      </span>\n                      <span className=\"font-medium\">Estudiante</span>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-2 rounded text-xs transition-colors\"\n                      onClick={() => {\n                        setCredentials({\n                          email: '<EMAIL>',\n                          password: 'Estud123!'\n                        });\n                        setRemember(true);\n                      }}\n                    >\n                      <i className=\"fas fa-sign-in-alt mr-1\"></i> Acceder como Estudiante\n                    </button>\n                  </div>\n                </div>\n\n                <div className=\"bg-yellow-50 p-2 rounded border border-yellow-200 mt-3\">\n                  <p className=\"text-yellow-700 text-xs flex items-start\">\n                    <i className=\"fas fa-exclamation-triangle mr-2 mt-0.5\"></i>\n                    <span>\n                      <strong>Nota:</strong> Estos usuarios son solo para pruebas y no requieren confirmación de correo electrónico.\n                    </span>\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative mt-6\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\"></div>\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">\n                  ¿No tiene una cuenta?\n                </span>\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <Link\n                to=\"/register\"\n                className=\"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                Registrarse\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "names": ["<PERSON><PERSON>", "credentials", "setCredentials", "useState", "email", "password", "remember", "setRemember", "error", "setError", "loading", "setLoading", "navigate", "useNavigate", "dispatch", "useDispatch", "handleChange", "e", "name", "value", "target", "prev", "jsxs", "className", "children", "jsx", "onSubmit", "async", "preventDefault", "Error", "isTestUser", "role", "includes", "userData", "token", "id", "Date", "now", "console", "log", "data", "supabase", "auth", "signInWithPassword", "user", "session", "_a", "user_metadata", "_b", "access_token", "supabaseError", "message", "localStorage", "setItem", "JSON", "stringify", "sessionStorage", "loginSuccess", "jsxRuntimeExports", "htmlFor", "type", "autoComplete", "required", "onChange", "checked", "href", "disabled", "onClick", "Link", "to"], "mappings": "wEAMA,MAAMA,EAAQ,KACZ,MAAOC,EAAaC,GAAkBC,WAAS,CAC7CC,MAAO,GACPC,SAAU,MAELC,EAAUC,GAAeJ,YAAS,IAClCK,EAAOC,GAAYN,WAAS,KAC5BO,EAASC,GAAcR,YAAS,GAEjCS,EAAWC,IACXC,EAAWC,IAEXC,EAAgBC,IACpB,MAAMC,KAAEA,EAAAC,MAAMA,GAAUF,EAAEG,OAC1BlB,GAAwBmB,IAAA,IACnBA,EACHH,CAACA,GAAOC,KACR,EAgIF,SAAAG,KAAC,MAAI,CAAAC,UAAU,8EACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,mCACbC,SAAA,GAACF,KAAA,KAAA,CAAGC,UAAU,oDAAoDC,SAAA,CAAA,WACvDC,EAAAA,IAAA,OAAA,CAAKF,UAAU,iBAAiBC,SAAK,aAE/CC,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAK,UAC3DC,EAAAA,IAAA,IAAA,CAAEF,UAAU,yCAAyCC,SAEtD,mDAGD,MAAI,CAAAD,UAAU,wCACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,mDACbC,SAAA,CAAAF,EAAAA,KAAC,OAAK,CAAAC,UAAU,YAAYG,SA1IfC,MAAOV,YAC1BA,EAAEW,iBACFjB,GAAW,GACXF,EAAS,IAEL,IAEF,IAAKR,EAAYG,QAAUH,EAAYI,SAC/B,MAAA,IAAIwB,MAAM,wCAIZ,MAAAC,EACkB,8BAAtB7B,EAAYG,OACU,oCAAtBH,EAAYG,OACU,mCAAtBH,EAAYG,MAGd,IAAI2B,EAAO,UACP9B,EAAYG,MAAM4B,SAAS,SACtBD,EAAA,QACE9B,EAAYG,MAAM4B,SAAS,iBAC7BD,EAAA,gBAIT,IASIE,EACAC,EAVAhB,EAAO,UAaX,GAXSA,EADI,UAATa,EACK,qBACW,iBAATA,EACF,mBAEA,kBAOLD,EAESG,EAAA,CACTE,GAAI,QAAQC,KAAKC,QACjBnB,OACAd,MAAOH,EAAYG,MACnB2B,QAGMG,EAAA,mBAAmBE,KAAKC,QAEhCC,QAAQC,IAAI,+BAAgC,CAAEN,kBAG1C,IACI,MAAAO,KAAEA,EAAMhC,MAAAA,SAAgBiC,EAASC,KAAKC,mBAAmB,CAC7DvC,MAAOH,EAAYG,MACnBC,SAAUJ,EAAYI,WAGxB,GAAIG,EACIA,MAAAA,EAIF,MAAAoC,KAAEA,EAAMC,QAAAA,GAAYL,EAGnBT,GAAA,OAAAe,EAAAF,EAAKG,oBAAL,EAAAD,EAAoBf,OAAQ,UAC5Bb,GAAA,OAAA8B,EAAAJ,EAAKG,oBAAL,EAAAC,EAAoB9B,OAAQ,UAGxBe,EAAA,CACTE,GAAIS,EAAKT,GACTjB,OACAd,MAAOwC,EAAKxC,MACZ2B,QAGFG,EAAQW,EAAQI,aAEhBX,QAAQC,IAAI,8BAA+B,CAAEN,mBACtCiB,GAIH,GAHIZ,QAAA9B,MAAM,qBAAsB0C,GAGN,wBAA1BA,EAAcC,QACV,MAAA,IAAItB,MAAM,sHAGZ,MAAAqB,CACR,CAIE5C,GACF8C,aAAaC,QAAQ,OAAQC,KAAKC,UAAUtB,IAC/BmB,aAAAC,QAAQ,QAASnB,KAE9BsB,eAAeH,QAAQ,OAAQC,KAAKC,UAAUtB,IAC/BuB,eAAAH,QAAQ,QAASnB,IAIlCpB,EAAS2C,EAAa,CACpBb,KAAMX,EACNC,WAKAtB,EADW,UAATmB,EACO,mBACS,iBAATA,EACA,0BAEA,4BAEJvB,GACC8B,QAAA9B,MAAM,6BAA8BA,GACnCA,EAAAA,EAAM2C,SAAW,0BAAyB,CACnD,QACAxC,GAAW,EACb,GAkBSa,SAAA,CACChB,GAAAkD,EAAAjC,IAAC,OAAIF,UAAU,0CACbC,eAAC,IAAE,CAAAD,UAAU,uBAAwBC,SAAAhB,aAIxC,MACC,CAAAgB,SAAA,CAAAC,MAAC,QAAM,CAAAkC,QAAQ,QAAQpC,UAAU,aAAaC,SAE9C,yBACAC,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAkC,EAAAjC,IAAC,QAAA,CACCU,GAAG,QACHjB,KAAK,QACL0C,KAAK,QACLC,aAAa,QACbC,UAAQ,EACRvC,UAAU,aACVwC,SAAU/C,EACVG,MAAOlB,EAAYG,oBAKxB,MACC,CAAAoB,SAAA,CAAAC,MAAC,QAAM,CAAAkC,QAAQ,WAAWpC,UAAU,aAAaC,SAEjD,iBACAC,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAkC,EAAAjC,IAAC,QAAA,CACCU,GAAG,WACHjB,KAAK,WACL0C,KAAK,WACLC,aAAa,mBACbC,UAAQ,EACRvC,UAAU,aACVwC,SAAU/C,EACVG,MAAOlB,EAAYI,kBAKzBiB,KAAC,MAAI,CAAAC,UAAU,oCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAkC,EAAAjC,IAAC,QAAA,CACCU,GAAG,cACHjB,KAAK,cACL0C,KAAK,WACLrC,UAAU,0EACVyC,QAAS1D,EACTyD,SAAW9C,GAAMV,EAAYU,EAAEG,OAAO4C,iBAEvC,QAAM,CAAAL,QAAQ,cAAcpC,UAAU,mCAAmCC,SAE1E,oBAGFC,IAAC,MAAI,CAAAF,UAAU,UACbC,SAAAC,EAAAA,IAAC,IAAE,CAAAwC,KAAK,IAAI1C,UAAU,sDAAsDC,SAAA,sCAM/E,MACC,CAAAA,SAAAkC,EAAAjC,IAAC,SAAA,CACCmC,KAAK,SACLM,SAAUxD,EACVa,UAAU,2NAETC,WAAU,sBAAwB,0BAKzCF,KAAC,MAAI,CAAAC,UAAU,OACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,WACbC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,qCACbC,eAAC,MAAI,CAAAD,UAAU,wCAEjBE,IAAC,OAAIF,UAAU,uCACbC,eAAC,OAAK,CAAAD,UAAU,8BAA8BC,SAAA,8BAMlDF,KAAC,MAAI,CAAAC,UAAU,wDACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,yBACbC,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,0CACZE,EAAAA,IAAA,KAAA,CAAGF,UAAU,oCAAoCC,SAAqC,+CAEzFF,KAAC,MAAI,CAAAC,UAAU,kCACbC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,sFACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,EAAAA,IAAC,QAAKF,UAAU,4FACdC,eAAC,IAAE,CAAAD,UAAU,iCAEdE,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAa,qBAE7CkC,EAAApC,KAAC,SAAA,CACCsC,KAAK,SACLrC,UAAU,0FACV4C,QAAS,KACQjE,EAAA,CACbE,MAAO,4BACPC,SAAU,cAEZE,GAAY,EAAI,EAGlBiB,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,4BAA8B,oCAKhD,MAAI,CAAAA,UAAU,sFACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,EAAAA,IAAC,QAAKF,UAAU,8FACdC,eAAC,IAAE,CAAAD,UAAU,6BAEdE,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAW,mBAE3CkC,EAAApC,KAAC,SAAA,CACCsC,KAAK,SACLrC,UAAU,0FACV4C,QAAS,KACQjE,EAAA,CACbE,MAAO,kCACPC,SAAU,aAEZE,GAAY,EAAI,EAGlBiB,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,4BAA8B,0CAKhD,MAAI,CAAAA,UAAU,sFACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,EAAAA,IAAC,QAAKF,UAAU,gGACdC,eAAC,IAAE,CAAAD,UAAU,mCAEdE,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAU,kBAE1CkC,EAAApC,KAAC,SAAA,CACCsC,KAAK,SACLrC,UAAU,0FACV4C,QAAS,KACQjE,EAAA,CACbE,MAAO,iCACPC,SAAU,cAEZE,GAAY,EAAI,EAGlBiB,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,4BAA8B,yCAKhD,MAAI,CAAAA,UAAU,yDACbC,SAACF,EAAAA,KAAA,IAAA,CAAEC,UAAU,2CACXC,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,mDACZ,OACC,CAAAC,SAAA,GAAAC,IAAC,UAAOD,SAAK,UAAS,2GAOhCF,KAAC,MAAI,CAAAC,UAAU,gBACbC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,qCACbC,eAAC,MAAI,CAAAD,UAAU,wCAEjBE,IAAC,OAAIF,UAAU,uCACbC,eAAC,OAAK,CAAAD,UAAU,8BAA8BC,SAAA,iCAMlDC,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAkC,EAAAjC,IAAC2C,EAAA,CACCC,GAAG,YACH9C,UAAU,0NACXC,SAAA,4BAOX"}