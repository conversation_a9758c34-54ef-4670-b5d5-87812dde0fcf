import{j as s}from"./index-165d7974.js";import{C as e,a,b as t}from"./Card-54419bd4.js";import{B as c}from"./Button-9c521291.js";const i=()=>s.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[s.jsxs("div",{className:"mb-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Test de Aptitud Mecánica"}),s.jsx("p",{className:"text-gray-600",children:"Esta sección está en desarrollo."})]}),s.jsxs(e,{children:[s.jsx(a,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Información"})}),s.jsxs(t,{children:[s.jsx("p",{className:"text-gray-700 mb-4",children:"El test de aptitud mecánica evalúa la comprensión de principios mecánicos y físicos básicos. Este test se encuentra actualmente en desarrollo."}),s.jsx(c,{onClick:()=>window.history.back(),children:"Volver"})]})]})]});export{i as default};
//# sourceMappingURL=Mecanico-6750c701.js.map
