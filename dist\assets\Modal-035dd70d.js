import{r as e,j as t}from"./index-165d7974.js";const n=({isOpen:n,onClose:s,title:r,children:o,size:l="md",closeOnClickOutside:a=!0})=>{const i=e.useRef(null);if(e.useEffect((()=>{const e=e=>{"Escape"===e.key&&s()};return n&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[n,s]),!n)return null;return t.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto bg-gray-600 bg-opacity-50 flex items-center justify-center",onClick:e=>{a&&i.current&&!i.current.contains(e.target)&&s()},children:t.jsxs("div",{ref:i,className:`bg-white rounded-lg shadow-2xl overflow-hidden w-full ${{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[l]} mx-auto transform transition-all animate-fadeIn`,children:[t.jsxs("div",{className:"bg-gray-50 px-6 py-4 flex items-center justify-between border-b border-gray-200",children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900",children:r}),t.jsxs("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none",onClick:s,children:[t.jsx("span",{className:"sr-only",children:"Cerrar"}),t.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})]})]}),t.jsx("div",{className:"px-6 py-4",children:o})]})})};export{n as M};
//# sourceMappingURL=Modal-035dd70d.js.map
