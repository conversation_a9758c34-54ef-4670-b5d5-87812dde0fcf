{"version": 3, "file": "Modal-035dd70d.js", "sources": ["../../src/components/ui/Modal.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nexport const Modal = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  closeOnClickOutside = true\n}) => {\n  const modalRef = useRef(null);\n\n  // Manejar cierre con tecla Escape\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden'; // Prevenir scroll en el body\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'auto'; // Restaurar scroll\n    };\n  }, [isOpen, onClose]);\n\n  // Si el modal no está abierto, no renderizar nada\n  if (!isOpen) return null;\n\n  // Manejar clic fuera del modal\n  const handleOutsideClick = (e) => {\n    if (closeOnClickOutside && modalRef.current && !modalRef.current.contains(e.target)) {\n      onClose();\n    }\n  };\n\n  // Tamaños del modal\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n    full: 'max-w-full mx-4'\n  };\n\n  return (\n    <div\n      className=\"fixed inset-0 z-50 overflow-y-auto bg-gray-600 bg-opacity-50 flex items-center justify-center\"\n      onClick={handleOutsideClick}\n    >\n      <div\n        ref={modalRef}\n        className={`bg-white rounded-lg shadow-2xl overflow-hidden w-full ${sizeClasses[size]} mx-auto transform transition-all animate-fadeIn`}\n      >\n        {/* Encabezado del modal */}\n        <div className=\"bg-gray-50 px-6 py-4 flex items-center justify-between border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\n          <button\n            type=\"button\"\n            className=\"text-gray-400 hover:text-gray-500 focus:outline-none\"\n            onClick={onClose}\n          >\n            <span className=\"sr-only\">Cerrar</span>\n            <svg className=\"h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Contenido del modal */}\n        <div className=\"px-6 py-4\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;\n"], "names": ["Modal", "isOpen", "onClose", "title", "children", "size", "closeOnClickOutside", "modalRef", "useRef", "useEffect", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "jsxRuntimeExports", "jsx", "className", "onClick", "current", "contains", "target", "jsxs", "ref", "sm", "md", "lg", "xl", "full", "type", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d"], "mappings": "+CAEO,MAAMA,EAAQ,EACnBC,SACAC,UACAC,QACAC,WACAC,OAAO,KACPC,uBAAsB,MAEhB,MAAAC,EAAWC,SAAO,MAsBxB,GAnBAC,EAAAA,WAAU,KACF,MAAAC,EAAgBC,IACN,WAAVA,EAAEC,QAEN,EAQF,OALIX,IACOY,SAAAC,iBAAiB,UAAWJ,GAC5BG,SAAAE,KAAKC,MAAMC,SAAW,UAG1B,KACIJ,SAAAK,oBAAoB,UAAWR,GAC/BG,SAAAE,KAAKC,MAAMC,SAAW,MAAA,CAAA,GAEhC,CAAChB,EAAQC,KAGPD,EAAe,OAAA,KAmBlB,OAAAkB,EAAAC,IAAC,MAAA,CACCC,UAAU,gGACVC,QAlBwBX,IACtBL,GAAuBC,EAASgB,UAAYhB,EAASgB,QAAQC,SAASb,EAAEc,YAE5E,EAiBErB,SAAAe,EAAAO,KAAC,MAAA,CACCC,IAAKpB,EACLc,UAAW,yDAfG,CAClBO,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAU8E3B,qDAGhFD,SAAA,GAACsB,KAAA,MAAA,CAAIL,UAAU,kFACbjB,SAAA,CAACgB,EAAAA,IAAA,KAAA,CAAGC,UAAU,oCAAqCjB,SAAMD,IACzDgB,EAAAO,KAAC,SAAA,CACCO,KAAK,SACLZ,UAAU,uDACVC,QAASpB,EAETE,SAAA,CAACgB,EAAAA,IAAA,OAAA,CAAKC,UAAU,UAAUjB,SAAM,WAChCgB,EAAAA,IAAC,OAAIC,UAAU,UAAUa,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YAAYC,OAAO,eACjGjC,SAACgB,EAAAA,IAAA,OAAA,CAAKkB,cAAc,QAAQC,eAAe,QAAQC,YAAY,IAAIC,EAAE,mCAM1ErB,IAAA,MAAA,CAAIC,UAAU,YACZjB,iBAEL"}