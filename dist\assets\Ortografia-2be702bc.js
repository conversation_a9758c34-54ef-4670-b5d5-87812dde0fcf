import{j as e}from"./index-165d7974.js";import{C as a,a as s,b as r}from"./Card-54419bd4.js";import{B as t}from"./Button-9c521291.js";const l=()=>e.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Test de Ortografía"}),e.jsx("p",{className:"text-gray-600",children:"Esta sección está en desarrollo."})]}),e.jsxs(a,{children:[e.jsx(s,{children:e.jsx("h2",{className:"text-lg font-medium",children:"Información"})}),e.jsxs(r,{children:[e.jsx("p",{className:"text-gray-700 mb-4",children:"El test de ortografía evalúa el conocimiento de las reglas ortográficas y la capacidad para identificar errores. Este test se encuentra actualmente en desarrollo."}),e.jsx(t,{onClick:()=>window.history.back(),children:"Volver"})]})]})]});export{l as default};
//# sourceMappingURL=Ortografia-2be702bc.js.map
