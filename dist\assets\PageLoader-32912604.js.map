{"version": 3, "file": "PageLoader-32912604.js", "sources": ["../../src/components/common/PageLoader.jsx"], "sourcesContent": ["import React from 'react';\n\n/**\n * Componente de carga para páginas que se cargan de forma diferida\n */\nconst PageLoader = () => {\n  return (\n    <div className=\"flex flex-col justify-center items-center min-h-[400px]\">\n      <div className=\"w-16 h-16 border-t-4 border-b-4 border-blue-500 rounded-full animate-spin mb-4\"></div>\n      <h2 className=\"text-xl font-semibold text-gray-700\">Cargando</h2>\n      <p className=\"text-gray-500 mt-2\">Por favor espere un momento...</p>\n    </div>\n  );\n};\n\nexport default PageLoader;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "jsxs", "className", "children", "jsx"], "mappings": "wCAKA,MAAMA,EAAa,MAEfC,KAAC,MAAI,CAAAC,UAAU,0DACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,mFACdE,EAAAA,IAAA,KAAA,CAAGF,UAAU,sCAAsCC,SAAQ,aAC3DC,EAAAA,IAAA,IAAA,CAAEF,UAAU,qBAAqBC,SAA8B"}