import{r as e,s,j as a}from"./index-165d7974.js";import{C as t,a as r,b as c}from"./Card-54419bd4.js";const i=()=>{const[i,n]=e.useState([]),[d,l]=e.useState(!0);e.useEffect((()=>{x()}),[]);const x=async()=>{try{l(!0);const{data:e,error:a}=await s.from("pacientes").select("*").order("nombre",{ascending:!0});if(a)throw a;n(e||[])}catch(e){console.error("Error al cargar pacientes:",e.message)}finally{l(!1)}};return a.jsxs("div",{className:"container mx-auto py-6",children:[a.jsxs("div",{className:"mb-6",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Pacientes"}),a.jsx("p",{className:"text-gray-600",children:"Lista de pacientes registrados en el sistema"})]}),a.jsxs(t,{children:[a.jsx(r,{children:a.jsx("h2",{className:"text-lg font-medium",children:"Lista de Pacientes"})}),a.jsx(c,{children:d?a.jsxs("div",{className:"text-center py-4",children:[a.jsx("i",{className:"fas fa-spinner fa-spin text-primary-500 text-2xl mb-2"}),a.jsx("p",{children:"Cargando pacientes..."})]}):0===i.length?a.jsx("div",{className:"text-center py-4",children:a.jsx("p",{className:"text-gray-500",children:"No hay pacientes registrados"})}):a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nombre"}),a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Edad"}),a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Género"}),a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha de Nacimiento"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map((e=>{return a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.nombre})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsxs("div",{className:"text-sm text-gray-500",children:[e.edad," años"]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("div",{className:"text-sm text-gray-500",children:e.genero})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("div",{className:"text-sm text-gray-500",children:(s=e.fecha_nacimiento,new Date(s).toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric"}))})})]},e.id);var s}))})]})})})]})]})};export{i as default};
//# sourceMappingURL=Patients-c17f6f91.js.map
