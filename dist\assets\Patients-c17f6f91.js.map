{"version": 3, "file": "Patients-c17f6f91.js", "sources": ["../../src/pages/student/Patients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { supabase } from '../../api/supabaseClient';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\n\nconst Patients = () => {\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Cargar pacientes al montar el componente\n  useEffect(() => {\n    fetchPatients();\n  }, []);\n\n  // Función para obtener pacientes de Supabase\n  const fetchPatients = async () => {\n    try {\n      setLoading(true);\n      const { data, error } = await supabase\n        .from('pacientes')\n        .select('*')\n        .order('nombre', { ascending: true });\n\n      if (error) throw error;\n      setPatients(data || []);\n    } catch (error) {\n      console.error('Error al cargar pacientes:', error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Formatear fecha para mostrar\n  const formatDate = (dateString) => {\n    const options = { year: 'numeric', month: 'long', day: 'numeric' };\n    return new Date(dateString).toLocaleDateString('es-ES', options);\n  };\n\n  return (\n    <div className=\"container mx-auto py-6\">\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Pacientes</h1>\n        <p className=\"text-gray-600\">Lista de pacientes registrados en el sistema</p>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <h2 className=\"text-lg font-medium\">Lista de Pacientes</h2>\n        </CardHeader>\n        <CardBody>\n          {loading ? (\n            <div className=\"text-center py-4\">\n              <i className=\"fas fa-spinner fa-spin text-primary-500 text-2xl mb-2\"></i>\n              <p>Cargando pacientes...</p>\n            </div>\n          ) : patients.length === 0 ? (\n            <div className=\"text-center py-4\">\n              <p className=\"text-gray-500\">No hay pacientes registrados</p>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Nombre\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Edad\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Género\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Fecha de Nacimiento\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {patients.map((patient) => (\n                    <tr key={patient.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">{patient.nombre}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-500\">{patient.edad} años</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-500\">\n                          {patient.genero}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-500\">{formatDate(patient.fecha_nacimiento)}</div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </CardBody>\n      </Card>\n    </div>\n  );\n};\n\nexport default Patients;\n"], "names": ["Patients", "patients", "setPatients", "useState", "loading", "setLoading", "useEffect", "fetchPatients", "async", "data", "error", "supabase", "from", "select", "order", "ascending", "console", "message", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody", "length", "scope", "map", "patient", "nombre", "edad", "genero", "dateString", "fecha_nacimiento", "Date", "toLocaleDateString", "year", "month", "day", "id"], "mappings": "sGAIA,MAAMA,EAAW,KACf,MAAOC,EAAUC,GAAeC,EAAAA,SAAS,KAClCC,EAASC,GAAcF,YAAS,GAGvCG,EAAAA,WAAU,WAEP,IAGH,MAAMC,EAAgBC,UAChB,IACFH,GAAW,GACX,MAAMI,KAAEA,EAAMC,MAAAA,SAAgBC,EAC3BC,KAAK,aACLC,OAAO,KACPC,MAAM,SAAU,CAAEC,WAAW,IAE5B,GAAAL,EAAa,MAAAA,EACLR,EAAAO,GAAQ,UACbC,GACCM,QAAAN,MAAM,6BAA8BA,EAAMO,QAAO,CACzD,QACAZ,GAAW,EACb,GAUA,SAAAa,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,OACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAS,cAC9DC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAA4C,2DAG1EE,EACC,CAAAF,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,wCAErCK,EACE,CAAAL,SAAAhB,SACE,MAAA,CAAIe,UAAU,mBACbC,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,4DACbE,IAAC,KAAED,SAAqB,6BAEJ,IAApBnB,EAASyB,aACV,MAAI,CAAAP,UAAU,mBACbC,WAAAC,IAAC,IAAE,CAAAF,UAAU,gBAAgBC,SAA4B,mCAG1DC,EAAAA,IAAA,MAAA,CAAIF,UAAU,kBACbC,SAAAI,EAAAN,KAAC,QAAM,CAAAC,UAAU,sCACfC,SAAA,CAAAC,MAAC,QAAM,CAAAF,UAAU,aACfC,SAAAI,EAAAN,KAAC,KACC,CAAAE,SAAA,CAAAC,MAAC,KAAG,CAAAM,MAAM,MAAMR,UAAU,iFAAiFC,SAE3G,iBACC,KAAG,CAAAO,MAAM,MAAMR,UAAU,iFAAiFC,SAE3G,eACC,KAAG,CAAAO,MAAM,MAAMR,UAAU,iFAAiFC,SAE3G,iBACC,KAAG,CAAAO,MAAM,MAAMR,UAAU,iFAAiFC,SAE3G,+BAGJC,IAAC,QAAM,CAAAF,UAAU,oCACdC,SAAAnB,EAAS2B,KAAKC,IACbL,SAAAN,KAAC,KAAoB,CAAAC,UAAU,mBAC7BC,SAAA,GAACC,IAAA,KAAA,CAAGF,UAAU,8BACZC,SAAAC,EAAAA,IAAC,OAAIF,UAAU,oCAAqCC,SAAQS,EAAAC,iBAE7D,KAAG,CAAAX,UAAU,8BACZC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,wBAAyBC,SAAA,CAAQS,EAAAE,KAAK,eAEvDV,IAAC,KAAG,CAAAF,UAAU,8BACZC,SAAAC,EAAAA,IAAC,OAAIF,UAAU,wBACZC,SAAQS,EAAAG,WAGZX,EAAAA,IAAA,KAAA,CAAGF,UAAU,8BACZC,WAACC,IAAA,MAAA,CAAIF,UAAU,wBAAyBC,UA5D1Ca,EA4DqDJ,EAAQK,iBA1DxE,IAAIC,KAAKF,GAAYG,mBAAmB,QAD/B,CAAEC,KAAM,UAAWC,MAAO,OAAQC,IAAK,mBA8C9BV,EAAQW,IA/ChB,IAACP,CA+DH,kBAOf"}