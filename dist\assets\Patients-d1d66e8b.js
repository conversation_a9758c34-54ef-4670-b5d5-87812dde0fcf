import{r as e,s as a,Q as t,j as s}from"./index-165d7974.js";import{C as r,a as n,b as i}from"./Card-54419bd4.js";import{B as c}from"./Button-9c521291.js";import{M as l}from"./Modal-035dd70d.js";const o=()=>{const[o,d]=e.useState([]),[m,x]=e.useState(!0),[h,p]=e.useState(!1),[u,g]=e.useState(null),[j,f]=e.useState({name:"",birth_date:"",gender:"",notes:""}),[b,N]=e.useState({});e.useEffect((()=>{y()}),[]);const y=async()=>{try{x(!0);const{data:e,error:t}=await a.from("pacientes").select("*").order("nombre",{ascending:!0});if(t)throw t;d(e||[])}catch(e){console.error("Error al cargar pacientes:",e.message),t.error("Error al cargar la lista de pacientes")}finally{x(!1)}},v=(e=null)=>{e?(g(e),f({name:e.nombre,birth_date:e.fecha_nacimiento,gender:e.genero,notes:e.notas||""})):(g(null),f({name:"",birth_date:"",gender:"",notes:""})),N({}),p(!0)},w=()=>{p(!1)},_=e=>{const{name:a,value:t}=e.target;f((e=>({...e,[a]:t}))),b[a]&&N((e=>({...e,[a]:null})))};return s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsxs("div",{className:"mb-6 flex flex-col md:flex-row md:items-center md:justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Gestión de Pacientes"}),s.jsx("p",{className:"text-gray-600",children:"Administre la información de los pacientes para evaluaciones psicométricas"})]}),s.jsx("div",{className:"mt-4 md:mt-0",children:s.jsxs(c,{variant:"primary",onClick:()=>v(),className:"flex items-center",disabled:m,children:[s.jsx("i",{className:"fas fa-plus mr-2"}),"Nuevo Paciente"]})})]}),s.jsxs(r,{children:[s.jsx(n,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Lista de Pacientes"})}),s.jsx(i,{children:m&&0===o.length?s.jsxs("div",{className:"text-center py-4",children:[s.jsx("i",{className:"fas fa-spinner fa-spin text-primary-500 text-2xl mb-2"}),s.jsx("p",{children:"Cargando pacientes..."})]}):0===o.length?s.jsxs("div",{className:"text-center py-4",children:[s.jsx("p",{className:"text-gray-500",children:"No hay pacientes registrados"}),s.jsx(c,{variant:"secondary",className:"mt-2",onClick:()=>v(),children:"Agregar Paciente"})]}):s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsxs("tr",{children:[s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nombre"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Edad"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Género"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha de Nacimiento"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Notas"}),s.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map((e=>{return s.jsxs("tr",{className:"hover:bg-gray-50",children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.nombre})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsxs("div",{className:"text-sm text-gray-500",children:[e.edad," años"]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("div",{className:"text-sm text-gray-500",children:e.genero})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("div",{className:"text-sm text-gray-500",children:(r=e.fecha_nacimiento,new Date(r).toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric"}))})}),s.jsx("td",{className:"px-6 py-4",children:s.jsx("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.notas||"-"})}),s.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[s.jsx("button",{onClick:()=>v(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:s.jsx("i",{className:"fas fa-edit"})}),s.jsx("button",{onClick:()=>(async e=>{if(window.confirm("¿Está seguro que desea eliminar este paciente? Esta acción no se puede deshacer."))try{x(!0);const{error:s}=await a.from("pacientes").delete().eq("id",e);if(s)throw s;t.success("Paciente eliminado correctamente"),y()}catch(s){console.error("Error al eliminar paciente:",s.message),t.error("Error al eliminar el paciente")}finally{x(!1)}})(e.id),className:"text-red-600 hover:text-red-900",children:s.jsx("i",{className:"fas fa-trash-alt"})})]})]},e.id);var r}))})]})})})]}),s.jsx(l,{isOpen:h,onClose:w,title:u?"Editar Paciente":"Nuevo Paciente",children:s.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return j.name.trim()||(e.name="El nombre es obligatorio"),j.birth_date||(e.birth_date="La fecha de nacimiento es obligatoria"),j.gender||(e.gender="El género es obligatorio"),N(e),0===Object.keys(e).length})())try{x(!0);const e=(e=>{const a=new Date,t=new Date(e);let s=a.getFullYear()-t.getFullYear();const r=a.getMonth()-t.getMonth();return(r<0||0===r&&a.getDate()<t.getDate())&&s--,s})(j.birth_date),s={nombre:j.name,fecha_nacimiento:j.birth_date,genero:j.gender,notas:j.notes,edad:e,fecha_creacion:(new Date).toISOString().split("T")[0]};if(u){const{error:e}=await a.from("pacientes").update(s).eq("id",u.id);if(e)throw e;t.success("Paciente actualizado correctamente")}else{const{error:e}=await a.from("pacientes").insert([s]);if(e)throw e;t.success("Paciente creado correctamente")}y(),w()}catch(s){console.error("Error al guardar paciente:",s.message),t.error(u?"Error al actualizar el paciente":"Error al crear el paciente")}finally{x(!1)}},children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre Completo *"}),s.jsx("input",{type:"text",id:"name",name:"name",value:j.name,onChange:_,className:"form-input "+(b.name?"border-red-500":""),placeholder:"Ej. Juan Pérez Gómez"}),b.name&&s.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.name})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"birth_date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha de Nacimiento *"}),s.jsx("input",{type:"date",id:"birth_date",name:"birth_date",value:j.birth_date,onChange:_,className:"form-input "+(b.birth_date?"border-red-500":"")}),b.birth_date&&s.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.birth_date})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"gender",className:"block text-sm font-medium text-gray-700 mb-1",children:"Género *"}),s.jsxs("select",{id:"gender",name:"gender",value:j.gender,onChange:_,className:"form-input "+(b.gender?"border-red-500":""),children:[s.jsx("option",{value:"",children:"Seleccionar género"}),s.jsx("option",{value:"M",children:"Masculino"}),s.jsx("option",{value:"F",children:"Femenino"})]}),b.gender&&s.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.gender})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{htmlFor:"notes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Notas"}),s.jsx("textarea",{id:"notes",name:"notes",value:j.notes,onChange:_,rows:"3",className:"form-input",placeholder:"Información adicional sobre el paciente"})]}),s.jsxs("div",{className:"flex justify-end space-x-2 mt-6",children:[s.jsx(c,{type:"button",variant:"secondary",onClick:w,disabled:m,children:"Cancelar"}),s.jsx(c,{type:"submit",variant:"primary",disabled:m,children:m?s.jsxs(s.Fragment,{children:[s.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}),u?"Actualizando...":"Guardando..."]}):u?"Actualizar":"Guardar"})]})]})})]})};export{o as default};
//# sourceMappingURL=Patients-d1d66e8b.js.map
