{"version": 3, "file": "Patients-d1d66e8b.js", "sources": ["../../src/pages/admin/Patients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { supabase } from '../../api/supabaseClient';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\nimport { Button } from '../../components/ui/Button';\nimport { Modal } from '../../components/ui/Modal';\nimport { toast } from 'react-toastify';\n\nconst Patients = () => {\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPatient, setCurrentPatient] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    birth_date: '',\n    gender: '',\n    notes: ''\n  });\n  const [errors, setErrors] = useState({});\n\n  // Cargar pacientes al montar el componente\n  useEffect(() => {\n    fetchPatients();\n  }, []);\n\n  // Función para obtener pacientes de Supabase\n  const fetchPatients = async () => {\n    try {\n      setLoading(true);\n      const { data, error } = await supabase\n        .from('pacientes')\n        .select('*')\n        .order('nombre', { ascending: true });\n\n      if (error) throw error;\n      setPatients(data || []);\n    } catch (error) {\n      console.error('Error al cargar pacientes:', error.message);\n      toast.error('Error al cargar la lista de pacientes');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Abrir modal para crear/editar paciente\n  const openModal = (patient = null) => {\n    if (patient) {\n      setCurrentPatient(patient);\n      setFormData({\n        name: patient.nombre,\n        birth_date: patient.fecha_nacimiento,\n        gender: patient.genero,\n        notes: patient.notas || ''\n      });\n    } else {\n      setCurrentPatient(null);\n      setFormData({\n        name: '',\n        birth_date: '',\n        gender: '',\n        notes: ''\n      });\n    }\n    setErrors({});\n    setIsModalOpen(true);\n  };\n\n  // Cerrar modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n  };\n\n  // Manejar cambios en el formulario\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Limpiar error del campo cuando el usuario escribe\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n\n  // Validar formulario\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'El nombre es obligatorio';\n    }\n\n    if (!formData.birth_date) {\n      newErrors.birth_date = 'La fecha de nacimiento es obligatoria';\n    }\n\n    if (!formData.gender) {\n      newErrors.gender = 'El género es obligatorio';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Calcular edad a partir de la fecha de nacimiento\n  const calculateAge = (birthDate) => {\n    const today = new Date();\n    const birth = new Date(birthDate);\n    let age = today.getFullYear() - birth.getFullYear();\n    const monthDiff = today.getMonth() - birth.getMonth();\n\n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {\n      age--;\n    }\n\n    return age;\n  };\n\n  // Enviar formulario\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) return;\n\n    try {\n      setLoading(true);\n\n      // Calcular edad\n      const age = calculateAge(formData.birth_date);\n\n      // Preparar datos para guardar\n      const patientData = {\n        nombre: formData.name,\n        fecha_nacimiento: formData.birth_date,\n        genero: formData.gender,\n        notas: formData.notes,\n        edad: age,\n        fecha_creacion: new Date().toISOString().split('T')[0]\n      };\n\n      if (currentPatient) {\n        // Actualizar paciente existente\n        const { error } = await supabase\n          .from('pacientes')\n          .update(patientData)\n          .eq('id', currentPatient.id);\n\n        if (error) throw error;\n        toast.success('Paciente actualizado correctamente');\n      } else {\n        // Crear nuevo paciente\n        const { error } = await supabase\n          .from('pacientes')\n          .insert([patientData]);\n\n        if (error) throw error;\n        toast.success('Paciente creado correctamente');\n      }\n\n      // Recargar lista de pacientes\n      fetchPatients();\n      closeModal();\n    } catch (error) {\n      console.error('Error al guardar paciente:', error.message);\n      toast.error(currentPatient\n        ? 'Error al actualizar el paciente'\n        : 'Error al crear el paciente');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Eliminar paciente\n  const handleDelete = async (id) => {\n    if (!window.confirm('¿Está seguro que desea eliminar este paciente? Esta acción no se puede deshacer.')) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const { error } = await supabase\n        .from('pacientes')\n        .delete()\n        .eq('id', id);\n\n      if (error) throw error;\n\n      toast.success('Paciente eliminado correctamente');\n      fetchPatients();\n    } catch (error) {\n      console.error('Error al eliminar paciente:', error.message);\n      toast.error('Error al eliminar el paciente');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Formatear fecha para mostrar\n  const formatDate = (dateString) => {\n    const options = { year: 'numeric', month: 'long', day: 'numeric' };\n    return new Date(dateString).toLocaleDateString('es-ES', options);\n  };\n\n  return (\n    <div className=\"container mx-auto py-6\">\n      <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Gestión de Pacientes</h1>\n          <p className=\"text-gray-600\">Administre la información de los pacientes para evaluaciones psicométricas</p>\n        </div>\n        <div className=\"mt-4 md:mt-0\">\n          <Button\n            variant=\"primary\"\n            onClick={() => openModal()}\n            className=\"flex items-center\"\n            disabled={loading}\n          >\n            <i className=\"fas fa-plus mr-2\"></i>\n            Nuevo Paciente\n          </Button>\n        </div>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <h2 className=\"text-lg font-medium\">Lista de Pacientes</h2>\n        </CardHeader>\n        <CardBody>\n          {loading && patients.length === 0 ? (\n            <div className=\"text-center py-4\">\n              <i className=\"fas fa-spinner fa-spin text-primary-500 text-2xl mb-2\"></i>\n              <p>Cargando pacientes...</p>\n            </div>\n          ) : patients.length === 0 ? (\n            <div className=\"text-center py-4\">\n              <p className=\"text-gray-500\">No hay pacientes registrados</p>\n              <Button\n                variant=\"secondary\"\n                className=\"mt-2\"\n                onClick={() => openModal()}\n              >\n                Agregar Paciente\n              </Button>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Nombre\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Edad\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Género\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Fecha de Nacimiento\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Notas\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Acciones\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {patients.map((patient) => (\n                    <tr key={patient.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">{patient.nombre}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-500\">{patient.edad} años</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-500\">\n                          {patient.genero}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-500\">{formatDate(patient.fecha_nacimiento)}</div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm text-gray-500 truncate max-w-xs\">{patient.notas || '-'}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                        <button\n                          onClick={() => openModal(patient)}\n                          className=\"text-blue-600 hover:text-blue-900 mr-3\"\n                        >\n                          <i className=\"fas fa-edit\"></i>\n                        </button>\n                        <button\n                          onClick={() => handleDelete(patient.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                        >\n                          <i className=\"fas fa-trash-alt\"></i>\n                        </button>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </CardBody>\n      </Card>\n\n      {/* Modal para crear/editar paciente */}\n      <Modal\n        isOpen={isModalOpen}\n        onClose={closeModal}\n        title={currentPatient ? 'Editar Paciente' : 'Nuevo Paciente'}\n      >\n        <form onSubmit={handleSubmit}>\n          <div className=\"mb-4\">\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Nombre Completo *\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              className={`form-input ${errors.name ? 'border-red-500' : ''}`}\n              placeholder=\"Ej. Juan Pérez Gómez\"\n            />\n            {errors.name && <p className=\"mt-1 text-sm text-red-500\">{errors.name}</p>}\n          </div>\n\n          <div className=\"mb-4\">\n            <label htmlFor=\"birth_date\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Fecha de Nacimiento *\n            </label>\n            <input\n              type=\"date\"\n              id=\"birth_date\"\n              name=\"birth_date\"\n              value={formData.birth_date}\n              onChange={handleChange}\n              className={`form-input ${errors.birth_date ? 'border-red-500' : ''}`}\n            />\n            {errors.birth_date && <p className=\"mt-1 text-sm text-red-500\">{errors.birth_date}</p>}\n          </div>\n\n          <div className=\"mb-4\">\n            <label htmlFor=\"gender\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Género *\n            </label>\n            <select\n              id=\"gender\"\n              name=\"gender\"\n              value={formData.gender}\n              onChange={handleChange}\n              className={`form-input ${errors.gender ? 'border-red-500' : ''}`}\n            >\n              <option value=\"\">Seleccionar género</option>\n              <option value=\"M\">Masculino</option>\n              <option value=\"F\">Femenino</option>\n            </select>\n            {errors.gender && <p className=\"mt-1 text-sm text-red-500\">{errors.gender}</p>}\n          </div>\n\n          <div className=\"mb-4\">\n            <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Notas\n            </label>\n            <textarea\n              id=\"notes\"\n              name=\"notes\"\n              value={formData.notes}\n              onChange={handleChange}\n              rows=\"3\"\n              className=\"form-input\"\n              placeholder=\"Información adicional sobre el paciente\"\n            ></textarea>\n          </div>\n\n          <div className=\"flex justify-end space-x-2 mt-6\">\n            <Button\n              type=\"button\"\n              variant=\"secondary\"\n              onClick={closeModal}\n              disabled={loading}\n            >\n              Cancelar\n            </Button>\n            <Button\n              type=\"submit\"\n              variant=\"primary\"\n              disabled={loading}\n            >\n              {loading ? (\n                <>\n                  <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                  {currentPatient ? 'Actualizando...' : 'Guardando...'}\n                </>\n              ) : (\n                currentPatient ? 'Actualizar' : 'Guardar'\n              )}\n            </Button>\n          </div>\n        </form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default Patients;\n"], "names": ["Patients", "patients", "setPatients", "useState", "loading", "setLoading", "isModalOpen", "setIsModalOpen", "currentPatient", "setCurrentPatient", "formData", "setFormData", "name", "birth_date", "gender", "notes", "errors", "setErrors", "useEffect", "fetchPatients", "async", "data", "error", "supabase", "from", "select", "order", "ascending", "console", "message", "toast", "openModal", "patient", "nombre", "fecha_nacimiento", "genero", "notas", "closeModal", "handleChange", "e", "value", "target", "prev", "jsxs", "className", "children", "jsx", "jsxRuntimeExports", "<PERSON><PERSON>", "variant", "onClick", "disabled", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardBody", "length", "scope", "map", "edad", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "id", "window", "confirm", "delete", "eq", "success", "handleDelete", "Modal", "isOpen", "onClose", "title", "onSubmit", "preventDefault", "newErrors", "trim", "Object", "keys", "validateForm", "age", "birthDate", "today", "birth", "getFullYear", "monthDiff", "getMonth", "getDate", "calculateAge", "patientData", "fecha_creacion", "toISOString", "split", "update", "insert", "Q", "htmlFor", "type", "onChange", "placeholder", "rows", "Fragment"], "mappings": "mMAOA,MAAMA,EAAW,KACf,MAAOC,EAAUC,GAAeC,EAAAA,SAAS,KAClCC,EAASC,GAAcF,YAAS,IAChCG,EAAaC,GAAkBJ,YAAS,IACxCK,EAAgBC,GAAqBN,WAAS,OAC9CO,EAAUC,GAAeR,WAAS,CACvCS,KAAM,GACNC,WAAY,GACZC,OAAQ,GACRC,MAAO,MAEFC,EAAQC,GAAad,EAAAA,SAAS,CAAE,GAGvCe,EAAAA,WAAU,WAEP,IAGH,MAAMC,EAAgBC,UAChB,IACFf,GAAW,GACX,MAAMgB,KAAEA,EAAMC,MAAAA,SAAgBC,EAC3BC,KAAK,aACLC,OAAO,KACPC,MAAM,SAAU,CAAEC,WAAW,IAE5B,GAAAL,EAAa,MAAAA,EACLpB,EAAAmB,GAAQ,UACbC,GACCM,QAAAN,MAAM,6BAA8BA,EAAMO,SAClDC,EAAMR,MAAM,wCAAuC,CACnD,QACAjB,GAAW,EACb,GAII0B,EAAY,CAACC,EAAU,QACvBA,GACFvB,EAAkBuB,GACNrB,EAAA,CACVC,KAAMoB,EAAQC,OACdpB,WAAYmB,EAAQE,iBACpBpB,OAAQkB,EAAQG,OAChBpB,MAAOiB,EAAQI,OAAS,OAG1B3B,EAAkB,MACNE,EAAA,CACVC,KAAM,GACNC,WAAY,GACZC,OAAQ,GACRC,MAAO,MAGXE,EAAU,CAAE,GACZV,GAAe,EAAI,EAIf8B,EAAa,KACjB9B,GAAe,EAAK,EAIhB+B,EAAgBC,IACpB,MAAM3B,KAAEA,EAAA4B,MAAMA,GAAUD,EAAEE,OAC1B9B,GAAqB+B,IAAA,IAChBA,EACH9B,CAACA,GAAO4B,MAINxB,EAAOJ,IACTK,GAAmByB,IAAA,IACdA,EACH9B,CAACA,GAAO,QAEZ,EA2HA,SAAA+B,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,oEACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAoB,yBACzEC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAA0E,oFAEzGC,IAAC,MAAI,CAAAF,UAAU,eACbC,SAAAE,EAAAJ,KAACK,EAAA,CACCC,QAAQ,UACRC,QAAS,IAAMnB,IACfa,UAAU,oBACVO,SAAU/C,EAEVyC,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,qBAAuB,gCAMzCQ,EACC,CAAAP,SAAA,CAAAC,EAAAA,IAACO,GACCR,SAACE,EAAAD,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,kCAEtCC,EAAAA,IAACQ,GACET,SAAWzC,GAAoB,IAApBH,EAASsD,SACnBZ,KAAC,MAAI,CAAAC,UAAU,mBACbC,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,4DACbE,IAAC,KAAED,SAAqB,6BAEJ,IAApB5C,EAASsD,cACV,MAAA,CAAIX,UAAU,mBACbC,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAA4B,iCACzDE,EAAAD,IAACE,EAAA,CACCC,QAAQ,YACRL,UAAU,OACVM,QAAS,IAAMnB,IAChBc,SAAA,8BAKF,MAAI,CAAAD,UAAU,kBACbC,WAACF,KAAA,QAAA,CAAMC,UAAU,sCACfC,SAAA,CAAAC,MAAC,QAAM,CAAAF,UAAU,aACfC,SAAAE,EAAAJ,KAAC,KACC,CAAAE,SAAA,CAAAC,MAAC,KAAG,CAAAU,MAAM,MAAMZ,UAAU,iFAAiFC,SAE3G,iBACC,KAAG,CAAAW,MAAM,MAAMZ,UAAU,iFAAiFC,SAE3G,eACC,KAAG,CAAAW,MAAM,MAAMZ,UAAU,iFAAiFC,SAE3G,iBACC,KAAG,CAAAW,MAAM,MAAMZ,UAAU,iFAAiFC,SAE3G,8BACC,KAAG,CAAAW,MAAM,MAAMZ,UAAU,iFAAiFC,SAE3G,gBACC,KAAG,CAAAW,MAAM,MAAMZ,UAAU,kFAAkFC,SAE5G,oBAGJC,IAAC,QAAM,CAAAF,UAAU,oCACdC,SAAA5C,EAASwD,KAAKzB,IACbe,SAAAJ,KAAC,KAAoB,CAAAC,UAAU,mBAC7BC,SAAA,GAACC,IAAA,KAAA,CAAGF,UAAU,8BACZC,SAAAC,EAAAA,IAAC,OAAIF,UAAU,oCAAqCC,SAAQb,EAAAC,iBAE7D,KAAG,CAAAW,UAAU,8BACZC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,wBAAyBC,SAAA,CAAQb,EAAA0B,KAAK,eAEvDZ,IAAC,KAAG,CAAAF,UAAU,8BACZC,SAAAC,EAAAA,IAAC,OAAIF,UAAU,wBACZC,SAAQb,EAAAG,WAGZW,EAAAA,IAAA,KAAA,CAAGF,UAAU,8BACZC,WAACC,IAAA,MAAA,CAAIF,UAAU,wBAAyBC,UAtF1Cc,EAsFqD3B,EAAQE,iBApFxE,IAAI0B,KAAKD,GAAYE,mBAAmB,QAD/B,CAAEC,KAAM,UAAWC,MAAO,OAAQC,IAAK,mBAuFrClB,IAAC,KAAG,CAAAF,UAAU,YACZC,SAAAC,EAAAA,IAAC,MAAI,CAAAF,UAAU,0CAA2CC,SAAAb,EAAQI,OAAS,UAE7EO,KAAC,KAAG,CAAAC,UAAU,6DACZC,SAAA,CAAAE,EAAAD,IAAC,SAAA,CACCI,QAAS,IAAMnB,EAAUC,GACzBY,UAAU,yCAEVC,SAAAC,EAAAA,IAAC,IAAE,CAAAF,UAAU,kBAEfG,EAAAD,IAAC,SAAA,CACCI,QAAS,IA5HZ9B,OAAO6C,IAC1B,GAAKC,OAAOC,QAAQ,oFAIhB,IACF9D,GAAW,GACX,MAAMiB,MAAEA,SAAgBC,EACrBC,KAAK,aACL4C,SACAC,GAAG,KAAMJ,GAER,GAAA3C,EAAa,MAAAA,EAEjBQ,EAAMwC,QAAQ,8CAEPhD,GACCM,QAAAN,MAAM,8BAA+BA,EAAMO,SACnDC,EAAMR,MAAM,gCAA+B,CAC3C,QACAjB,GAAW,EACb,GAuGqCkE,CAAavC,EAAQiC,IACpCrB,UAAU,kCAEVC,SAAAC,EAAAA,IAAC,IAAE,CAAAF,UAAU,4BA7BVZ,EAAQiC,IAzEhB,IAACN,CA0GH,gBASbZ,EAAAD,IAAC0B,EAAA,CACCC,OAAQnE,EACRoE,QAASrC,EACTsC,MAAOnE,EAAiB,kBAAoB,iBAE5CqC,SAAAE,EAAAJ,KAAC,OAAK,CAAAiC,SAvMSxD,MAAOmB,IAG1B,GAFAA,EAAEsC,iBAnCiB,MACnB,MAAMC,EAAY,CAAA,EAelB,OAbKpE,EAASE,KAAKmE,SACjBD,EAAUlE,KAAO,4BAGdF,EAASG,aACZiE,EAAUjE,WAAa,yCAGpBH,EAASI,SACZgE,EAAUhE,OAAS,4BAGrBG,EAAU6D,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWvB,MAAW,EAqBpC2B,GAED,IACF7E,GAAW,GAGL,MAAA8E,EAvBW,CAACC,IACd,MAAAC,MAAYzB,KACZ0B,EAAQ,IAAI1B,KAAKwB,GACvB,IAAID,EAAME,EAAME,cAAgBD,EAAMC,cACtC,MAAMC,EAAYH,EAAMI,WAAaH,EAAMG,WAMpC,OAJHD,EAAY,GAAoB,IAAdA,GAAmBH,EAAMK,UAAYJ,EAAMI,YAC/DP,IAGKA,CAAA,EAaOQ,CAAajF,EAASG,YAG5B+E,EAAc,CAClB3D,OAAQvB,EAASE,KACjBsB,iBAAkBxB,EAASG,WAC3BsB,OAAQzB,EAASI,OACjBsB,MAAO1B,EAASK,MAChB2C,KAAMyB,EACNU,gBAAA,IAAoBjC,MAAOkC,cAAcC,MAAM,KAAK,IAGtD,GAAIvF,EAAgB,CAElB,MAAMc,MAAEA,SAAgBC,EACrBC,KAAK,aACLwE,OAAOJ,GACPvB,GAAG,KAAM7D,EAAeyD,IAEvB,GAAA3C,EAAa,MAAAA,EACjBQ,EAAMwC,QAAQ,qCAAoC,KAC7C,CAEC,MAAAhD,MAAEA,SAAgBC,EACrBC,KAAK,aACLyE,OAAO,CAACL,IAEP,GAAAtE,EAAa,MAAAA,EACjBQ,EAAMwC,QAAQ,gCAChB,eAKOhD,GACCM,QAAAN,MAAM,6BAA8BA,EAAMO,SAC5CqE,EAAA5E,MAAMd,EACR,kCACA,6BAA4B,CAChC,QACAH,GAAW,EACb,GAsJMwC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,MAAC,QAAM,CAAAqD,QAAQ,OAAOvD,UAAU,+CAA+CC,SAE/E,sBACAE,EAAAD,IAAC,QAAA,CACCsD,KAAK,OACLnC,GAAG,OACHrD,KAAK,OACL4B,MAAO9B,EAASE,KAChByF,SAAU/D,EACVM,UAAW,eAAc5B,EAAOJ,KAAO,iBAAmB,IAC1D0F,YAAY,yBAEbtF,EAAOJ,MAAQkC,EAAAA,IAAC,KAAEF,UAAU,4BAA6BC,WAAOjC,YAGnE+B,KAAC,MAAI,CAAAC,UAAU,OACbC,SAAA,CAAAC,MAAC,QAAM,CAAAqD,QAAQ,aAAavD,UAAU,+CAA+CC,SAErF,0BACAE,EAAAD,IAAC,QAAA,CACCsD,KAAK,OACLnC,GAAG,aACHrD,KAAK,aACL4B,MAAO9B,EAASG,WAChBwF,SAAU/D,EACVM,UAAW,eAAc5B,EAAOH,WAAa,iBAAmB,MAEjEG,EAAOH,YAAciC,EAAAA,IAAC,KAAEF,UAAU,4BAA6BC,WAAOhC,kBAGzE8B,KAAC,MAAI,CAAAC,UAAU,OACbC,SAAA,CAAAC,MAAC,QAAM,CAAAqD,QAAQ,SAASvD,UAAU,+CAA+CC,SAEjF,aACAE,EAAAJ,KAAC,SAAA,CACCsB,GAAG,SACHrD,KAAK,SACL4B,MAAO9B,EAASI,OAChBuF,SAAU/D,EACVM,UAAW,eAAc5B,EAAOF,OAAS,iBAAmB,IAE5D+B,SAAA,CAACC,EAAAA,IAAA,SAAA,CAAON,MAAM,GAAGK,SAAkB,uBAClCC,EAAAA,IAAA,SAAA,CAAON,MAAM,IAAIK,SAAS,cAC1BC,EAAAA,IAAA,SAAA,CAAON,MAAM,IAAIK,SAAQ,gBAE3B7B,EAAOF,QAAUgC,EAAAA,IAAC,KAAEF,UAAU,4BAA6BC,WAAO/B,cAGrE6B,KAAC,MAAI,CAAAC,UAAU,OACbC,SAAA,CAAAC,MAAC,QAAM,CAAAqD,QAAQ,QAAQvD,UAAU,+CAA+CC,SAEhF,UACAE,EAAAD,IAAC,WAAA,CACCmB,GAAG,QACHrD,KAAK,QACL4B,MAAO9B,EAASK,MAChBsF,SAAU/D,EACViE,KAAK,IACL3D,UAAU,aACV0D,YAAY,iDAIhB3D,KAAC,MAAI,CAAAC,UAAU,kCACbC,SAAA,CAAAE,EAAAD,IAACE,EAAA,CACCoD,KAAK,SACLnD,QAAQ,YACRC,QAASb,EACTc,SAAU/C,EACXyC,SAAA,aAGDE,EAAAD,IAACE,EAAA,CACCoD,KAAK,SACLnD,QAAQ,UACRE,SAAU/C,EAETyC,WAEGF,EAAAA,KAAA6D,EAAAA,SAAA,CAAA3D,SAAA,GAACC,IAAA,IAAA,CAAEF,UAAU,gCACZpC,EAAiB,kBAAoB,kBAGxCA,EAAiB,aAAe,sBAM5C"}