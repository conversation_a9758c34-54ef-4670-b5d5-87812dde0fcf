import{j as e}from"./index-165d7974.js";import{C as s,a,b as r}from"./Card-54419bd4.js";import{B as t}from"./Button-9c521291.js";const l=()=>e.jsxs("div",{className:"container mx-auto py-6 max-w-4xl",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Test de Razonamiento"}),e.jsx("p",{className:"text-gray-600",children:"Esta sección está en desarrollo."})]}),e.jsxs(s,{children:[e.jsx(a,{children:e.jsx("h2",{className:"text-lg font-medium",children:"Información"})}),e.jsxs(r,{children:[e.jsx("p",{className:"text-gray-700 mb-4",children:"El test de razonamiento evalúa la capacidad para resolver problemas lógicos y encontrar patrones. Este test se encuentra actualmente en desarrollo."}),e.jsx(t,{onClick:()=>window.history.back(),children:"Volver"})]})]})]});export{l as default};
//# sourceMappingURL=Razonamiento-d95dad9d.js.map
