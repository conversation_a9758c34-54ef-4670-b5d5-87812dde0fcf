{"version": 3, "file": "Razonamiento-d95dad9d.js", "sources": ["../../src/pages/test/Razonamiento.jsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>, CardHeader, CardBody } from '../../components/ui/Card';\nimport { Button } from '../../components/ui/Button';\n\nconst Razonamiento = () => {\n  return (\n    <div className=\"container mx-auto py-6 max-w-4xl\">\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">Test de Razonamiento</h1>\n        <p className=\"text-gray-600\">Esta sección está en desarrollo.</p>\n      </div>\n      \n      <Card>\n        <CardHeader>\n          <h2 className=\"text-lg font-medium\">Información</h2>\n        </CardHeader>\n        <CardBody>\n          <p className=\"text-gray-700 mb-4\">\n            El test de razonamiento evalúa la capacidad para resolver problemas lógicos y encontrar patrones. \n            Este test se encuentra actualmente en desarrollo.\n          </p>\n          <Button onClick={() => window.history.back()}>\n            Volver\n          </Button>\n        </CardBody>\n      </Card>\n    </div>\n  );\n};\n\nexport default Razonamiento;"], "names": ["Razonamiento", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody", "<PERSON><PERSON>", "onClick", "window", "history", "back"], "mappings": "iIAIA,MAAMA,EAAe,MAEjBC,KAAC,MAAI,CAAAC,UAAU,mCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,OACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAoB,yBACzEC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAAgC,+CAG9DE,EACC,CAAAF,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,kCAErCK,EACC,CAAAL,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAEF,UAAU,qBAAqBC,SAGlC,8JACCM,GAAOC,QAAS,IAAMC,OAAOC,QAAQC,OAAQV,SAE9C"}