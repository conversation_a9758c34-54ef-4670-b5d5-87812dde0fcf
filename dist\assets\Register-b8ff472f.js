import{r as e,u as s,a,j as r,L as t,l}from"./index-165d7974.js";const n=()=>{const[n,o]=e.useState({name:"",email:"",password:"",confirmPassword:""}),[i,m]=e.useState(""),[d,c]=e.useState(!1),u=s(),x=a(),h=e=>{const{name:s,value:a}=e.target;o((e=>({...e,[s]:a})))};return r.jsxs("div",{className:"min-h-screen bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[r.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[r.jsxs("h2",{className:"text-center text-3xl font-extrabold text-gray-900",children:["Activatu",r.jsx("span",{className:"text-[#ffda0a]",children:"mente"})]}),r.jsx("h3",{className:"mt-2 text-center text-xl text-gray-600",children:"BAT-7"}),r.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Crea una cuenta nueva"})]}),r.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:r.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[r.jsxs("form",{className:"space-y-6",onSubmit:async e=>{e.preventDefault(),c(!0),m("");try{if(!n.name||!n.email||!n.password)throw new Error("Todos los campos son obligatorios");if(n.password!==n.confirmPassword)throw new Error("Las contraseñas no coinciden");const e={id:Math.floor(1e3*Math.random()).toString(),name:n.name,email:n.email,role:"student"};x(l({user:e,token:"token-simulado-nuevo-usuario"})),sessionStorage.setItem("user",JSON.stringify(e)),sessionStorage.setItem("token","token-simulado-nuevo-usuario"),u("/student/dashboard")}catch(s){m(s.message)}finally{c(!1)}},children:[i&&r.jsx("div",{className:"bg-red-50 border-l-4 border-red-400 p-4",children:r.jsx("p",{className:"text-sm text-red-700",children:i})}),r.jsxs("div",{children:[r.jsx("label",{htmlFor:"name",className:"form-label",children:"Nombre completo"}),r.jsx("div",{className:"mt-1",children:r.jsx("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,className:"form-input",onChange:h,value:n.name})})]}),r.jsxs("div",{children:[r.jsx("label",{htmlFor:"email",className:"form-label",children:"Correo electrónico"}),r.jsx("div",{className:"mt-1",children:r.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"form-input",onChange:h,value:n.email})})]}),r.jsxs("div",{children:[r.jsx("label",{htmlFor:"password",className:"form-label",children:"Contraseña"}),r.jsx("div",{className:"mt-1",children:r.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"form-input",onChange:h,value:n.password})})]}),r.jsxs("div",{children:[r.jsx("label",{htmlFor:"confirmPassword",className:"form-label",children:"Confirmar contraseña"}),r.jsx("div",{className:"mt-1",children:r.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:"form-input",onChange:h,value:n.confirmPassword})})]}),r.jsx("div",{children:r.jsx("button",{type:"submit",disabled:d,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:d?"Registrando...":"Registrarse"})})]}),r.jsxs("div",{className:"mt-6",children:[r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-0 flex items-center",children:r.jsx("div",{className:"w-full border-t border-gray-300"})}),r.jsx("div",{className:"relative flex justify-center text-sm",children:r.jsx("span",{className:"px-2 bg-white text-gray-500",children:"¿Ya tiene una cuenta?"})})]}),r.jsx("div",{className:"mt-6",children:r.jsx(t,{to:"/login",className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"Iniciar sesión"})})]})]})})]})};export{n as default};
//# sourceMappingURL=Register-b8ff472f.js.map
