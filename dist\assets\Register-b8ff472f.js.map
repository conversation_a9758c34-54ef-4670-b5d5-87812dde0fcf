{"version": 3, "file": "Register-b8ff472f.js", "sources": ["../../src/pages/auth/Register.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { loginSuccess } from '../../store/slices/authSlice';\n\nconst Register = () => {\n  const [userData, setUserData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  \n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setUserData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    \n    try {\n      // Validaciones básicas\n      if (!userData.name || !userData.email || !userData.password) {\n        throw new Error('Todos los campos son obligatorios');\n      }\n      \n      if (userData.password !== userData.confirmPassword) {\n        throw new Error('Las contraseñas no coinciden');\n      }\n      \n      // Simulación de registro exitoso\n      // En producción, aquí harías una llamada a la API\n      const newUser = {\n        id: Math.floor(Math.random() * 1000).toString(),\n        name: userData.name,\n        email: userData.email,\n        role: 'student'\n      };\n      \n      // Auto login después del registro usando Redux\n      dispatch(loginSuccess({\n        user: newUser,\n        token: 'token-simulado-nuevo-usuario'\n      }));\n      \n      // Guardar en sessionStorage\n      sessionStorage.setItem('user', JSON.stringify(newUser));\n      sessionStorage.setItem('token', 'token-simulado-nuevo-usuario');\n      \n      // Redirigir al dashboard de estudiante\n      navigate('/student/dashboard');\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <h2 className=\"text-center text-3xl font-extrabold text-gray-900\">\n          Activatu<span className=\"text-[#ffda0a]\">mente</span>\n        </h2>\n        <h3 className=\"mt-2 text-center text-xl text-gray-600\">BAT-7</h3>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Crea una cuenta nueva\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border-l-4 border-red-400 p-4\">\n                <p className=\"text-sm text-red-700\">{error}</p>\n              </div>\n            )}\n            \n            <div>\n              <label htmlFor=\"name\" className=\"form-label\">\n                Nombre completo\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  autoComplete=\"name\"\n                  required\n                  className=\"form-input\"\n                  onChange={handleChange}\n                  value={userData.name}\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label htmlFor=\"email\" className=\"form-label\">\n                Correo electrónico\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  className=\"form-input\"\n                  onChange={handleChange}\n                  value={userData.email}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"form-label\">\n                Contraseña\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  className=\"form-input\"\n                  onChange={handleChange}\n                  value={userData.password}\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                Confirmar contraseña\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  className=\"form-input\"\n                  onChange={handleChange}\n                  value={userData.confirmPassword}\n                />\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                {loading ? 'Registrando...' : 'Registrarse'}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\"></div>\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">\n                  ¿Ya tiene una cuenta?\n                </span>\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <Link\n                to=\"/login\"\n                className=\"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                Iniciar sesión\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;"], "names": ["Register", "userData", "setUserData", "useState", "name", "email", "password", "confirmPassword", "error", "setError", "loading", "setLoading", "navigate", "useNavigate", "dispatch", "useDispatch", "handleChange", "e", "value", "target", "prev", "jsxs", "className", "children", "jsx", "onSubmit", "async", "preventDefault", "Error", "newUser", "id", "Math", "floor", "random", "toString", "role", "loginSuccess", "user", "token", "sessionStorage", "setItem", "JSON", "stringify", "message", "jsxRuntimeExports", "htmlFor", "type", "autoComplete", "required", "onChange", "disabled", "Link", "to"], "mappings": "iEAKA,MAAMA,EAAW,KACf,MAAOC,EAAUC,GAAeC,WAAS,CACvCC,KAAM,GACNC,MAAO,GACPC,SAAU,GACVC,gBAAiB,MAEZC,EAAOC,GAAYN,WAAS,KAC5BO,EAASC,GAAcR,YAAS,GAEjCS,EAAWC,IACXC,EAAWC,IAEXC,EAAgBC,IACpB,MAAMb,KAAEA,EAAAc,MAAMA,GAAUD,EAAEE,OAC1BjB,GAAqBkB,IAAA,IAChBA,EACHhB,CAACA,GAAOc,KACR,EA+CF,SAAAG,KAAC,MAAI,CAAAC,UAAU,8EACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,mCACbC,SAAA,GAACF,KAAA,KAAA,CAAGC,UAAU,oDAAoDC,SAAA,CAAA,WACvDC,EAAAA,IAAA,OAAA,CAAKF,UAAU,iBAAiBC,SAAK,aAE/CC,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAK,UAC3DC,EAAAA,IAAA,IAAA,CAAEF,UAAU,yCAAyCC,SAEtD,mCAGD,MAAI,CAAAD,UAAU,wCACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,mDACbC,SAAA,CAAAF,EAAAA,KAAC,OAAK,CAAAC,UAAU,YAAYG,SAzDfC,MAAOT,IAC1BA,EAAEU,iBACFhB,GAAW,GACXF,EAAS,IAEL,IAEE,IAACR,EAASG,OAASH,EAASI,QAAUJ,EAASK,SAC3C,MAAA,IAAIsB,MAAM,qCAGd,GAAA3B,EAASK,WAAaL,EAASM,gBAC3B,MAAA,IAAIqB,MAAM,gCAKlB,MAAMC,EAAU,CACdC,GAAIC,KAAKC,MAAsB,IAAhBD,KAAKE,UAAiBC,WACrC9B,KAAMH,EAASG,KACfC,MAAOJ,EAASI,MAChB8B,KAAM,WAIRrB,EAASsB,EAAa,CACpBC,KAAMR,EACNS,MAAO,kCAITC,eAAeC,QAAQ,OAAQC,KAAKC,UAAUb,IAC/BU,eAAAC,QAAQ,QAAS,gCAGhC5B,EAAS,4BACFJ,GACPC,EAASD,EAAMmC,QAAO,CACtB,QACAhC,GAAW,EACb,GAkBSY,SAAA,CACCf,GAAAoC,EAAApB,IAAC,OAAIF,UAAU,0CACbC,eAAC,IAAE,CAAAD,UAAU,uBAAwBC,SAAAf,aAIxC,MACC,CAAAe,SAAA,CAAAC,MAAC,QAAM,CAAAqB,QAAQ,OAAOvB,UAAU,aAAaC,SAE7C,sBACAC,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAqB,EAAApB,IAAC,QAAA,CACCM,GAAG,OACH1B,KAAK,OACL0C,KAAK,OACLC,aAAa,OACbC,UAAQ,EACR1B,UAAU,aACV2B,SAAUjC,EACVE,MAAOjB,EAASG,mBAKrB,MACC,CAAAmB,SAAA,CAAAC,MAAC,QAAM,CAAAqB,QAAQ,QAAQvB,UAAU,aAAaC,SAE9C,yBACAC,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAqB,EAAApB,IAAC,QAAA,CACCM,GAAG,QACH1B,KAAK,QACL0C,KAAK,QACLC,aAAa,QACbC,UAAQ,EACR1B,UAAU,aACV2B,SAAUjC,EACVE,MAAOjB,EAASI,oBAKrB,MACC,CAAAkB,SAAA,CAAAC,MAAC,QAAM,CAAAqB,QAAQ,WAAWvB,UAAU,aAAaC,SAEjD,iBACAC,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAqB,EAAApB,IAAC,QAAA,CACCM,GAAG,WACH1B,KAAK,WACL0C,KAAK,WACLC,aAAa,eACbC,UAAQ,EACR1B,UAAU,aACV2B,SAAUjC,EACVE,MAAOjB,EAASK,uBAKrB,MACC,CAAAiB,SAAA,CAAAC,MAAC,QAAM,CAAAqB,QAAQ,kBAAkBvB,UAAU,aAAaC,SAExD,2BACAC,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAqB,EAAApB,IAAC,QAAA,CACCM,GAAG,kBACH1B,KAAK,kBACL0C,KAAK,WACLC,aAAa,eACbC,UAAQ,EACR1B,UAAU,aACV2B,SAAUjC,EACVE,MAAOjB,EAASM,6BAKrB,MACC,CAAAgB,SAAAqB,EAAApB,IAAC,SAAA,CACCsB,KAAK,SACLI,SAAUxC,EACVY,UAAU,2NAETC,WAAU,iBAAmB,uBAKpCF,KAAC,MAAI,CAAAC,UAAU,OACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,WACbC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,qCACbC,eAAC,MAAI,CAAAD,UAAU,wCAEjBE,IAAC,OAAIF,UAAU,uCACbC,eAAC,OAAK,CAAAD,UAAU,8BAA8BC,SAAA,iCAMlDC,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAqB,EAAApB,IAAC2B,EAAA,CACCC,GAAG,SACH9B,UAAU,0NACXC,SAAA,+BAOX"}