import{j as s}from"./index-165d7974.js";import{C as e,a,b as t}from"./Card-54419bd4.js";const r=()=>s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Reportes y Estadísticas"}),s.jsxs(e,{children:[s.jsx(a,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Panel de Reportes"})}),s.jsx(t,{children:s.jsx("p",{className:"text-gray-600",children:"Esta sección permitirá visualizar reportes y estadísticas del sistema (componente en desarrollo)."})})]})]});export{r as default};
//# sourceMappingURL=Reports-1e7f5957.js.map
