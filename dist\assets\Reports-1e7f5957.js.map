{"version": 3, "file": "Reports-1e7f5957.js", "sources": ["../../src/pages/admin/Reports.jsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\n\nconst Reports = () => {\n  return (\n    <div className=\"container mx-auto py-6\">\n      <h1 className=\"text-2xl font-bold text-gray-800 mb-6\">Reportes y Estadísticas</h1>\n      \n      <Card>\n        <CardHeader>\n          <h2 className=\"text-lg font-medium\">Panel de Reportes</h2>\n        </CardHeader>\n        <CardBody>\n          <p className=\"text-gray-600\">\n            Esta sección permitirá visualizar reportes y estadísticas del sistema (componente en desarrollo).\n          </p>\n        </CardBody>\n      </Card>\n    </div>\n  );\n};\n\nexport default Reports;"], "names": ["Reports", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody"], "mappings": "wFAGA,MAAMA,EAAU,MAEZC,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAuB,mCAE5EE,EACC,CAAAF,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,uCAErCK,EACC,CAAAL,SAAAI,EAAAH,IAAC,KAAEF,UAAU,gBAAgBC"}