import{r as e,j as t,L as s}from"./index-165d7974.js";import{C as r,b as a}from"./Card-54419bd4.js";import{B as i}from"./Button-9c521291.js";import{u as l}from"./useToast-cda9a5e1.js";const n=()=>{const[n,c]=e.useState([]),[d,o]=e.useState(!0),{showToast:x}=l();e.useEffect((()=>{(async()=>{try{await new Promise((e=>setTimeout(e,800)));const e=[{id:"12345",testName:"Test de Aptitud Verbal",completedDate:new Date(2025,3,15),score:82,percentile:72,interpretation:"Alto"},{id:"12346",testName:"Test de Razonamiento Lógico",completedDate:new Date(2025,3,10),score:75,percentile:64,interpretation:"Medio-Alto"},{id:"12347",testName:"Test de Aptitud Numérica",completedDate:new Date(2025,2,25),score:68,percentile:54,interpretation:"Medio"}];c(e),o(!1)}catch(e){console.error("Error al cargar resultados:",e),x("Error al cargar los resultados","error"),o(!1)}})()}),[]);const m=e=>{switch(e){case"Muy Alto":case"Alto":return"text-green-600 bg-green-50";case"Medio-Alto":case"Medio":return"text-blue-600 bg-blue-50";case"Medio-Bajo":case"Bajo":case"Muy Bajo":return"text-red-600 bg-red-50";default:return"text-gray-600 bg-gray-50"}};return t.jsxs("div",{className:"container mx-auto py-6",children:[t.jsx("div",{className:"flex justify-between items-center mb-6",children:t.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"Mis Resultados"})}),d?t.jsxs("div",{className:"py-16 text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-500",children:"Cargando resultados..."})]}):t.jsx(t.Fragment,{children:0===n.length?t.jsx(r,{children:t.jsx(a,{children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-gray-500",children:"No has completado ningún test todavía."})})})}):t.jsx("div",{className:"grid grid-cols-1 gap-6",children:n.map((e=>{return t.jsxs(r,{className:"overflow-hidden",children:[t.jsx("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200",children:t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("div",{children:[t.jsx("h2",{className:"text-lg font-medium text-gray-800",children:e.testName}),t.jsxs("p",{className:"text-sm text-gray-500",children:["Completado: ",(n=e.completedDate,new Date(n).toLocaleDateString())]})]}),t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsx("div",{className:`px-3 py-1 rounded-full text-sm font-medium ${m(e.interpretation)}`,children:e.interpretation}),t.jsx("div",{className:"text-2xl font-bold "+(l=e.score,l>=80?"text-green-600":l>=60?"text-yellow-600":"text-red-600"),children:e.score})]})]})}),t.jsx(a,{children:t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("div",{className:"flex items-center space-x-6",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Percentil"}),t.jsx("p",{className:"text-lg font-medium",children:e.percentile})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Interpretación"}),t.jsx("p",{className:"text-lg font-medium",children:e.interpretation})]})]}),t.jsx("div",{children:t.jsx(i,{as:s,to:`/test/results/${e.id}`,variant:"primary",children:"Ver Detalles"})})]})})]},e.id);var l,n}))})})]})};export{n as default};
//# sourceMappingURL=Results-89872f5c.js.map
