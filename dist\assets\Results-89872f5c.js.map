{"version": 3, "file": "Results-89872f5c.js", "sources": ["../../src/pages/student/Results.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\nimport { Button } from '../../components/ui/Button';\nimport { useToast } from '../../hooks/useToast';\n\nconst Results = () => {\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const { showToast } = useToast();\n\n  useEffect(() => {\n    // Simular carga de datos\n    const fetchData = async () => {\n      try {\n        // En producción, aquí se haría una llamada a la API\n        await new Promise(resolve => setTimeout(resolve, 800));\n        \n        // Datos simulados\n        const mockResults = [\n          { \n            id: '12345',\n            testName: 'Test de Aptitud Verbal',\n            completedDate: new Date(2025, 3, 15),\n            score: 82,\n            percentile: 72,\n            interpretation: 'Alto',\n          },\n          { \n            id: '12346',\n            testName: 'Test de Razonamiento Lógico',\n            completedDate: new Date(2025, 3, 10),\n            score: 75,\n            percentile: 64,\n            interpretation: 'Medio-Alto',\n          },\n          { \n            id: '12347',\n            testName: 'Test de Aptitud Numérica',\n            completedDate: new Date(2025, 2, 25),\n            score: 68,\n            percentile: 54,\n            interpretation: 'Medio',\n          },\n        ];\n        \n        setResults(mockResults);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error al cargar resultados:', error);\n        showToast('Error al cargar los resultados', 'error');\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const formatDate = (date) => {\n    return new Date(date).toLocaleDateString();\n  };\n\n  const getScoreColor = (score) => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  const getScoreBgColor = (score) => {\n    if (score >= 80) return 'bg-green-100';\n    if (score >= 60) return 'bg-yellow-100';\n    return 'bg-red-100';\n  };\n\n  const getInterpretationColor = (interpretation) => {\n    switch (interpretation) {\n      case 'Muy Alto':\n      case 'Alto':\n        return 'text-green-600 bg-green-50';\n      case 'Medio-Alto':\n      case 'Medio':\n        return 'text-blue-600 bg-blue-50';\n      case 'Medio-Bajo':\n      case 'Bajo':\n      case 'Muy Bajo':\n        return 'text-red-600 bg-red-50';\n      default:\n        return 'text-gray-600 bg-gray-50';\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto py-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-800\">Mis Resultados</h1>\n      </div>\n\n      {loading ? (\n        <div className=\"py-16 text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p className=\"text-gray-500\">Cargando resultados...</p>\n        </div>\n      ) : (\n        <>\n          {results.length === 0 ? (\n            <Card>\n              <CardBody>\n                <div className=\"py-8 text-center\">\n                  <p className=\"text-gray-500\">No has completado ningún test todavía.</p>\n                </div>\n              </CardBody>\n            </Card>\n          ) : (\n            <div className=\"grid grid-cols-1 gap-6\">\n              {results.map((result) => (\n                <Card key={result.id} className=\"overflow-hidden\">\n                  <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200\">\n                    <div className=\"flex justify-between items-center\">\n                      <div>\n                        <h2 className=\"text-lg font-medium text-gray-800\">{result.testName}</h2>\n                        <p className=\"text-sm text-gray-500\">\n                          Completado: {formatDate(result.completedDate)}\n                        </p>\n                      </div>\n                      <div className=\"flex items-center space-x-4\">\n                        <div className={`px-3 py-1 rounded-full text-sm font-medium ${getInterpretationColor(result.interpretation)}`}>\n                          {result.interpretation}\n                        </div>\n                        <div className={`text-2xl font-bold ${getScoreColor(result.score)}`}>\n                          {result.score}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <CardBody>\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center space-x-6\">\n                        <div>\n                          <p className=\"text-sm text-gray-500\">Percentil</p>\n                          <p className=\"text-lg font-medium\">{result.percentile}</p>\n                        </div>\n                        <div>\n                          <p className=\"text-sm text-gray-500\">Interpretación</p>\n                          <p className=\"text-lg font-medium\">{result.interpretation}</p>\n                        </div>\n                      </div>\n                      <div>\n                        <Button\n                          as={Link}\n                          to={`/test/results/${result.id}`}\n                          variant=\"primary\"\n                        >\n                          Ver Detalles\n                        </Button>\n                      </div>\n                    </div>\n                  </CardBody>\n                </Card>\n              ))}\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default Results;"], "names": ["Results", "results", "setResults", "useState", "loading", "setLoading", "showToast", "useToast", "useEffect", "async", "Promise", "resolve", "setTimeout", "mockResults", "id", "testName", "completedDate", "Date", "score", "percentile", "interpretation", "error", "console", "getInterpretationColor", "jsxs", "className", "children", "jsx", "jsxRuntimeExports", "Fragment", "length", "Card", "CardBody", "map", "result", "date", "toLocaleDateString", "<PERSON><PERSON>", "as", "Link", "to", "variant"], "mappings": "wLAMA,MAAMA,EAAU,KACd,MAAOC,EAASC,GAAcC,EAAAA,SAAS,KAChCC,EAASC,GAAcF,YAAS,IACjCG,UAAEA,GAAcC,IAEtBC,EAAAA,WAAU,KAEUC,WACZ,UAEI,IAAIC,SAAQC,GAAWC,WAAWD,EAAS,OAGjD,MAAME,EAAc,CAClB,CACEC,GAAI,QACJC,SAAU,yBACVC,cAAe,IAAIC,KAAK,KAAM,EAAG,IACjCC,MAAO,GACPC,WAAY,GACZC,eAAgB,QAElB,CACEN,GAAI,QACJC,SAAU,8BACVC,cAAe,IAAIC,KAAK,KAAM,EAAG,IACjCC,MAAO,GACPC,WAAY,GACZC,eAAgB,cAElB,CACEN,GAAI,QACJC,SAAU,2BACVC,cAAe,IAAIC,KAAK,KAAM,EAAG,IACjCC,MAAO,GACPC,WAAY,GACZC,eAAgB,UAIpBlB,EAAWW,GACXR,GAAW,SACJgB,GACCC,QAAAD,MAAM,8BAA+BA,GAC7Cf,EAAU,iCAAkC,SAC5CD,GAAW,EACb,QAID,IAEG,MAgBAkB,EAA0BH,IAC9B,OAAQA,GACN,IAAK,WACL,IAAK,OACI,MAAA,6BACT,IAAK,aACL,IAAK,QACI,MAAA,2BACT,IAAK,aACL,IAAK,OACL,IAAK,WACI,MAAA,yBACT,QACS,MAAA,2BACX,EAIA,SAAAI,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,yCACbC,SAAAC,EAAAA,IAAC,MAAGF,UAAU,mCAAmCC,8BAGlDtB,EACCwB,EAAAJ,KAAC,MAAI,CAAAC,UAAU,oBACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,gFACdE,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAAsB,8BAGrDC,MAAAE,EAAAA,SAAA,CACGH,SAAmB,IAAXzB,EAAA6B,OACNF,EAAAD,IAAAI,EAAA,CACCL,SAACC,EAAAA,IAAAK,EAAA,CACCN,eAAC,MAAA,CAAID,UAAU,mBACbC,eAAC,IAAE,CAAAD,UAAU,gBAAgBC,SAAA,uDAKlC,MAAI,CAAAD,UAAU,yBACZC,SAAAzB,EAAQgC,KAAKC,IACXV,cAAAO,EAAA,CAAqBN,UAAU,kBAC9BC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,gFACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,oCACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAAAC,EAAAA,IAAC,KAAG,CAAAF,UAAU,oCAAqCC,SAAAQ,EAAOnB,aAC1DS,KAAC,IAAE,CAAAC,UAAU,wBAAwBC,SAAA,CAAA,gBA9DvCS,EA+D4BD,EAAOlB,cA9D9C,IAAIC,KAAKkB,GAAMC,8BAiEJZ,KAAC,MAAI,CAAAC,UAAU,8BACbC,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAIF,UAAW,8CAA8CF,EAAuBW,EAAOd,kBACzFM,SAAAQ,EAAOd,iBAEVO,EAAAA,IAAC,MAAI,CAAAF,UAAW,uBAlEfP,EAkEmDgB,EAAOhB,MAjE3EA,GAAS,GAAW,iBACpBA,GAAS,GAAW,kBACjB,gBAgEgBQ,SAAAQ,EAAOhB,gBAKfS,MAAAK,EAAA,CACCN,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,8BACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAEF,UAAU,wBAAwBC,SAAS,cAC7CC,EAAAA,IAAA,IAAA,CAAEF,UAAU,sBAAuBC,WAAOP,uBAE5C,MACC,CAAAO,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAEF,UAAU,wBAAwBC,SAAc,mBAClDC,EAAAA,IAAA,IAAA,CAAEF,UAAU,sBAAuBC,WAAON,6BAG9C,MACC,CAAAM,SAAAE,EAAAD,IAACU,EAAA,CACCC,GAAIC,EACJC,GAAI,iBAAiBN,EAAOpB,KAC5B2B,QAAQ,UACTf,SAAA,0BApCEQ,EAAOpB,IArDV,IAACI,EAJHiB,CAoGP,UAKX"}