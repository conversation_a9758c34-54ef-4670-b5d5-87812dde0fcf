import{j as s}from"./index-165d7974.js";import{C as e,a,b as t}from"./Card-54419bd4.js";const i=()=>s.jsxs("div",{className:"container mx-auto py-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Gestión de Estudiantes"}),s.jsxs(e,{children:[s.jsx(a,{children:s.jsx("h2",{className:"text-lg font-medium",children:"Lista de Estudiantes"})}),s.jsx(t,{children:s.jsx("p",{className:"text-gray-600",children:"Esta sección permitirá gestionar los estudiantes asignados al profesional (componente en desarrollo)."})})]})]});export{i as default};
//# sourceMappingURL=Students-3d966307.js.map
