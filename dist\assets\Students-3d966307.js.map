{"version": 3, "file": "Students-3d966307.js", "sources": ["../../src/pages/professional/Students.jsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\n\nconst Students = () => {\n  return (\n    <div className=\"container mx-auto py-6\">\n      <h1 className=\"text-2xl font-bold text-gray-800 mb-6\">Gestión de Estudiantes</h1>\n      \n      <Card>\n        <CardHeader>\n          <h2 className=\"text-lg font-medium\">Lista de Estudiantes</h2>\n        </CardHeader>\n        <CardBody>\n          <p className=\"text-gray-600\">\n            Esta sección permitirá gestionar los estudiantes asignados al profesional (componente en desarrollo).\n          </p>\n        </CardBody>\n      </Card>\n    </div>\n  );\n};\n\nexport default Students;"], "names": ["Students", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody"], "mappings": "wFAGA,MAAMA,EAAW,MAEbC,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAsB,kCAE3EE,EACC,CAAAF,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,0CAErCK,EACC,CAAAL,SAAAI,EAAAH,IAAC,KAAEF,UAAU,gBAAgBC"}