import{e,s as a,r as s,j as o}from"./index-165d7974.js";import{C as t,a as n,b as i}from"./Card-54419bd4.js";import{B as r}from"./Button-9c521291.js";const c=(e,a,s)=>{console.log(`\n--- ${e} ${a} ---`),s.error?(console.error(`Error: ${s.error.message}`),console.error(s.error.original)):(console.log("Éxito!"),console.log(s.data),s.isOffline&&console.log("(Operación realizada en modo offline)"))},l=async()=>{console.log("\n=== PRUEBAS DE INSTITUCIONES ===");const a=await e.getInstitutions();c("GET","instituciones",a);const s=await e.createInstitution({nombre:`Test Institución ${Date.now()}`,direccion:"Dirección de prueba",telefono:"123456789"});if(c("CREATE","institución",s),s.data&&!s.error){const a=s.data.id,o=await e.updateInstitution(a,{nombre:`${s.data.nombre} (Actualizada)`,direccion:"Dirección actualizada"});c("UPDATE","institución",o);const t=await e.deleteInstitution(a);c("DELETE","institución",t)}return"Pruebas de instituciones completadas"},d=async()=>{console.log("\n=== PRUEBAS DE PSICÓLOGOS ===");const s=await e.getInstitutions();if(!s.data||0===s.data.length)return console.error("No hay instituciones disponibles para crear psicólogo"),"Error: No hay instituciones disponibles";const o=await e.getPsychologists();c("GET","psicólogos",o);const t=Date.now(),n=`test.psicologo.${t}@example.com`,{data:i,error:r}=await a.auth.signUp({email:n,password:"Temporal123!",options:{data:{rol:"psicologo",nombre_completo:`Test Psicólogo ${t}`}}});if(r)return console.error("Error al crear usuario para psicólogo:",r),"Error al crear usuario para psicólogo";const l=await e.createPsychologist({nombre:"Test",apellidos:`Psicólogo ${t}`,email:n,documento_identidad:`DOC-${t}`,telefono:"987654321",institucion_id:s.data[0].id,usuario_id:i.user.id});if(c("CREATE","psicólogo",l),l.data&&!l.error){const a=l.data.id,s=await e.updatePsychologist(a,{nombre:"Test (Actualizado)",apellidos:`Psicólogo ${t}`,documento_identidad:`DOC-${t}-UPD`,telefono:"987654322"});c("UPDATE","psicólogo",s);const o=await e.deletePsychologist(a);c("DELETE","psicólogo",o)}return"Pruebas de psicólogos completadas"},u=async()=>{console.log("\n=== PRUEBAS DE PACIENTES ===");const a=await e.getInstitutions();if(!a.data||0===a.data.length)return console.error("No hay instituciones disponibles para crear paciente"),"Error: No hay instituciones disponibles";const s=await e.getPsychologists(),o=s.data&&s.data.length>0?s.data[0].id:null,t=await e.getPatients();c("GET","pacientes",t);const n=Date.now(),i=await e.createPatient({nombre:`Test Paciente ${n}`,fecha_nacimiento:"2000-01-01",genero:"Masculino",institucion_id:a.data[0].id,psicologo_id:o,notas:"Paciente de prueba"});if(c("CREATE","paciente",i),i.data&&!i.error){const a=i.data.id,s=await e.updatePatient(a,{nombre:`Test Paciente ${n} (Actualizado)`,fecha_nacimiento:"2000-02-02",notas:"Paciente de prueba actualizado"});c("UPDATE","paciente",s);const o=await e.deletePatient(a);c("DELETE","paciente",o)}return"Pruebas de pacientes completadas"},p=async()=>{console.log("\n=== PRUEBAS DE SINCRONIZACIÓN ===");const a=e.getSyncStatus();if(console.log("Estado de sincronización:"),console.log(`Total operaciones pendientes: ${a.pendingCount}`),console.log("Conteo por tipo:",a.counts.byType),console.log("Conteo por entidad:",a.counts.byEntity),console.log("Último intento de sincronización:",new Date(a.lastSyncAttempt||0).toLocaleString()),a.pendingCount>0){console.log("\nOperaciones pendientes:"),a.operations.forEach(((e,a)=>{console.log(`${a+1}. ${e.type} ${e.entity} - ${e.name||e.id} (${new Date(e.timestamp).toLocaleString()})`)})),console.log("\nSincronizando operaciones pendientes...");const s=await e.syncPendingOperations();console.log(`Sincronización completada. Éxito: ${s.success}, Sincronizadas: ${s.syncedCount}, Errores: ${s.errors.length}`),s.errors.length>0&&(console.log("\nErrores de sincronización:"),s.errors.forEach(((e,a)=>{console.log(`${a+1}. ${e.operation.type} ${e.operation.entity} - ${e.error.message}`)})))}return"Pruebas de sincronización completadas"},m=async()=>{try{console.log("Iniciando pruebas del servicio enhancedSupabaseService...");const{data:{user:e}}=await a.auth.getUser();return e?(console.log(`Usuario autenticado: ${e.email}`),await l(),await d(),await u(),await p(),console.log("\nPruebas completadas!"),"Todas las pruebas completadas con éxito"):(console.error("Error: Usuario no autenticado. Inicie sesión antes de ejecutar las pruebas."),"Error: Usuario no autenticado")}catch(e){return console.error("Error al ejecutar pruebas:",e),`Error al ejecutar pruebas: ${e.message}`}},g=l,b=d,h=u,x=p,E=()=>{const[e,a]=s.useState(!1),[c,l]=s.useState([]),[d,u]=s.useState("all"),p=[{value:"all",label:"Todas las pruebas"},{value:"instituciones",label:"Pruebas de Instituciones"},{value:"psicologos",label:"Pruebas de Psicólogos"},{value:"pacientes",label:"Pruebas de Pacientes"},{value:"sincronizacion",label:"Pruebas de Sincronización"}];return o.jsxs("div",{className:"container mx-auto py-6",children:[o.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Página de Pruebas"}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[o.jsxs(t,{className:"md:col-span-1",children:[o.jsx(n,{children:o.jsx("h2",{className:"text-lg font-medium",children:"Panel de Control"})}),o.jsx(i,{children:o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Seleccionar prueba"}),o.jsx("select",{className:"form-select w-full",value:d,onChange:e=>u(e.target.value),disabled:e,children:p.map((e=>o.jsx("option",{value:e.value,children:e.label},e.value)))})]}),o.jsx(r,{variant:"primary",className:"w-full",onClick:()=>(async e=>{a(!0);try{let a;switch(e){case"all":a=await m();break;case"instituciones":a=await g();break;case"psicologos":a=await b();break;case"pacientes":a=await h();break;case"sincronizacion":a=await x();break;default:a="Prueba no válida"}l((s=>[{id:Date.now(),test:e,result:a,timestamp:(new Date).toLocaleString()},...s]))}catch(s){console.error(`Error al ejecutar prueba ${e}:`,s),l((a=>[{id:Date.now(),test:e,result:`Error: ${s.message}`,timestamp:(new Date).toLocaleString(),error:!0},...a]))}finally{a(!1)}})(d),disabled:e,children:e?"Ejecutando...":"Ejecutar Prueba"}),o.jsxs("div",{className:"text-sm text-gray-500",children:[o.jsx("p",{children:"Esta página permite ejecutar pruebas manuales para verificar el funcionamiento de las operaciones CRUD."}),o.jsx("p",{className:"mt-2",children:"Las pruebas se ejecutan contra la base de datos real, así que úsala con precaución."})]})]})})]}),o.jsxs(t,{className:"md:col-span-2",children:[o.jsx(n,{children:o.jsx("h2",{className:"text-lg font-medium",children:"Resultados"})}),o.jsx(i,{children:0===c.length?o.jsx("div",{className:"text-center py-8 text-gray-500",children:"No hay resultados. Ejecuta una prueba para ver los resultados aquí."}):o.jsx("div",{className:"space-y-4",children:c.map((e=>{var a;return o.jsxs("div",{className:"p-4 rounded-lg border "+(e.error?"border-red-200 bg-red-50":"border-green-200 bg-green-50"),children:[o.jsxs("div",{className:"flex justify-between items-start",children:[o.jsxs("div",{children:[o.jsx("h3",{className:"font-medium",children:(null==(a=p.find((a=>a.value===e.test)))?void 0:a.label)||e.test}),o.jsx("p",{className:"text-sm text-gray-500",children:e.timestamp})]}),o.jsx("span",{className:"px-2 py-1 text-xs rounded-full "+(e.error?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:e.error?"Error":"Éxito"})]}),o.jsx("div",{className:"mt-2",children:o.jsx("pre",{className:"text-sm whitespace-pre-wrap bg-white p-2 rounded border",children:e.result})})]},e.id)}))})})]})]}),o.jsx("div",{className:"mt-6 text-sm text-gray-500",children:o.jsx("p",{children:"Nota: Los resultados detallados de las pruebas se muestran en la consola del navegador."})})]})};export{E as default};
//# sourceMappingURL=TestPage-17564ea0.js.map
