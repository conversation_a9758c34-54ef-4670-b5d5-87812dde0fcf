{"version": 3, "file": "TestPage-17564ea0.js", "sources": ["../../src/tests/manualTests.js", "../../src/pages/admin/TestPage.jsx"], "sourcesContent": ["/**\n * Pruebas manuales para el servicio enhancedSupabaseService\n * Este archivo contiene funciones que pueden ejecutarse manualmente\n * para verificar el funcionamiento de las operaciones CRUD.\n */\n\nimport enhancedSupabaseService from '../services/enhancedSupabaseService';\nimport { supabase } from '../api/supabaseClient';\n\n// Función auxiliar para imprimir resultados\nconst printResult = (operation, entity, result) => {\n  console.log(`\\n--- ${operation} ${entity} ---`);\n  if (result.error) {\n    console.error(`Error: ${result.error.message}`);\n    console.error(result.error.original);\n  } else {\n    console.log('Éxito!');\n    console.log(result.data);\n    if (result.isOffline) {\n      console.log('(Operación realizada en modo offline)');\n    }\n  }\n};\n\n// Función para ejecutar pruebas de instituciones\nexport const testInstituciones = async () => {\n  console.log('\\n=== PRUEBAS DE INSTITUCIONES ===');\n  \n  // Obtener instituciones\n  const getResult = await enhancedSupabaseService.getInstitutions();\n  printResult('GET', 'instituciones', getResult);\n  \n  // Crear institución\n  const createResult = await enhancedSupabaseService.createInstitution({\n    nombre: `Test Institución ${Date.now()}`,\n    direccion: 'Dirección de prueba',\n    telefono: '123456789'\n  });\n  printResult('CREATE', 'institución', createResult);\n  \n  if (createResult.data && !createResult.error) {\n    const id = createResult.data.id;\n    \n    // Actualizar institución\n    const updateResult = await enhancedSupabaseService.updateInstitution(id, {\n      nombre: `${createResult.data.nombre} (Actualizada)`,\n      direccion: 'Dirección actualizada'\n    });\n    printResult('UPDATE', 'institución', updateResult);\n    \n    // Eliminar institución\n    const deleteResult = await enhancedSupabaseService.deleteInstitution(id);\n    printResult('DELETE', 'institución', deleteResult);\n  }\n  \n  return 'Pruebas de instituciones completadas';\n};\n\n// Función para ejecutar pruebas de psicólogos\nexport const testPsicologos = async () => {\n  console.log('\\n=== PRUEBAS DE PSICÓLOGOS ===');\n  \n  // Obtener instituciones para asociar el psicólogo\n  const instResult = await enhancedSupabaseService.getInstitutions();\n  if (!instResult.data || instResult.data.length === 0) {\n    console.error('No hay instituciones disponibles para crear psicólogo');\n    return 'Error: No hay instituciones disponibles';\n  }\n  \n  // Obtener psicólogos\n  const getResult = await enhancedSupabaseService.getPsychologists();\n  printResult('GET', 'psicólogos', getResult);\n  \n  // Crear usuario para el psicólogo\n  const timestamp = Date.now();\n  const email = `test.psicologo.${timestamp}@example.com`;\n  const { data: authData, error: authError } = await supabase.auth.signUp({\n    email,\n    password: 'Temporal123!',\n    options: {\n      data: {\n        rol: 'psicologo',\n        nombre_completo: `Test Psicólogo ${timestamp}`\n      }\n    }\n  });\n  \n  if (authError) {\n    console.error('Error al crear usuario para psicólogo:', authError);\n    return 'Error al crear usuario para psicólogo';\n  }\n  \n  // Crear psicólogo\n  const createResult = await enhancedSupabaseService.createPsychologist({\n    nombre: `Test`,\n    apellidos: `Psicólogo ${timestamp}`,\n    email,\n    documento_identidad: `DOC-${timestamp}`,\n    telefono: '987654321',\n    institucion_id: instResult.data[0].id,\n    usuario_id: authData.user.id\n  });\n  printResult('CREATE', 'psicólogo', createResult);\n  \n  if (createResult.data && !createResult.error) {\n    const id = createResult.data.id;\n    \n    // Actualizar psicólogo\n    const updateResult = await enhancedSupabaseService.updatePsychologist(id, {\n      nombre: `Test (Actualizado)`,\n      apellidos: `Psicólogo ${timestamp}`,\n      documento_identidad: `DOC-${timestamp}-UPD`,\n      telefono: '987654322'\n    });\n    printResult('UPDATE', 'psicólogo', updateResult);\n    \n    // Eliminar psicólogo\n    const deleteResult = await enhancedSupabaseService.deletePsychologist(id);\n    printResult('DELETE', 'psicólogo', deleteResult);\n  }\n  \n  return 'Pruebas de psicólogos completadas';\n};\n\n// Función para ejecutar pruebas de pacientes\nexport const testPacientes = async () => {\n  console.log('\\n=== PRUEBAS DE PACIENTES ===');\n  \n  // Obtener instituciones para asociar el paciente\n  const instResult = await enhancedSupabaseService.getInstitutions();\n  if (!instResult.data || instResult.data.length === 0) {\n    console.error('No hay instituciones disponibles para crear paciente');\n    return 'Error: No hay instituciones disponibles';\n  }\n  \n  // Obtener psicólogos para asociar el paciente (opcional)\n  const psicoResult = await enhancedSupabaseService.getPsychologists();\n  const psicoId = psicoResult.data && psicoResult.data.length > 0 ? psicoResult.data[0].id : null;\n  \n  // Obtener pacientes\n  const getResult = await enhancedSupabaseService.getPatients();\n  printResult('GET', 'pacientes', getResult);\n  \n  // Crear paciente\n  const timestamp = Date.now();\n  const createResult = await enhancedSupabaseService.createPatient({\n    nombre: `Test Paciente ${timestamp}`,\n    fecha_nacimiento: '2000-01-01',\n    genero: 'Masculino',\n    institucion_id: instResult.data[0].id,\n    psicologo_id: psicoId,\n    notas: 'Paciente de prueba'\n  });\n  printResult('CREATE', 'paciente', createResult);\n  \n  if (createResult.data && !createResult.error) {\n    const id = createResult.data.id;\n    \n    // Actualizar paciente\n    const updateResult = await enhancedSupabaseService.updatePatient(id, {\n      nombre: `Test Paciente ${timestamp} (Actualizado)`,\n      fecha_nacimiento: '2000-02-02',\n      notas: 'Paciente de prueba actualizado'\n    });\n    printResult('UPDATE', 'paciente', updateResult);\n    \n    // Eliminar paciente\n    const deleteResult = await enhancedSupabaseService.deletePatient(id);\n    printResult('DELETE', 'paciente', deleteResult);\n  }\n  \n  return 'Pruebas de pacientes completadas';\n};\n\n// Función para ejecutar pruebas de sincronización\nexport const testSincronizacion = async () => {\n  console.log('\\n=== PRUEBAS DE SINCRONIZACIÓN ===');\n  \n  // Obtener estado de sincronización\n  const statusResult = enhancedSupabaseService.getSyncStatus();\n  console.log('Estado de sincronización:');\n  console.log(`Total operaciones pendientes: ${statusResult.pendingCount}`);\n  console.log('Conteo por tipo:', statusResult.counts.byType);\n  console.log('Conteo por entidad:', statusResult.counts.byEntity);\n  console.log('Último intento de sincronización:', new Date(statusResult.lastSyncAttempt || 0).toLocaleString());\n  \n  if (statusResult.pendingCount > 0) {\n    console.log('\\nOperaciones pendientes:');\n    statusResult.operations.forEach((op, index) => {\n      console.log(`${index + 1}. ${op.type} ${op.entity} - ${op.name || op.id} (${new Date(op.timestamp).toLocaleString()})`);\n    });\n    \n    // Sincronizar operaciones pendientes\n    console.log('\\nSincronizando operaciones pendientes...');\n    const syncResult = await enhancedSupabaseService.syncPendingOperations();\n    console.log(`Sincronización completada. Éxito: ${syncResult.success}, Sincronizadas: ${syncResult.syncedCount}, Errores: ${syncResult.errors.length}`);\n    \n    if (syncResult.errors.length > 0) {\n      console.log('\\nErrores de sincronización:');\n      syncResult.errors.forEach((err, index) => {\n        console.log(`${index + 1}. ${err.operation.type} ${err.operation.entity} - ${err.error.message}`);\n      });\n    }\n  }\n  \n  return 'Pruebas de sincronización completadas';\n};\n\n// Función principal para ejecutar todas las pruebas\nexport const runAllTests = async () => {\n  try {\n    console.log('Iniciando pruebas del servicio enhancedSupabaseService...');\n    \n    // Verificar autenticación\n    const { data: { user } } = await supabase.auth.getUser();\n    if (!user) {\n      console.error('Error: Usuario no autenticado. Inicie sesión antes de ejecutar las pruebas.');\n      return 'Error: Usuario no autenticado';\n    }\n    \n    console.log(`Usuario autenticado: ${user.email}`);\n    \n    // Ejecutar pruebas\n    await testInstituciones();\n    await testPsicologos();\n    await testPacientes();\n    await testSincronizacion();\n    \n    console.log('\\nPruebas completadas!');\n    return 'Todas las pruebas completadas con éxito';\n  } catch (error) {\n    console.error('Error al ejecutar pruebas:', error);\n    return `Error al ejecutar pruebas: ${error.message}`;\n  }\n};\n\n// Exportar objeto con todas las funciones de prueba\nexport default {\n  runAllTests,\n  testInstituciones,\n  testPsicologos,\n  testPacientes,\n  testSincronizacion\n};\n", "import React, { useState } from 'react';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\nimport { Button } from '../../components/ui/Button';\nimport manualTests from '../../tests/manualTests';\n\n/**\n * Página de pruebas para el servicio enhancedSupabaseService\n * Esta página permite ejecutar pruebas manuales para verificar el funcionamiento\n * de las operaciones CRUD para instituciones, psicólogos y pacientes.\n */\nconst TestPage = () => {\n  const [loading, setLoading] = useState(false);\n  const [results, setResults] = useState([]);\n  const [selectedTest, setSelectedTest] = useState('all');\n\n  // Función para ejecutar una prueba\n  const runTest = async (testName) => {\n    setLoading(true);\n    try {\n      let result;\n      \n      switch (testName) {\n        case 'all':\n          result = await manualTests.runAllTests();\n          break;\n        case 'instituciones':\n          result = await manualTests.testInstituciones();\n          break;\n        case 'psicologos':\n          result = await manualTests.testPsicologos();\n          break;\n        case 'pacientes':\n          result = await manualTests.testPacientes();\n          break;\n        case 'sincronizacion':\n          result = await manualTests.testSincronizacion();\n          break;\n        default:\n          result = 'Prueba no válida';\n      }\n      \n      // Agregar resultado a la lista\n      setResults(prev => [\n        {\n          id: Date.now(),\n          test: testName,\n          result,\n          timestamp: new Date().toLocaleString()\n        },\n        ...prev\n      ]);\n    } catch (error) {\n      console.error(`Error al ejecutar prueba ${testName}:`, error);\n      setResults(prev => [\n        {\n          id: Date.now(),\n          test: testName,\n          result: `Error: ${error.message}`,\n          timestamp: new Date().toLocaleString(),\n          error: true\n        },\n        ...prev\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Opciones de pruebas\n  const testOptions = [\n    { value: 'all', label: 'Todas las pruebas' },\n    { value: 'instituciones', label: 'Pruebas de Instituciones' },\n    { value: 'psicologos', label: 'Pruebas de Psicólogos' },\n    { value: 'pacientes', label: 'Pruebas de Pacientes' },\n    { value: 'sincronizacion', label: 'Pruebas de Sincronización' }\n  ];\n\n  return (\n    <div className=\"container mx-auto py-6\">\n      <h1 className=\"text-2xl font-bold text-gray-800 mb-6\">Página de Pruebas</h1>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        {/* Panel de control */}\n        <Card className=\"md:col-span-1\">\n          <CardHeader>\n            <h2 className=\"text-lg font-medium\">Panel de Control</h2>\n          </CardHeader>\n          <CardBody>\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Seleccionar prueba\n                </label>\n                <select\n                  className=\"form-select w-full\"\n                  value={selectedTest}\n                  onChange={(e) => setSelectedTest(e.target.value)}\n                  disabled={loading}\n                >\n                  {testOptions.map(option => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n              \n              <Button\n                variant=\"primary\"\n                className=\"w-full\"\n                onClick={() => runTest(selectedTest)}\n                disabled={loading}\n              >\n                {loading ? 'Ejecutando...' : 'Ejecutar Prueba'}\n              </Button>\n              \n              <div className=\"text-sm text-gray-500\">\n                <p>Esta página permite ejecutar pruebas manuales para verificar el funcionamiento de las operaciones CRUD.</p>\n                <p className=\"mt-2\">Las pruebas se ejecutan contra la base de datos real, así que úsala con precaución.</p>\n              </div>\n            </div>\n          </CardBody>\n        </Card>\n        \n        {/* Resultados */}\n        <Card className=\"md:col-span-2\">\n          <CardHeader>\n            <h2 className=\"text-lg font-medium\">Resultados</h2>\n          </CardHeader>\n          <CardBody>\n            {results.length === 0 ? (\n              <div className=\"text-center py-8 text-gray-500\">\n                No hay resultados. Ejecuta una prueba para ver los resultados aquí.\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {results.map(item => (\n                  <div \n                    key={item.id} \n                    className={`p-4 rounded-lg border ${item.error ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}`}\n                  >\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <h3 className=\"font-medium\">\n                          {testOptions.find(opt => opt.value === item.test)?.label || item.test}\n                        </h3>\n                        <p className=\"text-sm text-gray-500\">{item.timestamp}</p>\n                      </div>\n                      <span className={`px-2 py-1 text-xs rounded-full ${item.error ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>\n                        {item.error ? 'Error' : 'Éxito'}\n                      </span>\n                    </div>\n                    <div className=\"mt-2\">\n                      <pre className=\"text-sm whitespace-pre-wrap bg-white p-2 rounded border\">\n                        {item.result}\n                      </pre>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardBody>\n        </Card>\n      </div>\n      \n      <div className=\"mt-6 text-sm text-gray-500\">\n        <p>Nota: Los resultados detallados de las pruebas se muestran en la consola del navegador.</p>\n      </div>\n    </div>\n  );\n};\n\nexport default TestPage;\n"], "names": ["printResult", "operation", "entity", "result", "console", "log", "error", "message", "original", "data", "isOffline", "testInstituciones", "async", "getResult", "enhancedSupabaseService", "getInstitutions", "createResult", "createInstitution", "nombre", "Date", "now", "direccion", "telefono", "id", "updateResult", "updateInstitution", "deleteResult", "deleteInstitution", "testPsicologos", "instResult", "length", "getPsychologists", "timestamp", "email", "authData", "authError", "supabase", "auth", "signUp", "password", "options", "rol", "nombre_completo", "createPsychologist", "<PERSON><PERSON><PERSON><PERSON>", "documento_identidad", "institucion_id", "usuario_id", "user", "updatePsychologist", "deletePsychologist", "testPacientes", "psicoResult", "psicoId", "getPatients", "createPatient", "fecha_nacimiento", "genero", "psicologo_id", "notas", "updatePatient", "deletePatient", "testSincronizacion", "statusResult", "getSyncStatus", "pendingCount", "counts", "byType", "byEntity", "lastSyncAttempt", "toLocaleString", "operations", "for<PERSON>ach", "op", "index", "type", "name", "syncResult", "syncPendingOperations", "success", "syncedCount", "errors", "err", "manualTests", "getUser", "TestPage", "loading", "setLoading", "useState", "results", "setResults", "selectedTest", "setSelectedTest", "testOptions", "value", "label", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody", "onChange", "e", "target", "disabled", "map", "option", "<PERSON><PERSON>", "variant", "onClick", "testName", "prev", "test", "runTest", "item", "_a", "find", "opt"], "mappings": "sJAUA,MAAMA,EAAc,CAACC,EAAWC,EAAQC,KACtCC,QAAQC,IAAI,SAASJ,KAAaC,SAC9BC,EAAOG,OACTF,QAAQE,MAAM,UAAUH,EAAOG,MAAMC,WAC7BH,QAAAE,MAAMH,EAAOG,MAAME,YAE3BJ,QAAQC,IAAI,UACJD,QAAAC,IAAIF,EAAOM,MACfN,EAAOO,WACTN,QAAQC,IAAI,yCAEf,EAIUM,EAAoBC,UAC/BR,QAAQC,IAAI,sCAGN,MAAAQ,QAAkBC,EAAwBC,kBACpCf,EAAA,MAAO,gBAAiBa,GAG9B,MAAAG,QAAqBF,EAAwBG,kBAAkB,CACnEC,OAAQ,oBAAoBC,KAAKC,QACjCC,UAAW,sBACXC,SAAU,cAIZ,GAFYtB,EAAA,SAAU,cAAegB,GAEjCA,EAAaP,OAASO,EAAaV,MAAO,CACtC,MAAAiB,EAAKP,EAAaP,KAAKc,GAGvBC,QAAqBV,EAAwBW,kBAAkBF,EAAI,CACvEL,OAAQ,GAAGF,EAAaP,KAAKS,uBAC7BG,UAAW,0BAEDrB,EAAA,SAAU,cAAewB,GAGrC,MAAME,QAAqBZ,EAAwBa,kBAAkBJ,GACzDvB,EAAA,SAAU,cAAe0B,EACtC,CAEM,MAAA,sCAAA,EAIIE,EAAiBhB,UAC5BR,QAAQC,IAAI,mCAGN,MAAAwB,QAAmBf,EAAwBC,kBACjD,IAAKc,EAAWpB,MAAmC,IAA3BoB,EAAWpB,KAAKqB,OAE/B,OADP1B,QAAQE,MAAM,yDACP,0CAIH,MAAAO,QAAkBC,EAAwBiB,mBACpC/B,EAAA,MAAO,aAAca,GAG3B,MAAAmB,EAAYb,KAAKC,MACjBa,EAAQ,kBAAkBD,iBACxBvB,KAAMyB,EAAU5B,MAAO6B,SAAoBC,EAASC,KAAKC,OAAO,CACtEL,QACAM,SAAU,eACVC,QAAS,CACP/B,KAAM,CACJgC,IAAK,YACLC,gBAAiB,kBAAkBV,QAKzC,GAAIG,EAEK,OADC/B,QAAAE,MAAM,yCAA0C6B,GACjD,wCAIH,MAAAnB,QAAqBF,EAAwB6B,mBAAmB,CACpEzB,OAAQ,OACR0B,UAAW,aAAaZ,IACxBC,QACAY,oBAAqB,OAAOb,IAC5BV,SAAU,YACVwB,eAAgBjB,EAAWpB,KAAK,GAAGc,GACnCwB,WAAYb,EAASc,KAAKzB,KAI5B,GAFYvB,EAAA,SAAU,YAAagB,GAE/BA,EAAaP,OAASO,EAAaV,MAAO,CACtC,MAAAiB,EAAKP,EAAaP,KAAKc,GAGvBC,QAAqBV,EAAwBmC,mBAAmB1B,EAAI,CACxEL,OAAQ,qBACR0B,UAAW,aAAaZ,IACxBa,oBAAqB,OAAOb,QAC5BV,SAAU,cAEAtB,EAAA,SAAU,YAAawB,GAGnC,MAAME,QAAqBZ,EAAwBoC,mBAAmB3B,GAC1DvB,EAAA,SAAU,YAAa0B,EACpC,CAEM,MAAA,mCAAA,EAIIyB,EAAgBvC,UAC3BR,QAAQC,IAAI,kCAGN,MAAAwB,QAAmBf,EAAwBC,kBACjD,IAAKc,EAAWpB,MAAmC,IAA3BoB,EAAWpB,KAAKqB,OAE/B,OADP1B,QAAQE,MAAM,wDACP,0CAIH,MAAA8C,QAAoBtC,EAAwBiB,mBAC5CsB,EAAUD,EAAY3C,MAAQ2C,EAAY3C,KAAKqB,OAAS,EAAIsB,EAAY3C,KAAK,GAAGc,GAAK,KAGrFV,QAAkBC,EAAwBwC,cACpCtD,EAAA,MAAO,YAAaa,GAG1B,MAAAmB,EAAYb,KAAKC,MACjBJ,QAAqBF,EAAwByC,cAAc,CAC/DrC,OAAQ,iBAAiBc,IACzBwB,iBAAkB,aAClBC,OAAQ,YACRX,eAAgBjB,EAAWpB,KAAK,GAAGc,GACnCmC,aAAcL,EACdM,MAAO,uBAIT,GAFY3D,EAAA,SAAU,WAAYgB,GAE9BA,EAAaP,OAASO,EAAaV,MAAO,CACtC,MAAAiB,EAAKP,EAAaP,KAAKc,GAGvBC,QAAqBV,EAAwB8C,cAAcrC,EAAI,CACnEL,OAAQ,iBAAiBc,kBACzBwB,iBAAkB,aAClBG,MAAO,mCAEG3D,EAAA,SAAU,WAAYwB,GAGlC,MAAME,QAAqBZ,EAAwB+C,cAActC,GACrDvB,EAAA,SAAU,WAAY0B,EACnC,CAEM,MAAA,kCAAA,EAIIoC,EAAqBlD,UAChCR,QAAQC,IAAI,uCAGN,MAAA0D,EAAejD,EAAwBkD,gBAOzC,GANJ5D,QAAQC,IAAI,6BACZD,QAAQC,IAAI,iCAAiC0D,EAAaE,gBAC1D7D,QAAQC,IAAI,mBAAoB0D,EAAaG,OAAOC,QACpD/D,QAAQC,IAAI,sBAAuB0D,EAAaG,OAAOE,UAC/ChE,QAAAC,IAAI,oCAAqC,IAAIc,KAAK4C,EAAaM,iBAAmB,GAAGC,kBAEzFP,EAAaE,aAAe,EAAG,CACjC7D,QAAQC,IAAI,6BACZ0D,EAAaQ,WAAWC,SAAQ,CAACC,EAAIC,KAC3BtE,QAAAC,IAAI,GAAGqE,EAAQ,MAAMD,EAAGE,QAAQF,EAAGvE,YAAYuE,EAAGG,MAAQH,EAAGlD,OAAO,IAAIJ,KAAKsD,EAAGzC,WAAWsC,oBAAmB,IAIxHlE,QAAQC,IAAI,6CACN,MAAAwE,QAAmB/D,EAAwBgE,wBACzC1E,QAAAC,IAAI,qCAAqCwE,EAAWE,2BAA2BF,EAAWG,yBAAyBH,EAAWI,OAAOnD,UAEzI+C,EAAWI,OAAOnD,OAAS,IAC7B1B,QAAQC,IAAI,gCACZwE,EAAWI,OAAOT,SAAQ,CAACU,EAAKR,KAC9BtE,QAAQC,IAAI,GAAGqE,EAAQ,MAAMQ,EAAIjF,UAAU0E,QAAQO,EAAIjF,UAAUC,YAAYgF,EAAI5E,MAAMC,UAAS,IAGrG,CAEM,MAAA,uCAAA,EAgCM4E,EA5BYvE,UACrB,IACFR,QAAQC,IAAI,6DAGN,MAAEI,MAAMuC,KAAEA,UAAiBZ,EAASC,KAAK+C,UAC/C,OAAKpC,GAKL5C,QAAQC,IAAI,wBAAwB2C,EAAKf,eAGnCtB,UACAiB,UACAuB,UACAW,IAEN1D,QAAQC,IAAI,0BACL,4CAbLD,QAAQE,MAAM,+EACP,gCAaV,OAAQA,GAEA,OADCF,QAAAE,MAAM,6BAA8BA,GACrC,8BAA8BA,EAAMC,SAC5C,GAIY4E,EAEbxE,EAFawE,EAGbvD,EAHauD,EAIbhC,EAJagC,EAKbrB,ECxOIuB,EAAW,KACf,MAAOC,EAASC,GAAcC,YAAS,IAChCC,EAASC,GAAcF,EAAAA,SAAS,KAChCG,EAAcC,GAAmBJ,WAAS,OAwD3CK,EAAc,CAClB,CAAEC,MAAO,MAAOC,MAAO,qBACvB,CAAED,MAAO,gBAAiBC,MAAO,4BACjC,CAAED,MAAO,aAAcC,MAAO,yBAC9B,CAAED,MAAO,YAAaC,MAAO,wBAC7B,CAAED,MAAO,iBAAkBC,MAAO,8BAIlC,SAAAC,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAiB,wBAEvEF,KAAC,MAAI,CAAAC,UAAU,wCAEbC,SAAA,GAACF,KAAAI,EAAA,CAAKH,UAAU,gBACdC,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,gCAErCC,MAAAI,EAAA,CACCL,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,YACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,QAAA,CAAMF,UAAU,+CAA+CC,SAEhE,uBACAI,EAAAH,IAAC,SAAA,CACCF,UAAU,qBACVH,MAAOH,EACPa,SAAWC,GAAMb,EAAgBa,EAAEC,OAAOZ,OAC1Ca,SAAUrB,EAETY,SAAYL,EAAAe,KACXC,GAAAV,EAAAA,IAAC,SAA0B,CAAAL,MAAOe,EAAOf,MACtCI,SAAOW,EAAAd,OADGc,EAAOf,cAO1BQ,EAAAH,IAACW,EAAA,CACCC,QAAQ,UACRd,UAAU,SACVe,QAAS,IA9FPpG,OAAOqG,IACrB1B,GAAW,GACP,IACE,IAAApF,EAEJ,OAAQ8G,GACN,IAAK,MACM9G,QAAMgF,IACf,MACF,IAAK,gBACMhF,QAAMgF,IACf,MACF,IAAK,aACMhF,QAAMgF,IACf,MACF,IAAK,YACMhF,QAAMgF,IACf,MACF,IAAK,iBACMhF,QAAMgF,IACf,MACF,QACWhF,EAAA,mBAIbuF,GAAmBwB,GAAA,CACjB,CACE3F,GAAIJ,KAAKC,MACT+F,KAAMF,EACN9G,SACA6B,WAAW,IAAIb,MAAOmD,qBAErB4C,WAEE5G,GACPF,QAAQE,MAAM,4BAA4B2G,KAAa3G,GACvDoF,GAAmBwB,GAAA,CACjB,CACE3F,GAAIJ,KAAKC,MACT+F,KAAMF,EACN9G,OAAQ,UAAUG,EAAMC,UACxByB,WAAW,IAAIb,MAAOmD,iBACtBhE,OAAO,MAEN4G,IACJ,CACD,QACA3B,GAAW,EACb,GA6C2B6B,CAAQzB,GACvBgB,SAAUrB,EAETY,WAAU,gBAAkB,sBAG/BF,KAAC,MAAI,CAAAC,UAAU,wBACbC,SAAA,GAAAC,IAAC,KAAED,SAAuG,4GACzGC,EAAAA,IAAA,IAAA,CAAEF,UAAU,OAAOC,SAAmF,qGAO/GF,KAACI,EAAK,CAAAH,UAAU,gBACdC,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,gCAErCK,EACE,CAAAL,SAAmB,IAAnBT,EAAQ3D,aACN,MAAI,CAAAmE,UAAU,iCAAiCC,SAAA,wEAI/CC,EAAAA,IAAA,MAAA,CAAIF,UAAU,YACZC,SAAAT,EAAQmB,KACPS,UAAA,OAAAf,EAAAN,KAAC,MAAA,CAECC,UAAW,0BAAyBoB,EAAK/G,MAAQ,2BAA6B,gCAE9E4F,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,mCACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAAAC,EAAAA,IAAC,KAAG,CAAAF,UAAU,cACXC,UAAA,OAAAoB,IAAYC,MAAKC,GAAOA,EAAI1B,QAAUuB,EAAKF,iBAAOpB,QAASsB,EAAKF,OAElEhB,EAAAA,IAAA,IAAA,CAAEF,UAAU,wBAAyBC,WAAKlE,iBAE5CmE,IAAA,OAAA,CAAKF,UAAW,mCAAkCoB,EAAK/G,MAAQ,0BAA4B,+BACzF4F,SAAAmB,EAAK/G,MAAQ,QAAU,eAG5B6F,IAAC,MAAI,CAAAF,UAAU,OACbC,SAAAC,EAAAA,IAAC,OAAIF,UAAU,0DACZC,SAAKmB,EAAAlH,aAhBLkH,EAAK9F,GAAA,oBA2BvB,MAAI,CAAA0E,UAAU,6BACbC,eAAC,IAAA,CAAEA,yGAEP"}