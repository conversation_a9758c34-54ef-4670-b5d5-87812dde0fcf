{"version": 3, "file": "Tests-272f6637.js", "sources": ["../../src/pages/professional/Tests.jsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\n\nconst Tests = () => {\n  return (\n    <div className=\"container mx-auto py-6\">\n      <h1 className=\"text-2xl font-bold text-gray-800 mb-6\">Gestión de Tests</h1>\n      \n      <Card>\n        <CardHeader>\n          <h2 className=\"text-lg font-medium\">Asignación y Administración de Tests</h2>\n        </CardHeader>\n        <CardBody>\n          <p className=\"text-gray-600\">\n            Esta sección permitirá asignar y gestionar tests para estudiantes (componente en desarrollo).\n          </p>\n        </CardBody>\n      </Card>\n    </div>\n  );\n};\n\nexport default Tests;"], "names": ["Tests", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody"], "mappings": "wFAGA,MAAMA,EAAQ,MAEVC,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAgB,4BAErEE,EACC,CAAAF,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,0DAErCK,EACC,CAAAL,SAAAI,EAAAH,IAAC,KAAEF,UAAU,gBAAgBC"}