import{j as e,L as s}from"./index-165d7974.js";const t=()=>e.jsxs("div",{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Mis Tests"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Tests psicométricos asignados para evaluación de aptitudes."})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Evaluaciones Pendientes"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white shadow-sm overflow-hidden rounded-lg",children:[e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0 bg-violet-100 rounded-md p-3",children:e.jsx("i",{className:"fas fa-clipboard-list text-violet-600 text-xl"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900",children:"Batería Completa BAT-7"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Batería completa de evaluación de aptitudes"}),e.jsxs("dl",{className:"mt-4 grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Fecha límite:"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:"27/4/2025"})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Duración:"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:"120 minutos"})]})]}),e.jsx("div",{className:"mt-4 border-t border-gray-100 pt-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("i",{className:"fas fa-clock text-yellow-500 mr-2"}),e.jsx("p",{className:"text-sm font-medium text-yellow-500",children:"Tiempo restante: Hoy"})]})}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:"Incluye:"}),e.jsxs("ul",{className:"mt-2 space-y-1",children:[e.jsxs("li",{className:"flex items-center text-sm text-gray-500",children:[e.jsx("i",{className:"fas fa-check text-green-500 mr-2"})," Test Verbal"]}),e.jsxs("li",{className:"flex items-center text-sm text-gray-500",children:[e.jsx("i",{className:"fas fa-check text-green-500 mr-2"})," Test Espacial"]}),e.jsxs("li",{className:"flex items-center text-sm text-gray-500",children:[e.jsx("i",{className:"fas fa-check text-green-500 mr-2"})," Test de Atención"]}),e.jsx("li",{className:"flex items-center text-sm text-gray-500",children:e.jsx("span",{className:"text-gray-400 text-xs",children:"+ 4 más"})})]})]})]})]})}),e.jsx("div",{className:"bg-gray-50 px-6 py-4",children:e.jsx(s,{to:"/test/instructions/bat7",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Iniciar Test"})})]}),e.jsxs("div",{className:"bg-white shadow-sm overflow-hidden rounded-lg",children:[e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0 bg-blue-100 rounded-md p-3",children:e.jsx("i",{className:"fas fa-file-alt text-blue-600 text-xl"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900",children:"Test de Aptitud Verbal"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Nueva evaluación de aptitud verbal"}),e.jsxs("dl",{className:"mt-4 grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Fecha límite:"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:"5/5/2025"})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Duración:"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:"30 minutos"})]})]}),e.jsx("div",{className:"mt-4 border-t border-gray-100 pt-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("i",{className:"fas fa-clock text-yellow-500 mr-2"}),e.jsx("p",{className:"text-sm font-medium text-yellow-500",children:"Tiempo restante: 8 días"})]})})]})]})}),e.jsx("div",{className:"bg-gray-50 px-6 py-4",children:e.jsx(s,{to:"/test/instructions/verbal",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Iniciar Test"})})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Evaluaciones Completadas"}),e.jsx("div",{className:"bg-white shadow-sm overflow-hidden rounded-lg",children:e.jsx("ul",{className:"divide-y divide-gray-200",children:a.map(((t,a)=>e.jsx("li",{className:"p-4 hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`flex-shrink-0 rounded-md p-2 ${t.iconBg}`,children:e.jsx("i",{className:`fas fa-${t.icon} text-white`})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:t.name}),e.jsxs("p",{className:"text-xs text-gray-500",children:["Completado: ",t.completedDate]})]})]}),e.jsx("div",{children:e.jsx(s,{to:`/test/results/${t.id}`,className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Ver resultado"})})]})},a)))})})]})]}),a=[{id:"1",name:"Test de Aptitud Espacial",completedDate:"25/04/2025",icon:"cube",iconBg:"bg-green-500"},{id:"2",name:"Test de Razonamiento",completedDate:"20/04/2025",icon:"brain",iconBg:"bg-purple-500"},{id:"3",name:"Test de Ortografía",completedDate:"15/04/2025",icon:"spell-check",iconBg:"bg-blue-500"}];export{t as default};
//# sourceMappingURL=Tests-3f31ac98.js.map
