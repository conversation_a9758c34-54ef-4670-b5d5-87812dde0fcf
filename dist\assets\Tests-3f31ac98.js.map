{"version": 3, "file": "Tests-3f31ac98.js", "sources": ["../../src/pages/student/Tests.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Tests = () => {\n  return (\n    <div>\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Mis Tests</h2>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          Tests psicométricos asignados para evaluación de aptitudes.\n        </p>\n      </div>\n\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Evaluaciones Pendientes</h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Batería Completa BAT-7 */}\n          <div className=\"bg-white shadow-sm overflow-hidden rounded-lg\">\n            <div className=\"p-6\">\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0 bg-violet-100 rounded-md p-3\">\n                  <i className=\"fas fa-clipboard-list text-violet-600 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h4 className=\"text-lg font-medium text-gray-900\">Batería Completa BAT-7</h4>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    Batería completa de evaluación de aptitudes\n                  </p>\n                  \n                  <dl className=\"mt-4 grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2\">\n                    <div className=\"sm:col-span-1\">\n                      <dt className=\"text-sm font-medium text-gray-500\">Fecha límite:</dt>\n                      <dd className=\"mt-1 text-sm text-gray-900\">27/4/2025</dd>\n                    </div>\n                    <div className=\"sm:col-span-1\">\n                      <dt className=\"text-sm font-medium text-gray-500\">Duración:</dt>\n                      <dd className=\"mt-1 text-sm text-gray-900\">120 minutos</dd>\n                    </div>\n                  </dl>\n                  \n                  <div className=\"mt-4 border-t border-gray-100 pt-4\">\n                    <div className=\"flex items-center\">\n                      <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                      <p className=\"text-sm font-medium text-yellow-500\">\n                        Tiempo restante: Hoy\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-4\">\n                    <h5 className=\"text-sm font-medium text-gray-900\">Incluye:</h5>\n                    <ul className=\"mt-2 space-y-1\">\n                      <li className=\"flex items-center text-sm text-gray-500\">\n                        <i className=\"fas fa-check text-green-500 mr-2\"></i> Test Verbal\n                      </li>\n                      <li className=\"flex items-center text-sm text-gray-500\">\n                        <i className=\"fas fa-check text-green-500 mr-2\"></i> Test Espacial\n                      </li>\n                      <li className=\"flex items-center text-sm text-gray-500\">\n                        <i className=\"fas fa-check text-green-500 mr-2\"></i> Test de Atención\n                      </li>\n                      <li className=\"flex items-center text-sm text-gray-500\">\n                        <span className=\"text-gray-400 text-xs\">+ 4 más</span>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-gray-50 px-6 py-4\">\n              <Link\n                to=\"/test/instructions/bat7\"\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Iniciar Test\n              </Link>\n            </div>\n          </div>\n          \n          {/* Test de Aptitud Verbal */}\n          <div className=\"bg-white shadow-sm overflow-hidden rounded-lg\">\n            <div className=\"p-6\">\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0 bg-blue-100 rounded-md p-3\">\n                  <i className=\"fas fa-file-alt text-blue-600 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h4 className=\"text-lg font-medium text-gray-900\">Test de Aptitud Verbal</h4>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    Nueva evaluación de aptitud verbal\n                  </p>\n                  \n                  <dl className=\"mt-4 grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2\">\n                    <div className=\"sm:col-span-1\">\n                      <dt className=\"text-sm font-medium text-gray-500\">Fecha límite:</dt>\n                      <dd className=\"mt-1 text-sm text-gray-900\">5/5/2025</dd>\n                    </div>\n                    <div className=\"sm:col-span-1\">\n                      <dt className=\"text-sm font-medium text-gray-500\">Duración:</dt>\n                      <dd className=\"mt-1 text-sm text-gray-900\">30 minutos</dd>\n                    </div>\n                  </dl>\n                  \n                  <div className=\"mt-4 border-t border-gray-100 pt-4\">\n                    <div className=\"flex items-center\">\n                      <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                      <p className=\"text-sm font-medium text-yellow-500\">\n                        Tiempo restante: 8 días\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-gray-50 px-6 py-4\">\n              <Link\n                to=\"/test/instructions/verbal\"\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Iniciar Test\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Evaluaciones Completadas</h3>\n        \n        <div className=\"bg-white shadow-sm overflow-hidden rounded-lg\">\n          <ul className=\"divide-y divide-gray-200\">\n            {pastTests.map((test, index) => (\n              <li key={index} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <div className={`flex-shrink-0 rounded-md p-2 ${test.iconBg}`}>\n                      <i className={`fas fa-${test.icon} text-white`}></i>\n                    </div>\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">{test.name}</p>\n                      <p className=\"text-xs text-gray-500\">Completado: {test.completedDate}</p>\n                    </div>\n                  </div>\n                  <div>\n                    <Link\n                      to={`/test/results/${test.id}`}\n                      className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                    >\n                      Ver resultado\n                    </Link>\n                  </div>\n                </div>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Datos de ejemplo para tests completados\nconst pastTests = [\n  {\n    id: '1',\n    name: 'Test de Aptitud Espacial',\n    completedDate: '25/04/2025',\n    icon: 'cube',\n    iconBg: 'bg-green-500'\n  },\n  {\n    id: '2',\n    name: 'Test de Razonamiento',\n    completedDate: '20/04/2025',\n    icon: 'brain',\n    iconBg: 'bg-purple-500'\n  },\n  {\n    id: '3',\n    name: 'Test de Ortografía',\n    completedDate: '15/04/2025',\n    icon: 'spell-check',\n    iconBg: 'bg-blue-500'\n  }\n];\n\nexport default Tests;"], "names": ["Tests", "children", "jsxs", "className", "jsx", "jsxRuntimeExports", "Link", "to", "pastTests", "map", "test", "index", "iconBg", "icon", "name", "completedDate", "id"], "mappings": "+CAGA,MAAMA,EAAQ,WAET,MACC,CAAAC,SAAA,GAACC,KAAA,MAAA,CAAIC,UAAU,OACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,mCAAmCF,SAAS,cACzDG,EAAAA,IAAA,IAAA,CAAED,UAAU,6BAA6BF,SAE1C,qEAGFC,KAAC,MAAI,CAAAC,UAAU,OACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,yCAAyCF,SAAuB,8BAE9EC,KAAC,MAAI,CAAAC,UAAU,wCAEbF,SAAA,GAACC,KAAA,MAAA,CAAIC,UAAU,gDACbF,SAAA,CAAAG,EAAAA,IAAC,OAAID,UAAU,MACbF,SAACC,EAAAA,KAAA,MAAA,CAAIC,UAAU,mBACbF,SAAA,CAAAG,EAAAA,IAAC,OAAID,UAAU,6CACbF,eAAC,IAAE,CAAAE,UAAU,sDAEfD,KAAC,MAAI,CAAAC,UAAU,OACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCF,SAAsB,2BACvEG,EAAAA,IAAA,IAAA,CAAED,UAAU,6BAA6BF,SAE1C,kDAEAC,KAAC,KAAG,CAAAC,UAAU,uDACZF,SAAA,GAACC,KAAA,MAAA,CAAIC,UAAU,gBACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCF,SAAa,kBAC9DG,EAAAA,IAAA,KAAA,CAAGD,UAAU,6BAA6BF,SAAS,mBAEtDC,KAAC,MAAI,CAAAC,UAAU,gBACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCF,SAAS,cAC1DG,EAAAA,IAAA,KAAA,CAAGD,UAAU,6BAA6BF,SAAW,4BAIzD,MAAI,CAAAE,UAAU,qCACbF,SAACC,EAAAA,KAAA,MAAA,CAAIC,UAAU,oBACbF,SAAA,GAACG,IAAA,IAAA,CAAED,UAAU,sCACZC,EAAAA,IAAA,IAAA,CAAED,UAAU,sCAAsCF,SAEnD,gCAIJC,KAAC,MAAI,CAAAC,UAAU,OACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCF,SAAQ,eAC1DC,KAAC,KAAG,CAAAC,UAAU,iBACZF,SAAA,GAACC,KAAA,KAAA,CAAGC,UAAU,0CACZF,SAAA,GAACG,IAAA,IAAA,CAAED,UAAU,qCAAuC,oBAEtDD,KAAC,KAAG,CAAAC,UAAU,0CACZF,SAAA,GAACG,IAAA,IAAA,CAAED,UAAU,qCAAuC,sBAEtDD,KAAC,KAAG,CAAAC,UAAU,0CACZF,SAAA,GAACG,IAAA,IAAA,CAAED,UAAU,qCAAuC,yBAEtDC,IAAC,MAAGD,UAAU,0CACZF,eAAC,OAAK,CAAAE,UAAU,wBAAwBF,SAAA,8BAOpDG,IAAC,MAAI,CAAAD,UAAU,uBACbF,SAAAI,EAAAD,IAACE,EAAA,CACCC,GAAG,0BACHJ,UAAU,2NACXF,SAAA,wBAOLC,KAAC,MAAI,CAAAC,UAAU,gDACbF,SAAA,CAAAG,EAAAA,IAAC,OAAID,UAAU,MACbF,SAACC,EAAAA,KAAA,MAAA,CAAIC,UAAU,mBACbF,SAAA,CAAAG,EAAAA,IAAC,OAAID,UAAU,2CACbF,eAAC,IAAE,CAAAE,UAAU,8CAEfD,KAAC,MAAI,CAAAC,UAAU,OACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCF,SAAsB,2BACvEG,EAAAA,IAAA,IAAA,CAAED,UAAU,6BAA6BF,SAE1C,yCAEAC,KAAC,KAAG,CAAAC,UAAU,uDACZF,SAAA,GAACC,KAAA,MAAA,CAAIC,UAAU,gBACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCF,SAAa,kBAC9DG,EAAAA,IAAA,KAAA,CAAGD,UAAU,6BAA6BF,SAAQ,kBAErDC,KAAC,MAAI,CAAAC,UAAU,gBACbF,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,oCAAoCF,SAAS,cAC1DG,EAAAA,IAAA,KAAA,CAAGD,UAAU,6BAA6BF,SAAU,2BAIxD,MAAI,CAAAE,UAAU,qCACbF,SAACC,EAAAA,KAAA,MAAA,CAAIC,UAAU,oBACbF,SAAA,GAACG,IAAA,IAAA,CAAED,UAAU,sCACZC,EAAAA,IAAA,IAAA,CAAED,UAAU,sCAAsCF,SAEnD,2CAMVG,IAAC,MAAI,CAAAD,UAAU,uBACbF,SAAAI,EAAAD,IAACE,EAAA,CACCC,GAAG,4BACHJ,UAAU,2NACXF,SAAA,mCAQR,MACC,CAAAA,SAAA,CAACG,EAAAA,IAAA,KAAA,CAAGD,UAAU,yCAAyCF,SAAwB,6BAE/EG,EAAAA,IAAC,OAAID,UAAU,gDACbF,eAAC,KAAG,CAAAE,UAAU,2BACXF,SAAUO,EAAAC,KAAI,CAACC,EAAMC,UACnB,KAAe,CAAAR,UAAU,uBACxBF,SAACC,EAAAA,KAAA,MAAA,CAAIC,UAAU,oCACbF,SAAA,GAACC,KAAA,MAAA,CAAIC,UAAU,oBACbF,SAAA,CAAAG,MAAC,MAAI,CAAAD,UAAW,gCAAgCO,EAAKE,SACnDX,WAACG,IAAA,IAAA,CAAED,UAAW,UAAUO,EAAKG,wBAE/BX,KAAC,MAAI,CAAAC,UAAU,OACbF,SAAA,CAAAG,EAAAA,IAAC,IAAE,CAAAD,UAAU,oCAAqCF,SAAAS,EAAKI,SACvDZ,KAAC,IAAE,CAAAC,UAAU,wBAAwBF,SAAA,CAAA,eAAaS,EAAKK,6BAG1D,MACC,CAAAd,SAAAI,EAAAD,IAACE,EAAA,CACCC,GAAI,iBAAiBG,EAAKM,KAC1Bb,UAAU,kNACXF,SAAA,wBAfEU,eA8BjBH,EAAY,CAChB,CACEQ,GAAI,IACJF,KAAM,2BACNC,cAAe,aACfF,KAAM,OACND,OAAQ,gBAEV,CACEI,GAAI,IACJF,KAAM,uBACNC,cAAe,aACfF,KAAM,QACND,OAAQ,iBAEV,CACEI,GAAI,IACJF,KAAM,qBACNC,cAAe,aACfF,KAAM,cACND,OAAQ"}