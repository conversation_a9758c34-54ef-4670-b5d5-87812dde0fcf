{"version": 3, "file": "Users-77d38882.js", "sources": ["../../src/pages/admin/Users.jsx"], "sourcesContent": ["import React from 'react';\nimport { Card, CardHeader, CardBody } from '../../components/ui/Card';\n\nconst Users = () => {\n  return (\n    <div className=\"container mx-auto py-6\">\n      <h1 className=\"text-2xl font-bold text-gray-800 mb-6\">Administración de Usuarios</h1>\n      \n      <Card>\n        <CardHeader>\n          <h2 className=\"text-lg font-medium\">Lista de Usuarios</h2>\n        </CardHeader>\n        <CardBody>\n          <p className=\"text-gray-600\">\n            Esta sección permitirá gestionar los usuarios del sistema (componente en desarrollo).\n          </p>\n        </CardBody>\n      </Card>\n    </div>\n  );\n};\n\nexport default Users;"], "names": ["Users", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxRuntimeExports", "CardBody"], "mappings": "wFAGA,MAAMA,EAAQ,MAEVC,KAAC,MAAI,CAAAC,UAAU,yBACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAA0B,sCAE/EE,EACC,CAAAF,SAAA,CAAAC,EAAAA,IAACE,GACCH,SAACI,EAAAH,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,uCAErCK,EACC,CAAAL,SAAAI,EAAAH,IAAC,KAAEF,UAAU,gBAAgBC"}