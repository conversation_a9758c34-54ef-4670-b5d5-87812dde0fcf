{"version": 3, "file": "Verbal-3c165ad9.js", "sources": ["../../src/pages/test/Verbal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Card, CardHeader, CardBody, CardFooter } from '../../components/ui/Card';\nimport { Button } from '../../components/ui/Button';\nimport { useToast } from '../../hooks/useToast';\n\nconst Verbal = () => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(30 * 60); // 30 minutos en segundos\n  const [testStarted, setTestStarted] = useState(false);\n  const { showInfo, showSuccess, showWarning, showError } = useToast();\n\n  useEffect(() => {\n    // Carga de preguntas\n    const fetchQuestions = async () => {\n      try {\n        // Aquí se implementaría la llamada a la API para obtener las preguntas\n        // Por ahora usamos datos de ejemplo\n        await new Promise(resolve => setTimeout(resolve, 800)); // Simular tiempo de carga\n        \n        const verbalQuestions = [\n          // SINÓNIMOS\n          {\n            id: 1,\n            type: 'synonyms',\n            text: 'Selecciona el sinónimo de \"Efímero\":',\n            options: [\n              { id: 'a', text: 'Duradero' },\n              { id: 'b', text: 'Eterno' },\n              { id: 'c', text: 'Pasajero' },\n              { id: 'd', text: 'Constante' },\n            ],\n            correctAnswer: 'c'\n          },\n          {\n            id: 2,\n            type: 'synonyms',\n            text: 'Selecciona el sinónimo de \"Incipiente\":',\n            options: [\n              { id: 'a', text: 'Inicial' },\n              { id: 'b', text: 'Culminante' },\n              { id: 'c', text: 'Permanente' },\n              { id: 'd', text: 'Consolidado' },\n            ],\n            correctAnswer: 'a'\n          },\n          \n          // ANTÓNIMOS\n          {\n            id: 3,\n            type: 'antonyms',\n            text: 'Selecciona el antónimo de \"Frugal\":',\n            options: [\n              { id: 'a', text: 'Austero' },\n              { id: 'b', text: 'Derrochador' },\n              { id: 'c', text: 'Moderado' },\n              { id: 'd', text: 'Económico' },\n            ],\n            correctAnswer: 'b'\n          },\n          {\n            id: 4,\n            type: 'antonyms',\n            text: 'Selecciona el antónimo de \"Parsimonia\":',\n            options: [\n              { id: 'a', text: 'Lentitud' },\n              { id: 'b', text: 'Calma' },\n              { id: 'c', text: 'Precipitación' },\n              { id: 'd', text: 'Paciencia' },\n            ],\n            correctAnswer: 'c'\n          },\n          \n          // ANALOGÍAS\n          {\n            id: 5,\n            type: 'analogies',\n            text: 'Libro es a Lector como Película es a:',\n            options: [\n              { id: 'a', text: 'Actor' },\n              { id: 'b', text: 'Director' },\n              { id: 'c', text: 'Espectador' },\n              { id: 'd', text: 'Guionista' },\n            ],\n            correctAnswer: 'c'\n          },\n          {\n            id: 6,\n            type: 'analogies',\n            text: 'Pincel es a Pintura como Pluma es a:',\n            options: [\n              { id: 'a', text: 'Tinta' },\n              { id: 'b', text: 'Libro' },\n              { id: 'c', text: 'Escritura' },\n              { id: 'd', text: 'Papel' },\n            ],\n            correctAnswer: 'c'\n          },\n          \n          // COMPRENSIÓN LECTORA\n          {\n            id: 7,\n            type: 'reading',\n            text: `La inteligencia artificial (IA) ha experimentado un avance significativo en los últimos años, impulsada por el desarrollo de algoritmos más sofisticados y el aumento en la capacidad de procesamiento de datos. Aunque ofrece numerosos beneficios en campos como la medicina, la educación y la industria, también plantea desafíos éticos y sociales que deben abordarse. Entre estos desafíos se encuentran la privacidad de los datos, la transparencia algorítmica y la posible automatización de empleos tradicionalmente humanos.\n\nDiferentes países han comenzado a desarrollar marcos regulatorios para abordar estos desafíos, aunque existe un debate sobre el equilibrio adecuado entre la innovación y la protección. Los expertos coinciden en que un enfoque colaborativo entre gobiernos, empresas y academia es esencial para maximizar los beneficios de la IA mientras se mitigan sus riesgos potenciales.\n\n¿Según la lectura, qué ha impulsado el avance de la IA?`,\n            options: [\n              { id: 'a', text: 'El aumento de preocupaciones éticas' },\n              { id: 'b', text: 'Algoritmos más sofisticados y mayor capacidad de procesamiento' },\n              { id: 'c', text: 'Su aplicación en la medicina' },\n              { id: 'd', text: 'La demanda en el sector educativo' },\n            ],\n            correctAnswer: 'b'\n          },\n          {\n            id: 8,\n            type: 'reading',\n            text: `La inteligencia artificial (IA) ha experimentado un avance significativo en los últimos años, impulsada por el desarrollo de algoritmos más sofisticados y el aumento en la capacidad de procesamiento de datos. Aunque ofrece numerosos beneficios en campos como la medicina, la educación y la industria, también plantea desafíos éticos y sociales que deben abordarse. Entre estos desafíos se encuentran la privacidad de los datos, la transparencia algorítmica y la posible automatización de empleos tradicionalmente humanos.\n\nDiferentes países han comenzado a desarrollar marcos regulatorios para abordar estos desafíos, aunque existe un debate sobre el equilibrio adecuado entre la innovación y la protección. Los expertos coinciden en que un enfoque colaborativo entre gobiernos, empresas y academia es esencial para maximizar los beneficios de la IA mientras se mitigan sus riesgos potenciales.\n\n¿Cuál es uno de los desafíos éticos mencionados en el texto?`,\n            options: [\n              { id: 'a', text: 'El costo de los algoritmos' },\n              { id: 'b', text: 'La falta de investigadores cualificados' },\n              { id: 'c', text: 'La privacidad de los datos' },\n              { id: 'd', text: 'La resistencia del público a adoptar nuevas tecnologías' },\n            ],\n            correctAnswer: 'c'\n          },\n          \n          // COMPLETAR ORACIONES\n          {\n            id: 9,\n            type: 'completion',\n            text: 'Complete la oración: \"La ___________ es fundamental para el desarrollo de habilidades comunicativas efectivas.\"',\n            options: [\n              { id: 'a', text: 'empatía' },\n              { id: 'b', text: 'inteligencia' },\n              { id: 'c', text: 'velocidad' },\n              { id: 'd', text: 'tecnología' },\n            ],\n            correctAnswer: 'a'\n          },\n          {\n            id: 10,\n            type: 'completion',\n            text: 'Complete la oración: \"En el ámbito científico, la ___________ de los resultados es tan importante como la originalidad de la investigación.\"',\n            options: [\n              { id: 'a', text: 'popularidad' },\n              { id: 'b', text: 'reproducibilidad' },\n              { id: 'c', text: 'antigüedad' },\n              { id: 'd', text: 'comercialización' },\n            ],\n            correctAnswer: 'b'\n          },\n          \n          // DEFINICIONES\n          {\n            id: 11,\n            type: 'definitions',\n            text: '¿Qué significa el término \"Epítome\"?',\n            options: [\n              { id: 'a', text: 'Suma o compendio de una obra extensa' },\n              { id: 'b', text: 'Extensión innecesaria de un texto' },\n              { id: 'c', text: 'Crítica negativa a un trabajo' },\n              { id: 'd', text: 'Ilustración al inicio de un capítulo' },\n            ],\n            correctAnswer: 'a'\n          },\n          {\n            id: 12,\n            type: 'definitions',\n            text: '¿Qué significa la palabra \"Dilema\"?',\n            options: [\n              { id: 'a', text: 'Solución sencilla a un problema complejo' },\n              { id: 'b', text: 'Situación en la que hay que elegir entre dos opciones igualmente difíciles' },\n              { id: 'c', text: 'Explicación detallada de un concepto' },\n              { id: 'd', text: 'Error de razonamiento en un argumento' },\n            ],\n            correctAnswer: 'b'\n          },\n          \n          // REFRANES Y EXPRESIONES\n          {\n            id: 13,\n            type: 'expressions',\n            text: '¿Cuál es el significado de la expresión \"Dar gato por liebre\"?',\n            options: [\n              { id: 'a', text: 'Ofrecer más de lo que se puede entregar' },\n              { id: 'b', text: 'Engañar haciendo pasar una cosa por otra de mayor valor' },\n              { id: 'c', text: 'Revelar un secreto inesperadamente' },\n              { id: 'd', text: 'Recibir menos de lo esperado' },\n            ],\n            correctAnswer: 'b'\n          },\n          {\n            id: 14,\n            type: 'expressions',\n            text: '¿Qué significa la expresión \"Tirar la casa por la ventana\"?',\n            options: [\n              { id: 'a', text: 'Deshacerse de bienes innecesarios' },\n              { id: 'b', text: 'Mudarse precipitadamente' },\n              { id: 'c', text: 'Gastar excesivamente en una celebración' },\n              { id: 'd', text: 'Renovar completamente un hogar' },\n            ],\n            correctAnswer: 'c'\n          },\n          \n          // CLASIFICACIONES DE PALABRAS\n          {\n            id: 15,\n            type: 'classification',\n            text: '¿Cuál de las siguientes palabras es un adverbio?',\n            options: [\n              { id: 'a', text: 'Felicidad' },\n              { id: 'b', text: 'Correr' },\n              { id: 'c', text: 'Rápidamente' },\n              { id: 'd', text: 'Bonito' },\n            ],\n            correctAnswer: 'c'\n          },\n          {\n            id: 16,\n            type: 'classification',\n            text: '¿Cuál de las siguientes palabras es un sustantivo abstracto?',\n            options: [\n              { id: 'a', text: 'Mesa' },\n              { id: 'b', text: 'Libertad' },\n              { id: 'c', text: 'Árbol' },\n              { id: 'd', text: 'Coche' },\n            ],\n            correctAnswer: 'b'\n          },\n          \n          // RELACIONES DE SIGNIFICADO\n          {\n            id: 17,\n            type: 'relations',\n            text: '¿Cuál es la relación entre \"Flora\" y \"Fauna\"?',\n            options: [\n              { id: 'a', text: 'Causa - efecto' },\n              { id: 'b', text: 'Parte - todo' },\n              { id: 'c', text: 'Términos complementarios (plantas - animales)' },\n              { id: 'd', text: 'Sinónimos' },\n            ],\n            correctAnswer: 'c'\n          },\n          {\n            id: 18,\n            type: 'relations',\n            text: '¿Qué relación existe entre \"Endémico\" y \"Local\"?',\n            options: [\n              { id: 'a', text: 'Contradicción' },\n              { id: 'b', text: 'Términos similares o relacionados' },\n              { id: 'c', text: 'Causa - consecuencia' },\n              { id: 'd', text: 'Complementariedad' },\n            ],\n            correctAnswer: 'b'\n          },\n          \n          // COMPRENSIÓN DE PÁRRAFOS COMPLEJOS\n          {\n            id: 19,\n            type: 'complex_reading',\n            text: `El concepto de \"desarrollo sostenible\" surgió como respuesta a la creciente preocupación sobre las consecuencias ambientales del desarrollo económico global. Acuñado formalmente en el Informe Brundtland de 1987, titulado \"Nuestro Futuro Común\", este paradigma propone un modelo de crecimiento que satisface las necesidades del presente sin comprometer la capacidad de las generaciones futuras para satisfacer sus propias necesidades. Esta definición enfatiza la interconexión de tres pilares fundamentales: el desarrollo económico, la equidad social y la protección ambiental.\n\nNo obstante, la implementación práctica del desarrollo sostenible ha enfrentado numerosos desafíos. La tensión inherente entre crecimiento económico y conservación ambiental ha revelado la complejidad de lograr un equilibrio genuino entre intereses aparentemente contradictorios. Asimismo, las asimetrías de poder en la gobernanza global han dificultado el establecimiento de compromisos vinculantes que trasciendan las fronteras nacionales.\n\n¿Cuál es la idea principal del texto?`,\n            options: [\n              { id: 'a', text: 'La imposibilidad de implementar el desarrollo sostenible' },\n              { id: 'b', text: 'El origen, definición y desafíos del concepto de desarrollo sostenible' },\n              { id: 'c', text: 'La crítica al Informe Brundtland de 1987' },\n              { id: 'd', text: 'La importancia exclusiva del pilar ambiental en el desarrollo' },\n            ],\n            correctAnswer: 'b'\n          },\n          {\n            id: 20,\n            type: 'complex_reading',\n            text: `El concepto de \"desarrollo sostenible\" surgió como respuesta a la creciente preocupación sobre las consecuencias ambientales del desarrollo económico global. Acuñado formalmente en el Informe Brundtland de 1987, titulado \"Nuestro Futuro Común\", este paradigma propone un modelo de crecimiento que satisface las necesidades del presente sin comprometer la capacidad de las generaciones futuras para satisfacer sus propias necesidades. Esta definición enfatiza la interconexión de tres pilares fundamentales: el desarrollo económico, la equidad social y la protección ambiental.\n\nNo obstante, la implementación práctica del desarrollo sostenible ha enfrentado numerosos desafíos. La tensión inherente entre crecimiento económico y conservación ambiental ha revelado la complejidad de lograr un equilibrio genuino entre intereses aparentemente contradictorios. Asimismo, las asimetrías de poder en la gobernanza global han dificultado el establecimiento de compromisos vinculantes que trasciendan las fronteras nacionales.\n\nSegún el texto, ¿cuáles son los tres pilares fundamentales del desarrollo sostenible?`,\n            options: [\n              { id: 'a', text: 'Conservación, restauración y mitigación' },\n              { id: 'b', text: 'Global, nacional y local' },\n              { id: 'c', text: 'Presente, futuro y pasado' },\n              { id: 'd', text: 'Desarrollo económico, equidad social y protección ambiental' },\n            ],\n            correctAnswer: 'd'\n          },\n        ];\n        \n        setQuestions(verbalQuestions);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error al cargar preguntas:', error);\n        showError('Error al cargar las preguntas del test');\n        setLoading(false);\n      }\n    };\n\n    fetchQuestions();\n  }, []);\n\n  // Temporizador\n  useEffect(() => {\n    if (!testStarted || timeLeft <= 0) return;\n\n    const timer = setInterval(() => {\n      setTimeLeft(prevTime => {\n        if (prevTime <= 1) {\n          clearInterval(timer);\n          handleFinishTest();\n          return 0;\n        }\n        return prevTime - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [testStarted, timeLeft]);\n\n  const handleStartTest = () => {\n    setTestStarted(true);\n    showInfo('Test iniciado. ¡Buena suerte!');\n  };\n\n  const handleSelectOption = (questionId, optionId) => {\n    setAnswers({\n      ...answers,\n      [questionId]: optionId\n    });\n  };\n\n  const handleNextQuestion = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const handlePreviousQuestion = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  const handleFinishTest = () => {\n    // En un caso real, aquí se enviarían las respuestas al servidor\n    // Por ahora, simulamos finalización exitosa\n    \n    const answeredQuestions = Object.keys(answers).length;\n    const totalQuestions = questions.length;\n    \n    showSuccess(`Test completado. Has respondido ${answeredQuestions} de ${totalQuestions} preguntas.`);\n    \n    // Simulación de ID de aplicación para resultados\n    const applicationId = '12345';\n    \n    // Redirigir a la página de resultados\n    navigate(`/test/results/${applicationId}`);\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getQuestionTypeLabel = (type) => {\n    switch (type) {\n      case 'synonyms': return 'Sinónimos';\n      case 'antonyms': return 'Antónimos';\n      case 'analogies': return 'Analogías';\n      case 'reading': return 'Comprensión Lectora';\n      case 'complex_reading': return 'Comprensión de Textos Complejos';\n      case 'completion': return 'Completar Oración';\n      case 'definitions': return 'Definiciones';\n      case 'expressions': return 'Refranes y Expresiones';\n      case 'classification': return 'Clasificación de Palabras';\n      case 'relations': return 'Relaciones de Significado';\n      default: return type;\n    }\n  };\n\n  const currentQuestionData = questions[currentQuestion];\n  const isAnswered = currentQuestionData ? answers[currentQuestionData.id] : false;\n\n  return (\n    <div className=\"container mx-auto py-6 max-w-4xl\">\n      <div className=\"mb-6 flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">Test de Aptitud Verbal</h1>\n          <p className=\"text-gray-600\">Evaluación de comprensión, razonamiento y habilidades lingüísticas</p>\n        </div>\n        {testStarted && (\n          <div className={`text-xl font-mono ${timeLeft < 60 ? 'text-red-600 animate-pulse' : timeLeft < 300 ? 'text-yellow-600' : 'text-gray-700'}`}>\n            {formatTime(timeLeft)}\n          </div>\n        )}\n      </div>\n\n      {loading ? (\n        <div className=\"py-16 text-center\">\n          <div className=\"flex flex-col items-center justify-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4\"></div>\n            <p className=\"text-gray-500\">Cargando test de aptitud verbal...</p>\n          </div>\n        </div>\n      ) : !testStarted ? (\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-xl font-semibold text-gray-800\">Aptitud Verbal: Instrucciones</h2>\n          </CardHeader>\n          <CardBody>\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-700 mb-2\">¿Qué es la Aptitud Verbal?</h3>\n                <p className=\"text-gray-600 mb-2\">\n                  La aptitud verbal es la capacidad para comprender conceptos expresados a través de palabras. Implica el dominio del lenguaje y la habilidad para entender relaciones entre conceptos verbales, procesando y analizando información expresada mediante el lenguaje.\n                </p>\n                <p className=\"text-gray-600\">\n                  Esta aptitud es fundamental en el ámbito académico y profesional, siendo especialmente relevante para carreras relacionadas con comunicación, derecho, educación, psicología y cualquier disciplina que requiera una buena comprensión lectora y expresión escrita.\n                </p>\n              </div>\n              \n              <div>\n                <h3 className=\"text-lg font-medium text-gray-700 mb-2\">Componentes de la Evaluación</h3>\n                <p className=\"text-gray-600 mb-3\">\n                  Este test evalúa diversas dimensiones de tu aptitud verbal a través de diferentes tipos de preguntas:\n                </p>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-medium text-blue-600 mb-2\">Comprensión Verbal</h4>\n                    <ul className=\"list-disc pl-5 space-y-1 text-gray-600\">\n                      <li>Sinónimos y antónimos</li>\n                      <li>Definiciones de palabras</li>\n                      <li>Analogías verbales</li>\n                    </ul>\n                  </div>\n                  \n                  <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-medium text-blue-600 mb-2\">Razonamiento Verbal</h4>\n                    <ul className=\"list-disc pl-5 space-y-1 text-gray-600\">\n                      <li>Clasificación de palabras</li>\n                      <li>Relaciones de significado</li>\n                      <li>Refranes y expresiones</li>\n                    </ul>\n                  </div>\n                  \n                  <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-medium text-blue-600 mb-2\">Comprensión Lectora</h4>\n                    <ul className=\"list-disc pl-5 space-y-1 text-gray-600\">\n                      <li>Comprensión de textos simples</li>\n                      <li>Comprensión de textos complejos</li>\n                      <li>Análisis de argumentos</li>\n                    </ul>\n                  </div>\n                  \n                  <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-medium text-blue-600 mb-2\">Uso del Lenguaje</h4>\n                    <ul className=\"list-disc pl-5 space-y-1 text-gray-600\">\n                      <li>Completar oraciones</li>\n                      <li>Identificación de errores</li>\n                      <li>Coherencia textual</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n              \n              <div>\n                <h3 className=\"text-lg font-medium text-gray-700 mb-2\">Instrucciones del Test</h3>\n                <ul className=\"list-disc pl-5 space-y-2 text-gray-600\">\n                  <li>El test consta de 20 preguntas que cubren diferentes aspectos de la aptitud verbal.</li>\n                  <li>Dispondrás de <span className=\"font-medium\">30 minutos</span> para completar todas las preguntas.</li>\n                  <li>Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible.</li>\n                  <li>Al finalizar el tiempo o al presionar \"Finalizar Test\", se enviará automáticamente y no podrás realizar más cambios.</li>\n                  <li>Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas.</li>\n                </ul>\n              </div>\n              \n              <div className=\"bg-yellow-50 border-l-4 border-yellow-500 p-4\">\n                <h3 className=\"text-lg font-medium text-yellow-800 mb-1\">Importante</h3>\n                <p className=\"text-yellow-700\">\n                  Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet.\n                </p>\n              </div>\n            </div>\n          </CardBody>\n          <CardFooter className=\"flex justify-end\">\n            <Button\n              variant=\"primary\"\n              onClick={handleStartTest}\n              className=\"px-6 py-2\"\n            >\n              Comenzar Test\n            </Button>\n          </CardFooter>\n        </Card>\n      ) : (\n        <>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n            <div className=\"md:col-span-3\">\n              <Card className=\"mb-6\">\n                <CardHeader className=\"flex justify-between items-center\">\n                  <div>\n                    <h2 className=\"text-lg font-medium\">\n                      Pregunta {currentQuestion + 1} de {questions.length}\n                    </h2>\n                    <p className=\"text-sm text-gray-500\">\n                      {currentQuestionData ? getQuestionTypeLabel(currentQuestionData.type) : ''}\n                    </p>\n                  </div>\n                  <div className=\"text-sm text-gray-500\">\n                    {isAnswered ? 'Respondida' : 'Sin responder'}\n                  </div>\n                </CardHeader>\n                <CardBody>\n                  {currentQuestionData && (\n                    <>\n                      <div className=\"text-gray-800 mb-6 whitespace-pre-line\">{currentQuestionData.text}</div>\n                      <div className=\"space-y-3\">\n                        {currentQuestionData.options.map((option) => (\n                          <div \n                            key={option.id}\n                            className={`border rounded-lg p-3 cursor-pointer transition-colors ${\n                              answers[currentQuestionData.id] === option.id\n                                ? 'bg-blue-50 border-blue-500'\n                                : 'hover:bg-gray-50'\n                            }`}\n                            onClick={() => handleSelectOption(currentQuestionData.id, option.id)}\n                          >\n                            <div className=\"flex items-center\">\n                              <div className={`w-6 h-6 flex items-center justify-center rounded-full mr-3 ${\n                                answers[currentQuestionData.id] === option.id\n                                  ? 'bg-blue-500 text-white'\n                                  : 'bg-gray-200 text-gray-700'\n                              }`}>\n                                {option.id.toUpperCase()}\n                              </div>\n                              <div className=\"text-gray-700\">{option.text}</div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </>\n                  )}\n                </CardBody>\n                <CardFooter className=\"flex justify-between\">\n                  <Button\n                    variant=\"outline\"\n                    onClick={handlePreviousQuestion}\n                    disabled={currentQuestion === 0}\n                  >\n                    Anterior\n                  </Button>\n                  {currentQuestion < questions.length - 1 ? (\n                    <Button\n                      variant=\"primary\"\n                      onClick={handleNextQuestion}\n                    >\n                      Siguiente\n                    </Button>\n                  ) : (\n                    <Button\n                      variant=\"primary\"\n                      onClick={handleFinishTest}\n                    >\n                      Finalizar Test\n                    </Button>\n                  )}\n                </CardFooter>\n              </Card>\n            </div>\n            \n            <div>\n              <Card className=\"sticky top-6\">\n                <CardHeader>\n                  <h2 className=\"text-md font-medium\">Navegación</h2>\n                </CardHeader>\n                <CardBody>\n                  <div className=\"grid grid-cols-5 gap-2\">\n                    {questions.map((question, index) => (\n                      <button\n                        key={question.id}\n                        className={`w-8 h-8 rounded-full font-medium text-sm ${\n                          currentQuestion === index\n                            ? 'bg-blue-500 text-white'\n                            : answers[question.id]\n                              ? 'bg-green-100 text-green-800 border border-green-300'\n                              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'\n                        }`}\n                        onClick={() => setCurrentQuestion(index)}\n                        title={getQuestionTypeLabel(question.type)}\n                      >\n                        {index + 1}\n                      </button>\n                    ))}\n                  </div>\n                  \n                  <div className=\"mt-6\">\n                    <div className=\"flex items-center justify-between mb-2 text-sm\">\n                      <span>Progreso</span>\n                      <span>{Object.keys(answers).length} de {questions.length}</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-green-500 h-2 rounded-full\"\n                        style={{ width: `${(Object.keys(answers).length / questions.length) * 100}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-6\">\n                    <div className=\"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4\">\n                      <h3 className=\"text-sm font-medium text-blue-700 mb-1\">Tipos de preguntas</h3>\n                      <div className=\"space-y-1\">\n                        {Array.from(new Set(questions.map(q => q.type))).map(type => (\n                          <div key={type} className=\"flex items-center text-xs\">\n                            <span className=\"w-3 h-3 rounded-full bg-blue-200 mr-2\"></span>\n                            <span className=\"text-gray-600\">{getQuestionTypeLabel(type)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <Button\n                    variant=\"primary\"\n                    className=\"w-full mt-2\"\n                    onClick={handleFinishTest}\n                  >\n                    Finalizar Test\n                  </Button>\n                </CardBody>\n              </Card>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default Verbal;"], "names": ["Verbal", "navigate", "useNavigate", "loading", "setLoading", "useState", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "testStarted", "setTestStarted", "showInfo", "showSuccess", "showWarning", "showError", "useToast", "useEffect", "async", "Promise", "resolve", "setTimeout", "id", "type", "text", "options", "<PERSON><PERSON><PERSON><PERSON>", "error", "console", "timer", "setInterval", "prevTime", "clearInterval", "handleFinishTest", "answeredQuestions", "Object", "keys", "length", "totalQuestions", "getQuestionTypeLabel", "currentQuestionData", "isAnswered", "jsxs", "className", "children", "jsx", "jsxRuntimeExports", "seconds", "secs", "Math", "floor", "toString", "padStart", "formatTime", "Fragment", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardBody", "map", "option", "onClick", "handleSelectOption", "questionId", "optionId", "toUpperCase", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "variant", "disabled", "question", "index", "title", "style", "width", "from", "Set", "q"], "mappings": "sMAMA,MAAMA,EAAS,KACb,MAAMC,EAAWC,KACVC,EAASC,GAAcC,YAAS,IAChCC,EAAWC,GAAgBF,EAAAA,SAAS,KACpCG,EAAiBC,GAAsBJ,WAAS,IAChDK,EAASC,GAAcN,EAAAA,SAAS,CAAE,IAClCO,EAAUC,GAAeR,EAAAA,SAAS,OAClCS,EAAaC,GAAkBV,YAAS,IACzCW,SAAEA,EAAUC,YAAAA,EAAAC,YAAaA,EAAaC,UAAAA,GAAcC,IAE1DC,EAAAA,WAAU,KAEeC,WACjB,UAGI,IAAIC,SAAQC,GAAWC,WAAWD,EAAS,OAwRjDjB,EAtRwB,CAEtB,CACEmB,GAAI,EACJC,KAAM,WACNC,KAAM,uCACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,YACjB,CAAEF,GAAI,IAAKE,KAAM,UACjB,CAAEF,GAAI,IAAKE,KAAM,YACjB,CAAEF,GAAI,IAAKE,KAAM,cAEnBE,cAAe,KAEjB,CACEJ,GAAI,EACJC,KAAM,WACNC,KAAM,0CACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,WACjB,CAAEF,GAAI,IAAKE,KAAM,cACjB,CAAEF,GAAI,IAAKE,KAAM,cACjB,CAAEF,GAAI,IAAKE,KAAM,gBAEnBE,cAAe,KAIjB,CACEJ,GAAI,EACJC,KAAM,WACNC,KAAM,sCACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,WACjB,CAAEF,GAAI,IAAKE,KAAM,eACjB,CAAEF,GAAI,IAAKE,KAAM,YACjB,CAAEF,GAAI,IAAKE,KAAM,cAEnBE,cAAe,KAEjB,CACEJ,GAAI,EACJC,KAAM,WACNC,KAAM,0CACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,YACjB,CAAEF,GAAI,IAAKE,KAAM,SACjB,CAAEF,GAAI,IAAKE,KAAM,iBACjB,CAAEF,GAAI,IAAKE,KAAM,cAEnBE,cAAe,KAIjB,CACEJ,GAAI,EACJC,KAAM,YACNC,KAAM,wCACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,SACjB,CAAEF,GAAI,IAAKE,KAAM,YACjB,CAAEF,GAAI,IAAKE,KAAM,cACjB,CAAEF,GAAI,IAAKE,KAAM,cAEnBE,cAAe,KAEjB,CACEJ,GAAI,EACJC,KAAM,YACNC,KAAM,uCACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,SACjB,CAAEF,GAAI,IAAKE,KAAM,SACjB,CAAEF,GAAI,IAAKE,KAAM,aACjB,CAAEF,GAAI,IAAKE,KAAM,UAEnBE,cAAe,KAIjB,CACEJ,GAAI,EACJC,KAAM,UACNC,KAAM,87BAKNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,uCACjB,CAAEF,GAAI,IAAKE,KAAM,kEACjB,CAAEF,GAAI,IAAKE,KAAM,gCACjB,CAAEF,GAAI,IAAKE,KAAM,sCAEnBE,cAAe,KAEjB,CACEJ,GAAI,EACJC,KAAM,UACNC,KAAM,m8BAKNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,8BACjB,CAAEF,GAAI,IAAKE,KAAM,2CACjB,CAAEF,GAAI,IAAKE,KAAM,8BACjB,CAAEF,GAAI,IAAKE,KAAM,4DAEnBE,cAAe,KAIjB,CACEJ,GAAI,EACJC,KAAM,aACNC,KAAM,kHACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,WACjB,CAAEF,GAAI,IAAKE,KAAM,gBACjB,CAAEF,GAAI,IAAKE,KAAM,aACjB,CAAEF,GAAI,IAAKE,KAAM,eAEnBE,cAAe,KAEjB,CACEJ,GAAI,GACJC,KAAM,aACNC,KAAM,+IACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,eACjB,CAAEF,GAAI,IAAKE,KAAM,oBACjB,CAAEF,GAAI,IAAKE,KAAM,cACjB,CAAEF,GAAI,IAAKE,KAAM,qBAEnBE,cAAe,KAIjB,CACEJ,GAAI,GACJC,KAAM,cACNC,KAAM,uCACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,wCACjB,CAAEF,GAAI,IAAKE,KAAM,qCACjB,CAAEF,GAAI,IAAKE,KAAM,iCACjB,CAAEF,GAAI,IAAKE,KAAM,yCAEnBE,cAAe,KAEjB,CACEJ,GAAI,GACJC,KAAM,cACNC,KAAM,sCACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,4CACjB,CAAEF,GAAI,IAAKE,KAAM,8EACjB,CAAEF,GAAI,IAAKE,KAAM,wCACjB,CAAEF,GAAI,IAAKE,KAAM,0CAEnBE,cAAe,KAIjB,CACEJ,GAAI,GACJC,KAAM,cACNC,KAAM,iEACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,2CACjB,CAAEF,GAAI,IAAKE,KAAM,2DACjB,CAAEF,GAAI,IAAKE,KAAM,sCACjB,CAAEF,GAAI,IAAKE,KAAM,iCAEnBE,cAAe,KAEjB,CACEJ,GAAI,GACJC,KAAM,cACNC,KAAM,8DACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,qCACjB,CAAEF,GAAI,IAAKE,KAAM,4BACjB,CAAEF,GAAI,IAAKE,KAAM,2CACjB,CAAEF,GAAI,IAAKE,KAAM,mCAEnBE,cAAe,KAIjB,CACEJ,GAAI,GACJC,KAAM,iBACNC,KAAM,mDACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,aACjB,CAAEF,GAAI,IAAKE,KAAM,UACjB,CAAEF,GAAI,IAAKE,KAAM,eACjB,CAAEF,GAAI,IAAKE,KAAM,WAEnBE,cAAe,KAEjB,CACEJ,GAAI,GACJC,KAAM,iBACNC,KAAM,+DACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,QACjB,CAAEF,GAAI,IAAKE,KAAM,YACjB,CAAEF,GAAI,IAAKE,KAAM,SACjB,CAAEF,GAAI,IAAKE,KAAM,UAEnBE,cAAe,KAIjB,CACEJ,GAAI,GACJC,KAAM,YACNC,KAAM,gDACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,kBACjB,CAAEF,GAAI,IAAKE,KAAM,gBACjB,CAAEF,GAAI,IAAKE,KAAM,iDACjB,CAAEF,GAAI,IAAKE,KAAM,cAEnBE,cAAe,KAEjB,CACEJ,GAAI,GACJC,KAAM,YACNC,KAAM,mDACNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,iBACjB,CAAEF,GAAI,IAAKE,KAAM,qCACjB,CAAEF,GAAI,IAAKE,KAAM,wBACjB,CAAEF,GAAI,IAAKE,KAAM,sBAEnBE,cAAe,KAIjB,CACEJ,GAAI,GACJC,KAAM,kBACNC,KAAM,yiCAKNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,4DACjB,CAAEF,GAAI,IAAKE,KAAM,0EACjB,CAAEF,GAAI,IAAKE,KAAM,4CACjB,CAAEF,GAAI,IAAKE,KAAM,kEAEnBE,cAAe,KAEjB,CACEJ,GAAI,GACJC,KAAM,kBACNC,KAAM,ylCAKNC,QAAS,CACP,CAAEH,GAAI,IAAKE,KAAM,2CACjB,CAAEF,GAAI,IAAKE,KAAM,4BACjB,CAAEF,GAAI,IAAKE,KAAM,6BACjB,CAAEF,GAAI,IAAKE,KAAM,gEAEnBE,cAAe,OAKnB1B,GAAW,SACJ2B,GACCC,QAAAD,MAAM,6BAA8BA,GAC5CZ,EAAU,0CACVf,GAAW,EACb,QAID,IAGHiB,EAAAA,WAAU,KACJ,IAACP,GAAeF,GAAY,EAAG,OAE7B,MAAAqB,EAAQC,aAAY,KACxBrB,GAAwBsB,GAClBA,GAAY,GACdC,cAAcH,OAEP,GAEFE,EAAW,GACnB,GACA,KAEI,MAAA,IAAMC,cAAcH,EAAK,GAC/B,CAACnB,EAAaF,IAEjB,MAwBMyB,EAAmB,KAIvB,MAAMC,EAAoBC,OAAOC,KAAK9B,GAAS+B,OACzCC,EAAiBpC,EAAUmC,OAEjCxB,EAAY,mCAAmCqB,QAAwBI,gBAM9DzC,EAAA,sBAAgC,EASrC0C,EAAwBhB,IAC5B,OAAQA,GACN,IAAK,WAAmB,MAAA,YACxB,IAAK,WAAmB,MAAA,YACxB,IAAK,YAAoB,MAAA,YACzB,IAAK,UAAkB,MAAA,sBACvB,IAAK,kBAA0B,MAAA,kCAC/B,IAAK,aAAqB,MAAA,oBAC1B,IAAK,cAAsB,MAAA,eAC3B,IAAK,cAAsB,MAAA,yBAC3B,IAAK,iBAAyB,MAAA,4BAC9B,IAAK,YAAoB,MAAA,4BACzB,QAAgB,OAAAA,EAClB,EAGIiB,EAAsBtC,EAAUE,GAChCqC,IAAaD,GAAsBlC,EAAQkC,EAAoBlB,IAGnE,SAAAoB,KAAC,MAAI,CAAAC,UAAU,mCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,yCACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,wCAAwCC,SAAsB,2BAC3EC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAAkE,0EAEhGlC,GACCoC,EAAAD,IAAC,MAAI,CAAAF,UAAW,sBAAqBnC,EAAW,GAAK,6BAA+BA,EAAW,IAAM,kBAAoB,iBACtHoC,SAlCQ,CAACG,IAClB,MACMC,EAAOD,EAAU,GACvB,MAAO,GAFME,KAAKC,MAAMH,EAAU,IAEnBI,WAAWC,SAAS,EAAG,QAAQJ,EAAKG,WAAWC,SAAS,EAAG,MAAI,EA+BrEC,CAAW7C,QAKjBT,QACE,MAAI,CAAA4C,UAAU,oBACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,4CACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,wEACdE,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAAkC,4CAGhElC,EA2FHmC,EAAAA,IAAAS,EAAAA,SAAA,CACEV,gBAAC,MAAA,CAAID,UAAU,wCACbC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,gBACbC,SAACF,EAAAA,KAAAa,EAAA,CAAKZ,UAAU,OACdC,SAAA,GAACF,KAAAc,EAAA,CAAWb,UAAU,oCACpBC,SAAA,QAAC,MACC,CAAAA,SAAA,GAACF,KAAA,KAAA,CAAGC,UAAU,sBAAsBC,SAAA,CAAA,YACxBxC,EAAkB,EAAE,OAAKF,EAAUmC,UAE/CQ,EAAAA,IAAC,KAAEF,UAAU,wBACVC,WAAsBL,EAAqBC,EAAoBjB,MAAQ,cAG3E,MAAI,CAAAoB,UAAU,wBACZC,SAAAH,EAAa,aAAe,qBAGjCI,EAAAA,IAACY,EACE,CAAAb,SAAAJ,GAEGE,OAAAY,EAAAA,SAAA,CAAAV,SAAA,CAAAC,EAAAA,IAAC,MAAI,CAAAF,UAAU,yCAA0CC,SAAAJ,EAAoBhB,OAC7EqB,EAAAA,IAAC,OAAIF,UAAU,YACZC,WAAoBnB,QAAQiC,KAAKC,GAChCb,EAAAD,IAAC,MAAA,CAECF,UAAW,2DACTrC,EAAQkC,EAAoBlB,MAAQqC,EAAOrC,GACvC,6BACA,oBAENsC,QAAS,KAAMC,OA1MbC,EA0MgCtB,EAAoBlB,GA1MxCyC,EA0M4CJ,EAAOrC,QAzM9Ef,EAAA,IACND,EACHwD,CAACA,GAAaC,IAHS,IAACD,EAAYC,CA0MqD,EAEnEnB,SAAAE,EAAAJ,KAAC,MAAI,CAAAC,UAAU,oBACbC,SAAA,CAAAC,MAAC,MAAI,CAAAF,UAAW,+DACdrC,EAAQkC,EAAoBlB,MAAQqC,EAAOrC,GACvC,yBACA,6BAEHsB,SAAOe,EAAArC,GAAG0C,gBAEZnB,EAAAA,IAAA,MAAA,CAAIF,UAAU,gBAAiBC,WAAOpB,WAhBpCmC,EAAOrC,eAwBxBoB,KAACuB,EAAW,CAAAtB,UAAU,uBACpBC,SAAA,CAAAE,EAAAD,IAACqB,EAAA,CACCC,QAAQ,UACRP,QAlNa,KACzBxD,EAAkB,GACpBC,EAAmBD,EAAkB,EACvC,EAgNgBgE,SAA8B,IAApBhE,EACXwC,SAAA,aAGAxC,EAAkBF,EAAUmC,OAAS,EACpCS,EAAAD,IAACqB,EAAA,CACCC,QAAQ,UACRP,QAhOO,KACrBxD,EAAkBF,EAAUmC,OAAS,GACvChC,EAAmBD,EAAkB,EACvC,EA8NiBwC,SAAA,cAIDE,EAAAD,IAACqB,EAAA,CACCC,QAAQ,UACRP,QAAS3B,EACVW,SAAA,2BAQRC,MAAA,MAAA,CACCD,SAACF,EAAAA,KAAAa,EAAA,CAAKZ,UAAU,eACdC,SAAA,CAAAC,EAAAA,IAACW,GACCZ,SAACE,EAAAD,IAAA,KAAA,CAAGF,UAAU,sBAAsBC,iCAErCa,EACC,CAAAb,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,yBACZC,WAAUc,KAAI,CAACW,EAAUC,IACxBxB,EAAAD,IAAC,SAAA,CAECF,UAAW,6CACTvC,IAAoBkE,EAChB,yBACAhE,EAAQ+D,EAAS/C,IACf,sDACA,+CAERsC,QAAS,IAAMvD,EAAmBiE,GAClCC,MAAOhC,EAAqB8B,EAAS9C,MAEpCqB,SAAQ0B,EAAA,GAXJD,EAAS/C,UAgBpBoB,KAAC,MAAI,CAAAC,UAAU,OACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,iDACbC,SAAA,GAAAC,IAAC,QAAKD,SAAQ,oBACb,OAAM,CAAAA,SAAA,CAAOT,OAAAC,KAAK9B,GAAS+B,OAAO,OAAKnC,EAAUmC,eAEpDQ,IAAC,MAAI,CAAAF,UAAU,sCACbC,SAAAE,EAAAD,IAAC,MAAA,CACCF,UAAU,gCACV6B,MAAO,CAAEC,MAAWtC,OAAOC,KAAK9B,GAAS+B,OAASnC,EAAUmC,OAAU,IAAtD,kBAKrB,MAAI,CAAAM,UAAU,OACbC,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,wDACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAkB,uBACzEC,EAAAA,IAAC,OAAIF,UAAU,YACZC,eAAM8B,KAAK,IAAIC,IAAIzE,EAAUwD,QAASkB,EAAErD,SAAQmC,KAC/CnC,GAAAmB,EAAAA,KAAC,MAAe,CAAAC,UAAU,4BACxBC,SAAA,GAACC,IAAA,OAAA,CAAKF,UAAU,gDACf,OAAK,CAAAA,UAAU,gBAAiBC,SAAAL,EAAqBhB,OAF9CA,YASlBuB,EAAAD,IAACqB,EAAA,CACCC,QAAQ,UACRxB,UAAU,cACViB,QAAS3B,EACVW,SAAA,uCA7NVW,EACC,CAAAX,SAAA,CAAAC,EAAAA,IAACW,GACCZ,SAACE,EAAAD,IAAA,KAAA,CAAGF,UAAU,sCAAsCC,6CAErDC,MAAAY,EAAA,CACCb,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,YACbC,SAAA,QAAC,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAA0B,+BAChFC,EAAAA,IAAA,IAAA,CAAEF,UAAU,qBAAqBC,SAElC,uQACCC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,kRAGD,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAA4B,iCAClFC,EAAAA,IAAA,IAAA,CAAEF,UAAU,qBAAqBC,SAElC,4GACAF,KAAC,MAAI,CAAAC,UAAU,wCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,iDACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,iCAAiCC,SAAkB,yBACjEF,KAAC,KAAG,CAAAC,UAAU,yCACZC,SAAA,GAAAC,IAAC,MAAGD,SAAqB,4BACzBC,IAAC,MAAGD,SAAwB,+BAC5BC,IAAC,MAAGD,SAAkB,+BAI1BF,KAAC,MAAI,CAAAC,UAAU,iDACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,iCAAiCC,SAAmB,0BAClEF,KAAC,KAAG,CAAAC,UAAU,yCACZC,SAAA,GAAAC,IAAC,MAAGD,SAAyB,gCAC7BC,IAAC,MAAGD,SAAyB,gCAC7BC,IAAC,MAAGD,SAAsB,mCAI9BF,KAAC,MAAI,CAAAC,UAAU,iDACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,iCAAiCC,SAAmB,0BAClEF,KAAC,KAAG,CAAAC,UAAU,yCACZC,SAAA,GAAAC,IAAC,MAAGD,SAA6B,oCACjCC,IAAC,MAAGD,SAA+B,sCACnCC,IAAC,MAAGD,SAAsB,mCAI9BF,KAAC,MAAI,CAAAC,UAAU,iDACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,iCAAiCC,SAAgB,uBAC/DF,KAAC,KAAG,CAAAC,UAAU,yCACZC,SAAA,GAAAC,IAAC,MAAGD,SAAmB,0BACvBC,IAAC,MAAGD,SAAyB,gCAC7BC,IAAC,MAAGD,SAAkB,0CAM7B,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAsB,6BAC7EF,KAAC,KAAG,CAAAC,UAAU,yCACZC,SAAA,GAAAC,IAAC,MAAGD,SAAmF,+FACtF,KAAG,CAAAA,SAAA,CAAA,iBAAeC,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAU,eAAO,4CACjEC,IAAC,MAAGD,SAAsG,6GAC1GC,IAAC,MAAGD,SAAoH,2HACxHC,IAAC,MAAGD,SAAiF,8FAIzFF,KAAC,MAAI,CAAAC,UAAU,gDACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,2CAA2CC,SAAU,eAClEC,EAAAA,IAAA,IAAA,CAAEF,UAAU,kBAAkBC,SAE/B,yOAINC,IAACoB,EAAW,CAAAtB,UAAU,mBACpBC,SAAAE,EAAAD,IAACqB,EAAA,CACCC,QAAQ,UACRP,QAzKY,KACtBjD,GAAe,GACfC,EAAS,gCAA+B,EAwK9B+B,UAAU,YACXC,SAAA,yBAkJT"}