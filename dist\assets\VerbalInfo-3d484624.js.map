{"version": 3, "file": "VerbalInfo-3d484624.js", "sources": ["../../src/pages/landing/VerbalInfo.jsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { <PERSON>, CardHeader, CardBody, CardFooter } from '../../components/ui/Card';\nimport { Button } from '../../components/ui/Button';\n\nconst VerbalInfo = () => {\n  const navigate = useNavigate();\n\n  const handleStartTest = () => {\n    navigate('/test/instructions/verbal');\n  };\n  \n  const handleViewSample = () => {\n    navigate('/test/verbal');\n  };\n\n  return (\n    <div className=\"container mx-auto py-8 px-4 max-w-6xl\">\n      <div className=\"text-center mb-12\">\n        <h1 className=\"text-3xl md:text-4xl font-bold text-gray-800 mb-4\">Test de Aptitud Verbal</h1>\n        <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n          Evalúa tu capacidad para comprender, razonar y comunicarte efectivamente a través del lenguaje\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12\">\n        <div className=\"lg:col-span-2\">\n          <Card>\n            <CardHeader>\n              <h2 className=\"text-2xl font-semibold text-gray-800\">¿Qué es la Aptitud Verbal?</h2>\n            </CardHeader>\n            <CardBody className=\"space-y-6\">\n              <p className=\"text-gray-700\">\n                La aptitud verbal es la capacidad para comprender ideas expresadas en palabras. Es una habilidad fundamental que implica el dominio del lenguaje, la comprensión de conceptos verbales, el razonamiento con palabras y la capacidad para expresar ideas de manera efectiva.\n              </p>\n              \n              <div>\n                <h3 className=\"text-lg font-medium text-gray-800 mb-3\">La aptitud verbal incluye:</h3>\n                <ul className=\"space-y-2 text-gray-700\">\n                  <li className=\"flex items-start\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span><strong>Comprensión verbal:</strong> Entender el significado de palabras, frases y textos.</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span><strong>Razonamiento verbal:</strong> Capacidad para analizar relaciones entre conceptos expresados en palabras.</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span><strong>Vocabulario:</strong> Conocimiento de palabras, sus significados y usos adecuados.</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span><strong>Comprensión lectora:</strong> Habilidad para entender textos completos, extraer información y hacer inferencias.</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span><strong>Expresión verbal:</strong> Capacidad para comunicar ideas de manera clara y precisa.</span>\n                  </li>\n                </ul>\n              </div>\n              \n              <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-100\">\n                <h3 className=\"text-lg font-medium text-blue-800 mb-2\">¿Por qué es importante?</h3>\n                <p className=\"text-blue-700\">\n                  La aptitud verbal es esencial en casi todos los ámbitos académicos y profesionales. Permite expresar ideas con claridad, comprender instrucciones complejas, interpretar información, participar en discusiones productivas y resolver problemas que involucran el uso del lenguaje.\n                </p>\n              </div>\n            </CardBody>\n          </Card>\n        </div>\n        \n        <div>\n          <Card className=\"mb-6 bg-gradient-to-br from-blue-50 to-indigo-50\">\n            <CardBody className=\"text-center p-6\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mx-auto mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">Test de Aptitud Verbal</h3>\n              <p className=\"text-gray-600 mb-6\">\n                Evalúa tus habilidades verbales con este test completo y recibe un análisis detallado de tus fortalezas y áreas de mejora.\n              </p>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center text-gray-700\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>Duración: 30 minutos</span>\n                </div>\n                <div className=\"flex items-center text-gray-700\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" />\n                    <path fillRule=\"evenodd\" d=\"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>20 preguntas</span>\n                </div>\n                <div className=\"flex items-center text-gray-700\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>Resultados inmediatos</span>\n                </div>\n              </div>\n              <Button\n                variant=\"primary\"\n                className=\"w-full mt-6\"\n                onClick={handleStartTest}\n              >\n                Iniciar Test\n              </Button>\n              <button \n                className=\"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                onClick={handleViewSample}\n              >\n                Ver ejemplo del test\n              </button>\n            </CardBody>\n          </Card>\n          \n          <Card>\n            <CardHeader>\n              <h3 className=\"text-lg font-medium text-gray-800\">Relevancia Profesional</h3>\n            </CardHeader>\n            <CardBody>\n              <ul className=\"space-y-3\">\n                <li className=\"flex items-start\">\n                  <div className=\"w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Derecho</span>\n                    <p className=\"text-sm text-gray-600\">Esencial para interpretar textos legales, argumentar y redactar documentos.</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <div className=\"w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Educación</span>\n                    <p className=\"text-sm text-gray-600\">Fundamental para transmitir conocimientos con claridad y precisión.</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <div className=\"w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Comunicación</span>\n                    <p className=\"text-sm text-gray-600\">Indispensable para periodistas, escritores y profesionales del marketing.</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <div className=\"w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3 flex-shrink-0 mt-0.5\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Psicología</span>\n                    <p className=\"text-sm text-gray-600\">Importante para la comunicación terapéutica y el análisis de casos.</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <div className=\"w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3 flex-shrink-0 mt-0.5\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Ciencias</span>\n                    <p className=\"text-sm text-gray-600\">Útil para comunicar conceptos complejos y resultados de investigación.</p>\n                  </div>\n                </li>\n              </ul>\n            </CardBody>\n          </Card>\n        </div>\n      </div>\n      \n      <div className=\"mb-12\">\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-2xl font-semibold text-gray-800\">Componentes del Test de Aptitud Verbal</h2>\n          </CardHeader>\n          <CardBody>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              <div className=\"bg-white p-5 rounded-lg border border-gray-200 shadow-sm\">\n                <div className=\"w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center text-blue-600 mb-4\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">Sinónimos y Antónimos</h3>\n                <p className=\"text-gray-600\">\n                  Evalúa tu vocabulario y comprensión de las relaciones entre palabras, midiendo tu capacidad para identificar significados similares u opuestos.\n                </p>\n              </div>\n              \n              <div className=\"bg-white p-5 rounded-lg border border-gray-200 shadow-sm\">\n                <div className=\"w-12 h-12 rounded-lg bg-indigo-100 flex items-center justify-center text-indigo-600 mb-4\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">Analogías Verbales</h3>\n                <p className=\"text-gray-600\">\n                  Mide tu capacidad para identificar relaciones lógicas entre pares de palabras, evaluando tu pensamiento abstracto y razonamiento verbal.\n                </p>\n              </div>\n              \n              <div className=\"bg-white p-5 rounded-lg border border-gray-200 shadow-sm\">\n                <div className=\"w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mb-4\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">Comprensión Lectora</h3>\n                <p className=\"text-gray-600\">\n                  Evalúa tu capacidad para entender textos, extraer información relevante, hacer inferencias y comprender ideas centrales y secundarias.\n                </p>\n              </div>\n              \n              <div className=\"bg-white p-5 rounded-lg border border-gray-200 shadow-sm\">\n                <div className=\"w-12 h-12 rounded-lg bg-yellow-100 flex items-center justify-center text-yellow-600 mb-4\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">Completar Oraciones</h3>\n                <p className=\"text-gray-600\">\n                  Evalúa tu sentido del contexto lingüístico y la coherencia textual, midiendo tu capacidad para seleccionar palabras adecuadas.\n                </p>\n              </div>\n              \n              <div className=\"bg-white p-5 rounded-lg border border-gray-200 shadow-sm\">\n                <div className=\"w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center text-red-600 mb-4\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">Definiciones</h3>\n                <p className=\"text-gray-600\">\n                  Evalúa tu precisión en la comprensión de términos y tu capacidad para identificar definiciones exactas de conceptos.\n                </p>\n              </div>\n              \n              <div className=\"bg-white p-5 rounded-lg border border-gray-200 shadow-sm\">\n                <div className=\"w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center text-purple-600 mb-4\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">Expresiones y Refranes</h3>\n                <p className=\"text-gray-600\">\n                  Mide tu comprensión del lenguaje figurado y las expresiones idiomáticas, evaluando tu familiaridad con el uso cultural del lenguaje.\n                </p>\n              </div>\n            </div>\n          </CardBody>\n        </Card>\n      </div>\n      \n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-semibold text-gray-800 mb-4\">¿Listo para evaluar tu aptitud verbal?</h2>\n        <p className=\"text-lg text-gray-600 max-w-3xl mx-auto mb-6\">\n          Descubre tus fortalezas y áreas de mejora en comprensión y razonamiento verbal con nuestro test completo.\n        </p>\n        <Button\n          variant=\"primary\"\n          size=\"lg\"\n          className=\"px-8\"\n          onClick={handleStartTest}\n        >\n          Iniciar Test de Aptitud Verbal\n        </Button>\n      </div>\n    </div>\n  );\n};\n\nexport default VerbalInfo;"], "names": ["VerbalInfo", "navigate", "useNavigate", "handleStartTest", "jsxs", "className", "children", "jsx", "jsxRuntimeExports", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardBody", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "<PERSON><PERSON>", "variant", "onClick", "size"], "mappings": "6IAKA,MAAMA,EAAa,KACjB,MAAMC,EAAWC,IAEXC,EAAkB,KACtBF,EAAS,4BAA2B,EAQpC,SAAAG,KAAC,MAAI,CAAAC,UAAU,wCACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,oDAAoDC,SAAsB,2BACvFC,EAAAA,IAAA,IAAA,CAAEF,UAAU,0CAA0CC,SAEvD,wGAGFF,KAAC,MAAI,CAAAC,UAAU,8CACbC,SAAA,CAAAC,MAAC,MAAI,CAAAF,UAAU,gBACbC,SAAAE,EAAAJ,KAACK,EACC,CAAAH,SAAA,CAAAC,EAAAA,IAACG,GACCJ,SAACE,EAAAD,IAAA,KAAA,CAAGF,UAAU,uCAAuCC,4CAEvDF,KAACO,EAAS,CAAAN,UAAU,YAClBC,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,uRAEC,MACC,CAAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAA0B,iCACjFF,KAAC,KAAG,CAAAC,UAAU,0BACZC,SAAA,GAACF,KAAA,KAAA,CAAGC,UAAU,mBACZC,SAAA,CAAAC,MAAC,OAAIK,MAAM,6BAA6BP,UAAU,kDAAkDQ,QAAQ,YAAYC,KAAK,eAC3HR,eAAC,QAAKS,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,qBAE1J,OAAK,CAAAX,SAAA,GAAAC,IAAC,UAAOD,SAAmB,wBAAS,iEAE5CF,KAAC,KAAG,CAAAC,UAAU,mBACZC,SAAA,CAAAC,MAAC,OAAIK,MAAM,6BAA6BP,UAAU,kDAAkDQ,QAAQ,YAAYC,KAAK,eAC3HR,eAAC,QAAKS,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,qBAE1J,OAAK,CAAAX,SAAA,GAAAC,IAAC,UAAOD,SAAoB,yBAAS,sFAE7CF,KAAC,KAAG,CAAAC,UAAU,mBACZC,SAAA,CAAAC,MAAC,OAAIK,MAAM,6BAA6BP,UAAU,kDAAkDQ,QAAQ,YAAYC,KAAK,eAC3HR,eAAC,QAAKS,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,qBAE1J,OAAK,CAAAX,SAAA,GAAAC,IAAC,UAAOD,SAAY,iBAAS,wEAErCF,KAAC,KAAG,CAAAC,UAAU,mBACZC,SAAA,CAAAC,MAAC,OAAIK,MAAM,6BAA6BP,UAAU,kDAAkDQ,QAAQ,YAAYC,KAAK,eAC3HR,eAAC,QAAKS,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,qBAE1J,OAAK,CAAAX,SAAA,GAAAC,IAAC,UAAOD,SAAoB,yBAAS,8FAE7CF,KAAC,KAAG,CAAAC,UAAU,mBACZC,SAAA,CAAAC,MAAC,OAAIK,MAAM,6BAA6BP,UAAU,kDAAkDQ,QAAQ,YAAYC,KAAK,eAC3HR,eAAC,QAAKS,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,qBAE1J,OAAK,CAAAX,SAAA,GAAAC,IAAC,UAAOD,SAAiB,sBAAS,2EAK9CF,KAAC,MAAI,CAAAC,UAAU,mDACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAuB,4BAC7EC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,2SAMP,MACC,CAAAA,SAAA,CAAAC,EAAAA,IAACE,GAAKJ,UAAU,mDACdC,SAACF,EAAAA,KAAAO,EAAA,CAASN,UAAU,kBAClBC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,iGACbC,SAACC,EAAAA,IAAA,MAAA,CAAIK,MAAM,6BAA6BP,UAAU,UAAUS,KAAK,OAAOD,QAAQ,YAAYK,OAAO,eACjGZ,SAAAC,EAAAA,IAAC,OAAK,CAAAY,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGL,EAAE,kEAGxET,EAAAA,IAAA,KAAA,CAAGF,UAAU,2CAA2CC,SAAsB,2BAC9EC,EAAAA,IAAA,IAAA,CAAEF,UAAU,qBAAqBC,SAElC,iIACAF,KAAC,MAAI,CAAAC,UAAU,YACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,kCACbC,SAAA,CAAAC,MAAC,OAAIK,MAAM,6BAA6BP,UAAU,6BAA6BQ,QAAQ,YAAYC,KAAK,eACtGR,eAAC,QAAKS,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,gBAE3JV,IAAC,QAAKD,SAAoB,8BAE5BF,KAAC,MAAI,CAAAC,UAAU,kCACbC,SAAA,CAACF,EAAAA,KAAA,MAAA,CAAIQ,MAAM,6BAA6BP,UAAU,6BAA6BQ,QAAQ,YAAYC,KAAK,eACtGR,SAAA,GAACC,IAAA,OAAA,CAAKS,EAAE,4CACP,OAAK,CAAAD,SAAS,UAAUC,EAAE,sOAAsOC,SAAS,iBAE5QV,IAAC,QAAKD,SAAY,sBAEpBF,KAAC,MAAI,CAAAC,UAAU,kCACbC,SAAA,CAAAC,MAAC,OAAIK,MAAM,6BAA6BP,UAAU,6BAA6BQ,QAAQ,YAAYC,KAAK,eACtGR,eAAC,QAAKS,SAAS,UAAUC,EAAE,kiBAAkiBC,SAAS,gBAExkBV,IAAC,QAAKD,SAAqB,gCAG/BE,EAAAD,IAACe,EAAA,CACCC,QAAQ,UACRlB,UAAU,cACVmB,QAASrB,EACVG,SAAA,iBAGDE,EAAAD,IAAC,SAAA,CACCF,UAAU,6DACVmB,QAhHW,KACvBvB,EAAS,eAAc,EAgHZK,SAAA,qCAMJG,EACC,CAAAH,SAAA,CAAAC,EAAAA,IAACG,GACCJ,SAACE,EAAAD,IAAA,KAAA,CAAGF,UAAU,oCAAoCC,sCAEnDC,MAAAI,EAAA,CACCL,SAACF,EAAAA,KAAA,KAAA,CAAGC,UAAU,YACZC,SAAA,GAACF,KAAA,KAAA,CAAGC,UAAU,mBACZC,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAIF,UAAU,8GACbC,SAAAC,EAAAA,IAAC,OAAIK,MAAM,6BAA6BP,UAAU,UAAUQ,QAAQ,YAAYC,KAAK,eACnFR,SAAAC,EAAAA,IAAC,QAAKQ,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,uBAG5J,MACC,CAAAX,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAO,YACpCC,EAAAA,IAAA,IAAA,CAAEF,UAAU,wBAAwBC,SAA2E,wFAGpHF,KAAC,KAAG,CAAAC,UAAU,mBACZC,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAIF,UAAU,8GACbC,SAAAC,EAAAA,IAAC,OAAIK,MAAM,6BAA6BP,UAAU,UAAUQ,QAAQ,YAAYC,KAAK,eACnFR,SAAAC,EAAAA,IAAC,QAAKQ,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,uBAG5J,MACC,CAAAX,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAS,cACtCC,EAAAA,IAAA,IAAA,CAAEF,UAAU,wBAAwBC,SAAmE,gFAG5GF,KAAC,KAAG,CAAAC,UAAU,mBACZC,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAIF,UAAU,8GACbC,SAAAC,EAAAA,IAAC,OAAIK,MAAM,6BAA6BP,UAAU,UAAUQ,QAAQ,YAAYC,KAAK,eACnFR,SAAAC,EAAAA,IAAC,QAAKQ,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,uBAG5J,MACC,CAAAX,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAY,iBACzCC,EAAAA,IAAA,IAAA,CAAEF,UAAU,wBAAwBC,SAAyE,sFAGlHF,KAAC,KAAG,CAAAC,UAAU,mBACZC,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAIF,UAAU,4GACbC,SAAAC,EAAAA,IAAC,OAAIK,MAAM,6BAA6BP,UAAU,UAAUQ,QAAQ,YAAYC,KAAK,eACnFR,SAAAC,EAAAA,IAAC,QAAKQ,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,uBAG5J,MACC,CAAAX,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAU,eACvCC,EAAAA,IAAA,IAAA,CAAEF,UAAU,wBAAwBC,SAAmE,gFAG5GF,KAAC,KAAG,CAAAC,UAAU,mBACZC,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAIF,UAAU,4GACbC,SAAAC,EAAAA,IAAC,OAAIK,MAAM,6BAA6BP,UAAU,UAAUQ,QAAQ,YAAYC,KAAK,eACnFR,SAAAC,EAAAA,IAAC,QAAKQ,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,uBAG5J,MACC,CAAAX,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAKF,UAAU,cAAcC,SAAQ,aACrCC,EAAAA,IAAA,IAAA,CAAEF,UAAU,wBAAwBC,SAAsE,+FASxHC,MAAA,MAAA,CAAIF,UAAU,QACbC,gBAACG,EACC,CAAAH,SAAA,CAAAC,EAAAA,IAACG,GACCJ,SAACE,EAAAD,IAAA,KAAA,CAAGF,UAAU,uCAAuCC,sDAEtDC,MAAAI,EAAA,CACCL,SAACF,EAAAA,KAAA,MAAA,CAAIC,UAAU,uDACbC,SAAA,GAACF,KAAA,MAAA,CAAIC,UAAU,2DACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,uFACbC,SAACC,EAAAA,IAAA,MAAA,CAAIK,MAAM,6BAA6BP,UAAU,UAAUS,KAAK,OAAOD,QAAQ,YAAYK,OAAO,eACjGZ,SAAAC,EAAAA,IAAC,OAAK,CAAAY,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGL,EAAE,oGAGxET,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAqB,0BAC3EC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,yJAGFF,KAAC,MAAI,CAAAC,UAAU,2DACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,2FACbC,SAACC,EAAAA,IAAA,MAAA,CAAIK,MAAM,6BAA6BP,UAAU,UAAUS,KAAK,OAAOD,QAAQ,YAAYK,OAAO,eACjGZ,SAAAC,EAAAA,IAAC,OAAK,CAAAY,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGL,EAAE,mCAGxET,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAkB,uBACxEC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,kJAGFF,KAAC,MAAI,CAAAC,UAAU,2DACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,yFACbC,SAACC,EAAAA,IAAA,MAAA,CAAIK,MAAM,6BAA6BP,UAAU,UAAUS,KAAK,OAAOD,QAAQ,YAAYK,OAAO,eACjGZ,SAAAC,EAAAA,IAAC,OAAK,CAAAY,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGL,EAAE,2PAGxET,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAmB,wBACzEC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,gJAGFF,KAAC,MAAI,CAAAC,UAAU,2DACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,2FACbC,SAACC,EAAAA,IAAA,MAAA,CAAIK,MAAM,6BAA6BP,UAAU,UAAUS,KAAK,OAAOD,QAAQ,YAAYK,OAAO,eACjGZ,SAAAC,EAAAA,IAAC,OAAK,CAAAY,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGL,EAAE,+HAGxET,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAmB,wBACzEC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,wIAGFF,KAAC,MAAI,CAAAC,UAAU,2DACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,qFACbC,SAACC,EAAAA,IAAA,MAAA,CAAIK,MAAM,6BAA6BP,UAAU,UAAUS,KAAK,OAAOD,QAAQ,YAAYK,OAAO,eACjGZ,SAAAC,EAAAA,IAAC,OAAK,CAAAY,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGL,EAAE,6HAGxET,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAY,iBAClEC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,8HAGFF,KAAC,MAAI,CAAAC,UAAU,2DACbC,SAAA,GAACC,IAAA,MAAA,CAAIF,UAAU,2FACbC,SAACC,EAAAA,IAAA,MAAA,CAAIK,MAAM,6BAA6BP,UAAU,UAAUS,KAAK,OAAOD,QAAQ,YAAYK,OAAO,eACjGZ,SAAAC,EAAAA,IAAC,OAAK,CAAAY,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGL,EAAE,6FAGxET,EAAAA,IAAA,KAAA,CAAGF,UAAU,yCAAyCC,SAAsB,2BAC5EC,EAAAA,IAAA,IAAA,CAAEF,UAAU,gBAAgBC,SAE7B,wJAOVF,KAAC,MAAI,CAAAC,UAAU,mBACbC,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAGF,UAAU,4CAA4CC,SAAsC,2CAC/FC,EAAAA,IAAA,IAAA,CAAEF,UAAU,+CAA+CC,SAE5D,8GACAE,EAAAD,IAACe,EAAA,CACCC,QAAQ,UACRE,KAAK,KACLpB,UAAU,OACVmB,QAASrB,EACVG,SAAA,wCAIL"}