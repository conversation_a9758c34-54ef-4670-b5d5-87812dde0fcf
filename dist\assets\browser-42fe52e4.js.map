{"version": 3, "file": "browser-42fe52e4.js", "sources": ["../../node_modules/ws/browser.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n"], "names": ["browser", "Error"], "mappings": "4XAEA,IAAAA,EAAiB,WACf,MAAM,IAAIC,MACR,wFAGJ", "x_google_ignoreList": [0]}