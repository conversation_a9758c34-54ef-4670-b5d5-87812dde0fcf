var e=Object.defineProperty,t=(t,n,r)=>(((t,n,r)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r})(t,"symbol"!=typeof n?n+"":n,r),r);function n(e,t){for(var n=0;n<t.length;n++){const r=t[n];if("string"!=typeof r&&!Array.isArray(r))for(const t in r)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(r,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>r[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function o(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})})),n}var i={exports:{}},s={},l={exports:{}},u={},c=Symbol.for("react.element"),d=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),h=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),m=Symbol.for("react.provider"),g=Symbol.for("react.context"),y=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),b=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),_=Symbol.iterator;var k={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},S=Object.assign,E={};function x(e,t,n){this.props=e,this.context=t,this.refs=E,this.updater=n||k}function j(){}function T(e,t,n){this.props=e,this.context=t,this.refs=E,this.updater=n||k}x.prototype.isReactComponent={},x.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},x.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},j.prototype=x.prototype;var P=T.prototype=new j;P.constructor=T,S(P,x.prototype),P.isPureReactComponent=!0;var C=Array.isArray,O=Object.prototype.hasOwnProperty,N={current:null},R={key:!0,ref:!0,__self:!0,__source:!0};function I(e,t,n){var r,a={},o=null,i=null;if(null!=t)for(r in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(o=""+t.key),t)O.call(t,r)&&!R.hasOwnProperty(r)&&(a[r]=t[r]);var s=arguments.length-2;if(1===s)a.children=n;else if(1<s){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+2];a.children=l}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===a[r]&&(a[r]=s[r]);return{$$typeof:c,type:e,key:o,ref:i,props:a,_owner:N.current}}function L(e){return"object"==typeof e&&null!==e&&e.$$typeof===c}var $=/\/+/g;function A(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function D(e,t,n,r,a){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var i=!1;if(null===e)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case c:case d:i=!0}}if(i)return a=a(i=e),e=""===r?"."+A(i,0):r,C(a)?(n="",null!=e&&(n=e.replace($,"$&/")+"/"),D(a,t,n,"",(function(e){return e}))):null!=a&&(L(a)&&(a=function(e,t){return{$$typeof:c,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,n+(!a.key||i&&i.key===a.key?"":(""+a.key).replace($,"$&/")+"/")+e)),t.push(a)),1;if(i=0,r=""===r?".":r+":",C(e))for(var s=0;s<e.length;s++){var l=r+A(o=e[s],s);i+=D(o,t,n,l,a)}else if(l=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=_&&e[_]||e["@@iterator"])?e:null}(e),"function"==typeof l)for(e=l.call(e),s=0;!(o=e.next()).done;)i+=D(o=o.value,t,n,l=r+A(o,s++),a);else if("object"===o)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function z(e,t,n){if(null==e)return e;var r=[],a=0;return D(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function U(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var M={current:null},F={transition:null},B={ReactCurrentDispatcher:M,ReactCurrentBatchConfig:F,ReactCurrentOwner:N};function q(){throw Error("act(...) is not supported in production builds of React.")}u.Children={map:z,forEach:function(e,t,n){z(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return z(e,(function(){t++})),t},toArray:function(e){return z(e,(function(e){return e}))||[]},only:function(e){if(!L(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},u.Component=x,u.Fragment=f,u.Profiler=p,u.PureComponent=T,u.StrictMode=h,u.Suspense=v,u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=B,u.act=q,u.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=S({},e.props),a=e.key,o=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,i=N.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(l in t)O.call(t,l)&&!R.hasOwnProperty(l)&&(r[l]=void 0===t[l]&&void 0!==s?s[l]:t[l])}var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){s=Array(l);for(var u=0;u<l;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:c,type:e.type,key:a,ref:o,props:r,_owner:i}},u.createContext=function(e){return(e={$$typeof:g,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:m,_context:e},e.Consumer=e},u.createElement=I,u.createFactory=function(e){var t=I.bind(null,e);return t.type=e,t},u.createRef=function(){return{current:null}},u.forwardRef=function(e){return{$$typeof:y,render:e}},u.isValidElement=L,u.lazy=function(e){return{$$typeof:w,_payload:{_status:-1,_result:e},_init:U}},u.memo=function(e,t){return{$$typeof:b,type:e,compare:void 0===t?null:t}},u.startTransition=function(e){var t=F.transition;F.transition={};try{e()}finally{F.transition=t}},u.unstable_act=q,u.useCallback=function(e,t){return M.current.useCallback(e,t)},u.useContext=function(e){return M.current.useContext(e)},u.useDebugValue=function(){},u.useDeferredValue=function(e){return M.current.useDeferredValue(e)},u.useEffect=function(e,t){return M.current.useEffect(e,t)},u.useId=function(){return M.current.useId()},u.useImperativeHandle=function(e,t,n){return M.current.useImperativeHandle(e,t,n)},u.useInsertionEffect=function(e,t){return M.current.useInsertionEffect(e,t)},u.useLayoutEffect=function(e,t){return M.current.useLayoutEffect(e,t)},u.useMemo=function(e,t){return M.current.useMemo(e,t)},u.useReducer=function(e,t,n){return M.current.useReducer(e,t,n)},u.useRef=function(e){return M.current.useRef(e)},u.useState=function(e){return M.current.useState(e)},u.useSyncExternalStore=function(e,t,n){return M.current.useSyncExternalStore(e,t,n)},u.useTransition=function(){return M.current.useTransition()},u.version="18.3.1",l.exports=u;var V=l.exports;const W=a(V),H=n({__proto__:null,default:W},[V]);
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var K=V,J=Symbol.for("react.element"),Q=Symbol.for("react.fragment"),G=Object.prototype.hasOwnProperty,Y=K.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,X={key:!0,ref:!0,__self:!0,__source:!0};function Z(e,t,n){var r,a={},o=null,i=null;for(r in void 0!==n&&(o=""+n),void 0!==t.key&&(o=""+t.key),void 0!==t.ref&&(i=t.ref),t)G.call(t,r)&&!X.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:J,type:e,key:o,ref:i,props:a,_owner:Y.current}}s.Fragment=Q,s.jsx=Z,s.jsxs=Z,i.exports=s;var ee=i.exports,te={},ne={exports:{}},re={},ae={exports:{}},oe={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>a(l,n))u<o&&0>a(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<o&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();e.unstable_now=function(){return i.now()-s}}var l=[],u=[],c=1,d=null,f=3,h=!1,p=!1,m=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function b(e){for(var a=n(u);null!==a;){if(null===a.callback)r(u);else{if(!(a.startTime<=e))break;r(u),a.sortIndex=a.expirationTime,t(l,a)}a=n(u)}}function w(e){if(m=!1,b(e),!p)if(null!==n(l))p=!0,R(_);else{var t=n(u);null!==t&&I(w,t.startTime-e)}}function _(t,a){p=!1,m&&(m=!1,y(x),x=-1),h=!0;var o=f;try{for(b(a),d=n(l);null!==d&&(!(d.expirationTime>a)||t&&!P());){var i=d.callback;if("function"==typeof i){d.callback=null,f=d.priorityLevel;var s=i(d.expirationTime<=a);a=e.unstable_now(),"function"==typeof s?d.callback=s:d===n(l)&&r(l),b(a)}else r(l);d=n(l)}if(null!==d)var c=!0;else{var g=n(u);null!==g&&I(w,g.startTime-a),c=!1}return c}finally{d=null,f=o,h=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,S=!1,E=null,x=-1,j=5,T=-1;function P(){return!(e.unstable_now()-T<j)}function C(){if(null!==E){var t=e.unstable_now();T=t;var n=!0;try{n=E(!0,t)}finally{n?k():(S=!1,E=null)}}else S=!1}if("function"==typeof v)k=function(){v(C)};else if("undefined"!=typeof MessageChannel){var O=new MessageChannel,N=O.port2;O.port1.onmessage=C,k=function(){N.postMessage(null)}}else k=function(){g(C,0)};function R(e){E=e,S||(S=!0,k())}function I(t,n){x=g((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){p||h||(p=!0,R(_))},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<e?Math.floor(1e3/e):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(r,a,o){var i=e.unstable_now();switch("object"==typeof o&&null!==o?o="number"==typeof(o=o.delay)&&0<o?i+o:i:o=i,r){case 1:var s=-1;break;case 2:s=250;break;case 5:s=**********;break;case 4:s=1e4;break;default:s=5e3}return r={id:c++,callback:a,priorityLevel:r,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>i?(r.sortIndex=o,t(u,r),null===n(l)&&r===n(u)&&(m?(y(x),x=-1):m=!0,I(w,o-i))):(r.sortIndex=s,t(l,r),p||h||(p=!0,R(_))),r},e.unstable_shouldYield=P,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(oe),ae.exports=oe;var ie=ae.exports,se=V,le=ie;
/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function ue(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ce=new Set,de={};function fe(e,t){he(e,t),he(e+"Capture",t)}function he(e,t){for(de[e]=t,e=0;e<t.length;e++)ce.add(t[e])}var pe=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),me=Object.prototype.hasOwnProperty,ge=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ye={},ve={};function be(e,t,n,r,a,o,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var we={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){we[e]=new be(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];we[t]=new be(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){we[e]=new be(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){we[e]=new be(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){we[e]=new be(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){we[e]=new be(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){we[e]=new be(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){we[e]=new be(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){we[e]=new be(e,5,!1,e.toLowerCase(),null,!1,!1)}));var _e=/[\-:]([a-z])/g;function ke(e){return e[1].toUpperCase()}function Se(e,t,n,r){var a=we.hasOwnProperty(t)?we[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!me.call(ve,e)||!me.call(ye,e)&&(ge.test(e)?ve[e]=!0:(ye[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(_e,ke);we[t]=new be(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(_e,ke);we[t]=new be(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(_e,ke);we[t]=new be(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){we[e]=new be(e,1,!1,e.toLowerCase(),null,!1,!1)})),we.xlinkHref=new be("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){we[e]=new be(e,1,!1,e.toLowerCase(),null,!0,!0)}));var Ee=se.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,xe=Symbol.for("react.element"),je=Symbol.for("react.portal"),Te=Symbol.for("react.fragment"),Pe=Symbol.for("react.strict_mode"),Ce=Symbol.for("react.profiler"),Oe=Symbol.for("react.provider"),Ne=Symbol.for("react.context"),Re=Symbol.for("react.forward_ref"),Ie=Symbol.for("react.suspense"),Le=Symbol.for("react.suspense_list"),$e=Symbol.for("react.memo"),Ae=Symbol.for("react.lazy"),De=Symbol.for("react.offscreen"),ze=Symbol.iterator;function Ue(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=ze&&e[ze]||e["@@iterator"])?e:null}var Me,Fe=Object.assign;function Be(e){if(void 0===Me)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Me=t&&t[1]||""}return"\n"+Me+e}var qe=!1;function Ve(e,t){if(!e||qe)return"";qe=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"==typeof u.stack){for(var a=u.stack.split("\n"),o=r.stack.split("\n"),i=a.length-1,s=o.length-1;1<=i&&0<=s&&a[i]!==o[s];)s--;for(;1<=i&&0<=s;i--,s--)if(a[i]!==o[s]){if(1!==i||1!==s)do{if(i--,0>--s||a[i]!==o[s]){var l="\n"+a[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=i&&0<=s);break}}}finally{qe=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Be(e):""}function We(e){switch(e.tag){case 5:return Be(e.type);case 16:return Be("Lazy");case 13:return Be("Suspense");case 19:return Be("SuspenseList");case 0:case 2:case 15:return e=Ve(e.type,!1);case 11:return e=Ve(e.type.render,!1);case 1:return e=Ve(e.type,!0);default:return""}}function He(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case Te:return"Fragment";case je:return"Portal";case Ce:return"Profiler";case Pe:return"StrictMode";case Ie:return"Suspense";case Le:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case Ne:return(e.displayName||"Context")+".Consumer";case Oe:return(e._context.displayName||"Context")+".Provider";case Re:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case $e:return null!==(t=e.displayName||null)?t:He(e.type)||"Memo";case Ae:t=e._payload,e=e._init;try{return He(e(t))}catch(n){}}return null}function Ke(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return He(t);case 8:return t===Pe?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function Je(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function Qe(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Ge(e){e._valueTracker||(e._valueTracker=function(e){var t=Qe(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Ye(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Qe(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Xe(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Ze(e,t){var n=t.checked;return Fe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function et(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=Je(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function tt(e,t){null!=(t=t.checked)&&Se(e,"checked",t,!1)}function nt(e,t){tt(e,t);var n=Je(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?at(e,t.type,n):t.hasOwnProperty("defaultValue")&&at(e,t.type,Je(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function rt(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function at(e,t,n){"number"===t&&Xe(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ot=Array.isArray;function it(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Je(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function st(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(ue(91));return Fe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function lt(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(ue(92));if(ot(n)){if(1<n.length)throw Error(ue(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:Je(n)}}function ut(e,t){var n=Je(t.value),r=Je(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ct(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function dt(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ft(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?dt(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ht,pt,mt=(pt=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ht=ht||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ht.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return pt(e,t)}))}:pt);function gt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var yt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},vt=["Webkit","ms","Moz","O"];function bt(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||yt.hasOwnProperty(e)&&yt[e]?(""+t).trim():t+"px"}function wt(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=bt(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(yt).forEach((function(e){vt.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),yt[t]=yt[e]}))}));var _t=Fe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function kt(e,t){if(t){if(_t[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(ue(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(ue(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(ue(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(ue(62))}}function St(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Et=null;function xt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var jt=null,Tt=null,Pt=null;function Ct(e){if(e=Eo(e)){if("function"!=typeof jt)throw Error(ue(280));var t=e.stateNode;t&&(t=jo(t),jt(e.stateNode,e.type,t))}}function Ot(e){Tt?Pt?Pt.push(e):Pt=[e]:Tt=e}function Nt(){if(Tt){var e=Tt,t=Pt;if(Pt=Tt=null,Ct(e),t)for(e=0;e<t.length;e++)Ct(t[e])}}function Rt(e,t){return e(t)}function It(){}var Lt=!1;function $t(e,t,n){if(Lt)return e(t,n);Lt=!0;try{return Rt(e,t,n)}finally{Lt=!1,(null!==Tt||null!==Pt)&&(It(),Nt())}}function At(e,t){var n=e.stateNode;if(null===n)return null;var r=jo(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(ue(231,t,typeof n));return n}var Dt=!1;if(pe)try{var zt={};Object.defineProperty(zt,"passive",{get:function(){Dt=!0}}),window.addEventListener("test",zt,zt),window.removeEventListener("test",zt,zt)}catch(pt){Dt=!1}function Ut(e,t,n,r,a,o,i,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Mt=!1,Ft=null,Bt=!1,qt=null,Vt={onError:function(e){Mt=!0,Ft=e}};function Wt(e,t,n,r,a,o,i,s,l){Mt=!1,Ft=null,Ut.apply(Vt,arguments)}function Ht(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Kt(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Jt(e){if(Ht(e)!==e)throw Error(ue(188))}function Qt(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ht(e)))throw Error(ue(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return Jt(a),e;if(o===r)return Jt(a),t;o=o.sibling}throw Error(ue(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i){for(s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i)throw Error(ue(189))}}if(n.alternate!==r)throw Error(ue(190))}if(3!==n.tag)throw Error(ue(188));return n.stateNode.current===n?e:t}(e))?Gt(e):null}function Gt(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Gt(e);if(null!==t)return t;e=e.sibling}return null}var Yt=le.unstable_scheduleCallback,Xt=le.unstable_cancelCallback,Zt=le.unstable_shouldYield,en=le.unstable_requestPaint,tn=le.unstable_now,nn=le.unstable_getCurrentPriorityLevel,rn=le.unstable_ImmediatePriority,an=le.unstable_UserBlockingPriority,on=le.unstable_NormalPriority,sn=le.unstable_LowPriority,ln=le.unstable_IdlePriority,un=null,cn=null;var dn=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(fn(e)/hn|0)|0},fn=Math.log,hn=Math.LN2;var pn=64,mn=4194304;function gn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function yn(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,i=268435455&n;if(0!==i){var s=i&~a;0!==s?r=gn(s):0!==(o&=i)&&(r=gn(o))}else 0!==(i=n&~a)?r=gn(i):0!==o&&(r=gn(o));if(0===r)return 0;if(0!==t&&t!==r&&!(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&4194240&o))return t;if(4&r&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-dn(t)),r|=e[n],t&=~a;return r}function vn(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function bn(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function wn(){var e=pn;return!(4194240&(pn<<=1))&&(pn=64),e}function _n(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function kn(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-dn(t)]=n}function Sn(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-dn(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var En=0;function xn(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var jn,Tn,Pn,Cn,On,Nn=!1,Rn=[],In=null,Ln=null,$n=null,An=new Map,Dn=new Map,zn=[],Un="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mn(e,t){switch(e){case"focusin":case"focusout":In=null;break;case"dragenter":case"dragleave":Ln=null;break;case"mouseover":case"mouseout":$n=null;break;case"pointerover":case"pointerout":An.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Dn.delete(t.pointerId)}}function Fn(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=Eo(t))&&Tn(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Bn(e){var t=So(e.target);if(null!==t){var n=Ht(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Kt(n)))return e.blockedOn=t,void On(e.priority,(function(){Pn(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function qn(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=er(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=Eo(n))&&Tn(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Et=r,n.target.dispatchEvent(r),Et=null,t.shift()}return!0}function Vn(e,t,n){qn(e)&&n.delete(t)}function Wn(){Nn=!1,null!==In&&qn(In)&&(In=null),null!==Ln&&qn(Ln)&&(Ln=null),null!==$n&&qn($n)&&($n=null),An.forEach(Vn),Dn.forEach(Vn)}function Hn(e,t){e.blockedOn===t&&(e.blockedOn=null,Nn||(Nn=!0,le.unstable_scheduleCallback(le.unstable_NormalPriority,Wn)))}function Kn(e){function t(t){return Hn(t,e)}if(0<Rn.length){Hn(Rn[0],e);for(var n=1;n<Rn.length;n++){var r=Rn[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==In&&Hn(In,e),null!==Ln&&Hn(Ln,e),null!==$n&&Hn($n,e),An.forEach(t),Dn.forEach(t),n=0;n<zn.length;n++)(r=zn[n]).blockedOn===e&&(r.blockedOn=null);for(;0<zn.length&&null===(n=zn[0]).blockedOn;)Bn(n),null===n.blockedOn&&zn.shift()}var Jn=Ee.ReactCurrentBatchConfig,Qn=!0;function Gn(e,t,n,r){var a=En,o=Jn.transition;Jn.transition=null;try{En=1,Xn(e,t,n,r)}finally{En=a,Jn.transition=o}}function Yn(e,t,n,r){var a=En,o=Jn.transition;Jn.transition=null;try{En=4,Xn(e,t,n,r)}finally{En=a,Jn.transition=o}}function Xn(e,t,n,r){if(Qn){var a=er(e,t,n,r);if(null===a)Ja(e,t,r,Zn,n),Mn(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return In=Fn(In,e,t,n,r,a),!0;case"dragenter":return Ln=Fn(Ln,e,t,n,r,a),!0;case"mouseover":return $n=Fn($n,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return An.set(o,Fn(An.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,Dn.set(o,Fn(Dn.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Mn(e,r),4&t&&-1<Un.indexOf(e)){for(;null!==a;){var o=Eo(a);if(null!==o&&jn(o),null===(o=er(e,t,n,r))&&Ja(e,t,r,Zn,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Ja(e,t,r,null,n)}}var Zn=null;function er(e,t,n,r){if(Zn=null,null!==(e=So(e=xt(r))))if(null===(t=Ht(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Kt(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Zn=e,null}function tr(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(nn()){case rn:return 1;case an:return 4;case on:case sn:return 16;case ln:return 536870912;default:return 16}default:return 16}}var nr=null,rr=null,ar=null;function or(){if(ar)return ar;var e,t,n=rr,r=n.length,a="value"in nr?nr.value:nr.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return ar=a.slice(e,1<t?1-t:void 0)}function ir(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function sr(){return!0}function lr(){return!1}function ur(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?sr:lr,this.isPropagationStopped=lr,this}return Fe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=sr)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=sr)},persist:function(){},isPersistent:sr}),t}var cr,dr,fr,hr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pr=ur(hr),mr=Fe({},hr,{view:0,detail:0}),gr=ur(mr),yr=Fe({},mr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cr,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fr&&(fr&&"mousemove"===e.type?(cr=e.screenX-fr.screenX,dr=e.screenY-fr.screenY):dr=cr=0,fr=e),cr)},movementY:function(e){return"movementY"in e?e.movementY:dr}}),vr=ur(yr),br=ur(Fe({},yr,{dataTransfer:0})),wr=ur(Fe({},mr,{relatedTarget:0})),_r=ur(Fe({},hr,{animationName:0,elapsedTime:0,pseudoElement:0})),kr=Fe({},hr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Sr=ur(kr),Er=ur(Fe({},hr,{data:0})),xr={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jr={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Tr={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Pr(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Tr[e])&&!!t[e]}function Cr(){return Pr}var Or=Fe({},mr,{key:function(e){if(e.key){var t=xr[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=ir(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?jr[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cr,charCode:function(e){return"keypress"===e.type?ir(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?ir(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Nr=ur(Or),Rr=ur(Fe({},yr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Ir=ur(Fe({},mr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cr})),Lr=ur(Fe({},hr,{propertyName:0,elapsedTime:0,pseudoElement:0})),$r=Fe({},yr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ar=ur($r),Dr=[9,13,27,32],zr=pe&&"CompositionEvent"in window,Ur=null;pe&&"documentMode"in document&&(Ur=document.documentMode);var Mr=pe&&"TextEvent"in window&&!Ur,Fr=pe&&(!zr||Ur&&8<Ur&&11>=Ur),Br=String.fromCharCode(32),qr=!1;function Vr(e,t){switch(e){case"keyup":return-1!==Dr.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wr(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Hr=!1;var Kr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Jr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Kr[e.type]:"textarea"===t}function Qr(e,t,n,r){Ot(r),0<(t=Ga(t,"onChange")).length&&(n=new pr("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Gr=null,Yr=null;function Xr(e){Ba(e,0)}function Zr(e){if(Ye(xo(e)))return e}function ea(e,t){if("change"===e)return t}var ta=!1;if(pe){var na;if(pe){var ra="oninput"in document;if(!ra){var aa=document.createElement("div");aa.setAttribute("oninput","return;"),ra="function"==typeof aa.oninput}na=ra}else na=!1;ta=na&&(!document.documentMode||9<document.documentMode)}function oa(){Gr&&(Gr.detachEvent("onpropertychange",ia),Yr=Gr=null)}function ia(e){if("value"===e.propertyName&&Zr(Yr)){var t=[];Qr(t,Yr,e,xt(e)),$t(Xr,t)}}function sa(e,t,n){"focusin"===e?(oa(),Yr=n,(Gr=t).attachEvent("onpropertychange",ia)):"focusout"===e&&oa()}function la(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Zr(Yr)}function ua(e,t){if("click"===e)return Zr(t)}function ca(e,t){if("input"===e||"change"===e)return Zr(t)}var da="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function fa(e,t){if(da(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!me.call(t,a)||!da(e[a],t[a]))return!1}return!0}function ha(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function pa(e,t){var n,r=ha(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ha(r)}}function ma(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?ma(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function ga(){for(var e=window,t=Xe();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Xe((e=t.contentWindow).document)}return t}function ya(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function va(e){var t=ga(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ma(n.ownerDocument.documentElement,n)){if(null!==r&&ya(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=pa(n,o);var i=pa(n,r);a&&i&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var ba=pe&&"documentMode"in document&&11>=document.documentMode,wa=null,_a=null,ka=null,Sa=!1;function Ea(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;Sa||null==wa||wa!==Xe(r)||("selectionStart"in(r=wa)&&ya(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ka&&fa(ka,r)||(ka=r,0<(r=Ga(_a,"onSelect")).length&&(t=new pr("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=wa)))}function xa(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ja={animationend:xa("Animation","AnimationEnd"),animationiteration:xa("Animation","AnimationIteration"),animationstart:xa("Animation","AnimationStart"),transitionend:xa("Transition","TransitionEnd")},Ta={},Pa={};function Ca(e){if(Ta[e])return Ta[e];if(!ja[e])return e;var t,n=ja[e];for(t in n)if(n.hasOwnProperty(t)&&t in Pa)return Ta[e]=n[t];return e}pe&&(Pa=document.createElement("div").style,"AnimationEvent"in window||(delete ja.animationend.animation,delete ja.animationiteration.animation,delete ja.animationstart.animation),"TransitionEvent"in window||delete ja.transitionend.transition);var Oa=Ca("animationend"),Na=Ca("animationiteration"),Ra=Ca("animationstart"),Ia=Ca("transitionend"),La=new Map,$a="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Aa(e,t){La.set(e,t),fe(t,[e])}for(var Da=0;Da<$a.length;Da++){var za=$a[Da];Aa(za.toLowerCase(),"on"+(za[0].toUpperCase()+za.slice(1)))}Aa(Oa,"onAnimationEnd"),Aa(Na,"onAnimationIteration"),Aa(Ra,"onAnimationStart"),Aa("dblclick","onDoubleClick"),Aa("focusin","onFocus"),Aa("focusout","onBlur"),Aa(Ia,"onTransitionEnd"),he("onMouseEnter",["mouseout","mouseover"]),he("onMouseLeave",["mouseout","mouseover"]),he("onPointerEnter",["pointerout","pointerover"]),he("onPointerLeave",["pointerout","pointerover"]),fe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),fe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),fe("onBeforeInput",["compositionend","keypress","textInput","paste"]),fe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),fe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),fe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ua="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ma=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ua));function Fa(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,i,s,l){if(Wt.apply(this,arguments),Mt){if(!Mt)throw Error(ue(198));var u=Ft;Mt=!1,Ft=null,Bt||(Bt=!0,qt=u)}}(r,t,void 0,e),e.currentTarget=null}function Ba(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&a.isPropagationStopped())break e;Fa(a,s,u),o=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,u=s.currentTarget,s=s.listener,l!==o&&a.isPropagationStopped())break e;Fa(a,s,u),o=l}}}if(Bt)throw e=qt,Bt=!1,qt=null,e}function qa(e,t){var n=t[wo];void 0===n&&(n=t[wo]=new Set);var r=e+"__bubble";n.has(r)||(Ka(t,e,2,!1),n.add(r))}function Va(e,t,n){var r=0;t&&(r|=4),Ka(n,e,r,t)}var Wa="_reactListening"+Math.random().toString(36).slice(2);function Ha(e){if(!e[Wa]){e[Wa]=!0,ce.forEach((function(t){"selectionchange"!==t&&(Ma.has(t)||Va(t,!1,e),Va(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Wa]||(t[Wa]=!0,Va("selectionchange",!1,t))}}function Ka(e,t,n,r){switch(tr(t)){case 1:var a=Gn;break;case 4:a=Yn;break;default:a=Xn}n=a.bind(null,t,n,e),a=void 0,!Dt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Ja(e,t,n,r,a){var o=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===a||8===s.nodeType&&s.parentNode===a)break;if(4===i)for(i=r.return;null!==i;){var l=i.tag;if((3===l||4===l)&&((l=i.stateNode.containerInfo)===a||8===l.nodeType&&l.parentNode===a))return;i=i.return}for(;null!==s;){if(null===(i=So(s)))return;if(5===(l=i.tag)||6===l){r=o=i;continue e}s=s.parentNode}}r=r.return}$t((function(){var r=o,a=xt(n),i=[];e:{var s=La.get(e);if(void 0!==s){var l=pr,u=e;switch(e){case"keypress":if(0===ir(n))break e;case"keydown":case"keyup":l=Nr;break;case"focusin":u="focus",l=wr;break;case"focusout":u="blur",l=wr;break;case"beforeblur":case"afterblur":l=wr;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=vr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=br;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Ir;break;case Oa:case Na:case Ra:l=_r;break;case Ia:l=Lr;break;case"scroll":l=gr;break;case"wheel":l=Ar;break;case"copy":case"cut":case"paste":l=Sr;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Rr}var c=!!(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=At(p,f))&&c.push(Qa(p,m,h)))),d)break;p=p.return}0<c.length&&(s=new l(s,u,null,n,a),i.push({event:s,listeners:c}))}}if(!(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===Et||!(u=n.relatedTarget||n.fromElement)||!So(u)&&!u[bo])&&(l||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?So(u):null)&&(u!==(d=Ht(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=vr,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=Rr,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?s:xo(l),h=null==u?s:xo(u),(s=new c(m,p+"leave",l,n,a)).target=d,s.relatedTarget=h,m=null,So(a)===r&&((c=new c(f,p+"enter",u,n,a)).target=h,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,p=0,h=c=l;h;h=Ya(h))p++;for(h=0,m=f;m;m=Ya(m))h++;for(;0<p-h;)c=Ya(c),p--;for(;0<h-p;)f=Ya(f),h--;for(;p--;){if(c===f||null!==f&&c===f.alternate)break e;c=Ya(c),f=Ya(f)}c=null}else c=null;null!==l&&Xa(i,s,l,c,!1),null!==u&&null!==d&&Xa(i,d,u,c,!0)}if("select"===(l=(s=r?xo(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=ea;else if(Jr(s))if(ta)g=ca;else{g=la;var y=sa}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=ua);switch(g&&(g=g(e,r))?Qr(i,g,n,a):(y&&y(e,s,r),"focusout"===e&&(y=s._wrapperState)&&y.controlled&&"number"===s.type&&at(s,"number",s.value)),y=r?xo(r):window,e){case"focusin":(Jr(y)||"true"===y.contentEditable)&&(wa=y,_a=r,ka=null);break;case"focusout":ka=_a=wa=null;break;case"mousedown":Sa=!0;break;case"contextmenu":case"mouseup":case"dragend":Sa=!1,Ea(i,n,a);break;case"selectionchange":if(ba)break;case"keydown":case"keyup":Ea(i,n,a)}var v;if(zr)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Hr?Vr(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fr&&"ko"!==n.locale&&(Hr||"onCompositionStart"!==b?"onCompositionEnd"===b&&Hr&&(v=or()):(rr="value"in(nr=a)?nr.value:nr.textContent,Hr=!0)),0<(y=Ga(r,b)).length&&(b=new Er(b,e,null,n,a),i.push({event:b,listeners:y}),v?b.data=v:null!==(v=Wr(n))&&(b.data=v))),(v=Mr?function(e,t){switch(e){case"compositionend":return Wr(t);case"keypress":return 32!==t.which?null:(qr=!0,Br);case"textInput":return(e=t.data)===Br&&qr?null:e;default:return null}}(e,n):function(e,t){if(Hr)return"compositionend"===e||!zr&&Vr(e,t)?(e=or(),ar=rr=nr=null,Hr=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fr&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Ga(r,"onBeforeInput")).length&&(a=new Er("onBeforeInput","beforeinput",null,n,a),i.push({event:a,listeners:r}),a.data=v))}Ba(i,t)}))}function Qa(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ga(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=At(e,n))&&r.unshift(Qa(e,o,a)),null!=(o=At(e,t))&&r.push(Qa(e,o,a))),e=e.return}return r}function Ya(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Xa(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,a?null!=(l=At(n,o))&&i.unshift(Qa(n,l,s)):a||null!=(l=At(n,o))&&i.push(Qa(n,l,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Za=/\r\n?/g,eo=/\u0000|\uFFFD/g;function to(e){return("string"==typeof e?e:""+e).replace(Za,"\n").replace(eo,"")}function no(e,t,n){if(t=to(t),to(e)!==t&&n)throw Error(ue(425))}function ro(){}var ao=null,oo=null;function io(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var so="function"==typeof setTimeout?setTimeout:void 0,lo="function"==typeof clearTimeout?clearTimeout:void 0,uo="function"==typeof Promise?Promise:void 0,co="function"==typeof queueMicrotask?queueMicrotask:void 0!==uo?function(e){return uo.resolve(null).then(e).catch(fo)}:so;function fo(e){setTimeout((function(){throw e}))}function ho(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Kn(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Kn(t)}function po(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function mo(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var go=Math.random().toString(36).slice(2),yo="__reactFiber$"+go,vo="__reactProps$"+go,bo="__reactContainer$"+go,wo="__reactEvents$"+go,_o="__reactListeners$"+go,ko="__reactHandles$"+go;function So(e){var t=e[yo];if(t)return t;for(var n=e.parentNode;n;){if(t=n[bo]||n[yo]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=mo(e);null!==e;){if(n=e[yo])return n;e=mo(e)}return t}n=(e=n).parentNode}return null}function Eo(e){return!(e=e[yo]||e[bo])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xo(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(ue(33))}function jo(e){return e[vo]||null}var To=[],Po=-1;function Co(e){return{current:e}}function Oo(e){0>Po||(e.current=To[Po],To[Po]=null,Po--)}function No(e,t){Po++,To[Po]=e.current,e.current=t}var Ro={},Io=Co(Ro),Lo=Co(!1),$o=Ro;function Ao(e,t){var n=e.type.contextTypes;if(!n)return Ro;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Do(e){return null!=(e=e.childContextTypes)}function zo(){Oo(Lo),Oo(Io)}function Uo(e,t,n){if(Io.current!==Ro)throw Error(ue(168));No(Io,t),No(Lo,n)}function Mo(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(ue(108,Ke(e)||"Unknown",a));return Fe({},n,r)}function Fo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ro,$o=Io.current,No(Io,e),No(Lo,Lo.current),!0}function Bo(e,t,n){var r=e.stateNode;if(!r)throw Error(ue(169));n?(e=Mo(e,t,$o),r.__reactInternalMemoizedMergedChildContext=e,Oo(Lo),Oo(Io),No(Io,e)):Oo(Lo),No(Lo,n)}var qo=null,Vo=!1,Wo=!1;function Ho(e){null===qo?qo=[e]:qo.push(e)}function Ko(){if(!Wo&&null!==qo){Wo=!0;var e=0,t=En;try{var n=qo;for(En=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}qo=null,Vo=!1}catch(a){throw null!==qo&&(qo=qo.slice(e+1)),Yt(rn,Ko),a}finally{En=t,Wo=!1}}return null}var Jo=[],Qo=0,Go=null,Yo=0,Xo=[],Zo=0,ei=null,ti=1,ni="";function ri(e,t){Jo[Qo++]=Yo,Jo[Qo++]=Go,Go=e,Yo=t}function ai(e,t,n){Xo[Zo++]=ti,Xo[Zo++]=ni,Xo[Zo++]=ei,ei=e;var r=ti;e=ni;var a=32-dn(r)-1;r&=~(1<<a),n+=1;var o=32-dn(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,ti=1<<32-dn(t)+a|n<<a|r,ni=o+e}else ti=1<<o|n<<a|r,ni=e}function oi(e){null!==e.return&&(ri(e,1),ai(e,1,0))}function ii(e){for(;e===Go;)Go=Jo[--Qo],Jo[Qo]=null,Yo=Jo[--Qo],Jo[Qo]=null;for(;e===ei;)ei=Xo[--Zo],Xo[Zo]=null,ni=Xo[--Zo],Xo[Zo]=null,ti=Xo[--Zo],Xo[Zo]=null}var si=null,li=null,ui=!1,ci=null;function di(e,t){var n=$c(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function fi(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,si=e,li=po(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,si=e,li=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==ei?{id:ti,overflow:ni}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=$c(18,null,null,0)).stateNode=t,n.return=e,e.child=n,si=e,li=null,!0);default:return!1}}function hi(e){return!(!(1&e.mode)||128&e.flags)}function pi(e){if(ui){var t=li;if(t){var n=t;if(!fi(e,t)){if(hi(e))throw Error(ue(418));t=po(n.nextSibling);var r=si;t&&fi(e,t)?di(r,n):(e.flags=-4097&e.flags|2,ui=!1,si=e)}}else{if(hi(e))throw Error(ue(418));e.flags=-4097&e.flags|2,ui=!1,si=e}}}function mi(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;si=e}function gi(e){if(e!==si)return!1;if(!ui)return mi(e),ui=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!io(e.type,e.memoizedProps)),t&&(t=li)){if(hi(e))throw yi(),Error(ue(418));for(;t;)di(e,t),t=po(t.nextSibling)}if(mi(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(ue(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){li=po(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}li=null}}else li=si?po(e.stateNode.nextSibling):null;return!0}function yi(){for(var e=li;e;)e=po(e.nextSibling)}function vi(){li=si=null,ui=!1}function bi(e){null===ci?ci=[e]:ci.push(e)}var wi=Ee.ReactCurrentBatchConfig;function _i(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(ue(309));var r=n.stateNode}if(!r)throw Error(ue(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===o?t.ref:((t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e})._stringRef=o,t)}if("string"!=typeof e)throw Error(ue(284));if(!n._owner)throw Error(ue(290,e))}return e}function ki(e,t){throw e=Object.prototype.toString.call(t),Error(ue(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Si(e){return(0,e._init)(e._payload)}function Ei(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Dc(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Fc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function l(e,t,n,r){var o=n.type;return o===Te?c(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"==typeof o&&null!==o&&o.$$typeof===Ae&&Si(o)===t.type)?((r=a(t,n.props)).ref=_i(e,t,n),r.return=e,r):((r=zc(n.type,n.key,n.props,null,e.mode,r)).ref=_i(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Bc(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function c(e,t,n,r,o){return null===t||7!==t.tag?((t=Uc(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Fc(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case xe:return(n=zc(t.type,t.key,t.props,null,e.mode,n)).ref=_i(e,null,t),n.return=e,n;case je:return(t=Bc(t,e.mode,n)).return=e,t;case Ae:return d(e,(0,t._init)(t._payload),n)}if(ot(t)||Ue(t))return(t=Uc(t,e.mode,n,null)).return=e,t;ki(e,t)}return null}function f(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==a?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case xe:return n.key===a?l(e,t,n,r):null;case je:return n.key===a?u(e,t,n,r):null;case Ae:return f(e,t,(a=n._init)(n._payload),r)}if(ot(n)||Ue(n))return null!==a?null:c(e,t,n,r,null);ki(e,n)}return null}function h(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case xe:return l(t,e=e.get(null===r.key?n:r.key)||null,r,a);case je:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case Ae:return h(e,t,n,(0,r._init)(r._payload),a)}if(ot(r)||Ue(r))return c(t,e=e.get(n)||null,r,a,null);ki(t,r)}return null}return function s(l,u,c,p){if("object"==typeof c&&null!==c&&c.type===Te&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case xe:e:{for(var m=c.key,g=u;null!==g;){if(g.key===m){if((m=c.type)===Te){if(7===g.tag){n(l,g.sibling),(u=a(g,c.props.children)).return=l,l=u;break e}}else if(g.elementType===m||"object"==typeof m&&null!==m&&m.$$typeof===Ae&&Si(m)===g.type){n(l,g.sibling),(u=a(g,c.props)).ref=_i(l,g,c),u.return=l,l=u;break e}n(l,g);break}t(l,g),g=g.sibling}c.type===Te?((u=Uc(c.props.children,l.mode,p,c.key)).return=l,l=u):((p=zc(c.type,c.key,c.props,null,l.mode,p)).ref=_i(l,u,c),p.return=l,l=p)}return i(l);case je:e:{for(g=c.key;null!==u;){if(u.key===g){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(l,u.sibling),(u=a(u,c.children||[])).return=l,l=u;break e}n(l,u);break}t(l,u),u=u.sibling}(u=Bc(c,l.mode,p)).return=l,l=u}return i(l);case Ae:return s(l,u,(g=c._init)(c._payload),p)}if(ot(c))return function(a,i,s,l){for(var u=null,c=null,p=i,m=i=0,g=null;null!==p&&m<s.length;m++){p.index>m?(g=p,p=null):g=p.sibling;var y=f(a,p,s[m],l);if(null===y){null===p&&(p=g);break}e&&p&&null===y.alternate&&t(a,p),i=o(y,i,m),null===c?u=y:c.sibling=y,c=y,p=g}if(m===s.length)return n(a,p),ui&&ri(a,m),u;if(null===p){for(;m<s.length;m++)null!==(p=d(a,s[m],l))&&(i=o(p,i,m),null===c?u=p:c.sibling=p,c=p);return ui&&ri(a,m),u}for(p=r(a,p);m<s.length;m++)null!==(g=h(p,a,m,s[m],l))&&(e&&null!==g.alternate&&p.delete(null===g.key?m:g.key),i=o(g,i,m),null===c?u=g:c.sibling=g,c=g);return e&&p.forEach((function(e){return t(a,e)})),ui&&ri(a,m),u}(l,u,c,p);if(Ue(c))return function(a,i,s,l){var u=Ue(s);if("function"!=typeof u)throw Error(ue(150));if(null==(s=u.call(s)))throw Error(ue(151));for(var c=u=null,p=i,m=i=0,g=null,y=s.next();null!==p&&!y.done;m++,y=s.next()){p.index>m?(g=p,p=null):g=p.sibling;var v=f(a,p,y.value,l);if(null===v){null===p&&(p=g);break}e&&p&&null===v.alternate&&t(a,p),i=o(v,i,m),null===c?u=v:c.sibling=v,c=v,p=g}if(y.done)return n(a,p),ui&&ri(a,m),u;if(null===p){for(;!y.done;m++,y=s.next())null!==(y=d(a,y.value,l))&&(i=o(y,i,m),null===c?u=y:c.sibling=y,c=y);return ui&&ri(a,m),u}for(p=r(a,p);!y.done;m++,y=s.next())null!==(y=h(p,a,m,y.value,l))&&(e&&null!==y.alternate&&p.delete(null===y.key?m:y.key),i=o(y,i,m),null===c?u=y:c.sibling=y,c=y);return e&&p.forEach((function(e){return t(a,e)})),ui&&ri(a,m),u}(l,u,c,p);ki(l,c)}return"string"==typeof c&&""!==c||"number"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(l,u.sibling),(u=a(u,c)).return=l,l=u):(n(l,u),(u=Fc(c,l.mode,p)).return=l,l=u),i(l)):n(l,u)}}var xi=Ei(!0),ji=Ei(!1),Ti=Co(null),Pi=null,Ci=null,Oi=null;function Ni(){Oi=Ci=Pi=null}function Ri(e){var t=Ti.current;Oo(Ti),e._currentValue=t}function Ii(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Li(e,t){Pi=e,Oi=Ci=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(!!(e.lanes&t)&&(Sl=!0),e.firstContext=null)}function $i(e){var t=e._currentValue;if(Oi!==e)if(e={context:e,memoizedValue:t,next:null},null===Ci){if(null===Pi)throw Error(ue(308));Ci=e,Pi.dependencies={lanes:0,firstContext:e}}else Ci=Ci.next=e;return t}var Ai=null;function Di(e){null===Ai?Ai=[e]:Ai.push(e)}function zi(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Di(t)):(n.next=a.next,a.next=n),t.interleaved=n,Ui(e,r)}function Ui(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Mi=!1;function Fi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Bi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function qi(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Vi(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&Ru){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Ui(e,n)}return null===(a=r.interleaved)?(t.next=t,Di(r)):(t.next=a.next,a.next=t),r.interleaved=t,Ui(e,n)}function Wi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Sn(e,n)}}function Hi(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ki(e,t,n,r){var a=e.updateQueue;Mi=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,u=l.next;l.next=null,null===i?o=u:i.next=u,i=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==o){var d=a.baseState;for(i=0,c=u=l=null,s=o;;){var f=s.lane,h=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(f=t,h=n,m.tag){case 1:if("function"==typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null==(f="function"==typeof(p=m.payload)?p.call(h,d,f):p))break e;d=Fe({},d,f);break e;case 2:Mi=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=h,l=d):c=c.next=h,i|=f;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(f=s).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(l=d),a.baseState=l,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{i|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);Mu|=i,e.lanes=i,e.memoizedState=d}}function Ji(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!=typeof a)throw Error(ue(191,a));a.call(r)}}}var Qi={},Gi=Co(Qi),Yi=Co(Qi),Xi=Co(Qi);function Zi(e){if(e===Qi)throw Error(ue(174));return e}function es(e,t){switch(No(Xi,t),No(Yi,e),No(Gi,Qi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ft(null,"");break;default:t=ft(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Oo(Gi),No(Gi,t)}function ts(){Oo(Gi),Oo(Yi),Oo(Xi)}function ns(e){Zi(Xi.current);var t=Zi(Gi.current),n=ft(t,e.type);t!==n&&(No(Yi,e),No(Gi,n))}function rs(e){Yi.current===e&&(Oo(Gi),Oo(Yi))}var as=Co(0);function os(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var is=[];function ss(){for(var e=0;e<is.length;e++)is[e]._workInProgressVersionPrimary=null;is.length=0}var ls=Ee.ReactCurrentDispatcher,us=Ee.ReactCurrentBatchConfig,cs=0,ds=null,fs=null,hs=null,ps=!1,ms=!1,gs=0,ys=0;function vs(){throw Error(ue(321))}function bs(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!da(e[n],t[n]))return!1;return!0}function ws(e,t,n,r,a,o){if(cs=o,ds=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ls.current=null===e||null===e.memoizedState?rl:al,e=n(r,a),ms){o=0;do{if(ms=!1,gs=0,25<=o)throw Error(ue(301));o+=1,hs=fs=null,t.updateQueue=null,ls.current=ol,e=n(r,a)}while(ms)}if(ls.current=nl,t=null!==fs&&null!==fs.next,cs=0,hs=fs=ds=null,ps=!1,t)throw Error(ue(300));return e}function _s(){var e=0!==gs;return gs=0,e}function ks(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===hs?ds.memoizedState=hs=e:hs=hs.next=e,hs}function Ss(){if(null===fs){var e=ds.alternate;e=null!==e?e.memoizedState:null}else e=fs.next;var t=null===hs?ds.memoizedState:hs.next;if(null!==t)hs=t,fs=e;else{if(null===e)throw Error(ue(310));e={memoizedState:(fs=e).memoizedState,baseState:fs.baseState,baseQueue:fs.baseQueue,queue:fs.queue,next:null},null===hs?ds.memoizedState=hs=e:hs=hs.next=e}return hs}function Es(e,t){return"function"==typeof t?t(e):t}function xs(e){var t=Ss(),n=t.queue;if(null===n)throw Error(ue(311));n.lastRenderedReducer=e;var r=fs,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var s=i=null,l=null,u=o;do{var c=u.lane;if((cs&c)===c)null!==l&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===l?(s=l=d,i=r):l=l.next=d,ds.lanes|=c,Mu|=c}u=u.next}while(null!==u&&u!==o);null===l?i=r:l.next=s,da(r,t.memoizedState)||(Sl=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=l,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,ds.lanes|=o,Mu|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function js(e){var t=Ss(),n=t.queue;if(null===n)throw Error(ue(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);da(o,t.memoizedState)||(Sl=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ts(){}function Ps(e,t){var n=ds,r=Ss(),a=t(),o=!da(r.memoizedState,a);if(o&&(r.memoizedState=a,Sl=!0),r=r.queue,Ms(Ns.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==hs&&1&hs.memoizedState.tag){if(n.flags|=2048,$s(9,Os.bind(null,n,r,a,t),void 0,null),null===Iu)throw Error(ue(349));30&cs||Cs(n,t,a)}return a}function Cs(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ds.updateQueue)?(t={lastEffect:null,stores:null},ds.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Os(e,t,n,r){t.value=n,t.getSnapshot=r,Rs(t)&&Is(e)}function Ns(e,t,n){return n((function(){Rs(t)&&Is(e)}))}function Rs(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!da(e,n)}catch(r){return!0}}function Is(e){var t=Ui(e,1);null!==t&&ic(t,e,1,-1)}function Ls(e){var t=ks();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Es,lastRenderedState:e},t.queue=e,e=e.dispatch=Xs.bind(null,ds,e),[t.memoizedState,e]}function $s(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ds.updateQueue)?(t={lastEffect:null,stores:null},ds.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function As(){return Ss().memoizedState}function Ds(e,t,n,r){var a=ks();ds.flags|=e,a.memoizedState=$s(1|t,n,void 0,void 0===r?null:r)}function zs(e,t,n,r){var a=Ss();r=void 0===r?null:r;var o=void 0;if(null!==fs){var i=fs.memoizedState;if(o=i.destroy,null!==r&&bs(r,i.deps))return void(a.memoizedState=$s(t,n,o,r))}ds.flags|=e,a.memoizedState=$s(1|t,n,o,r)}function Us(e,t){return Ds(8390656,8,e,t)}function Ms(e,t){return zs(2048,8,e,t)}function Fs(e,t){return zs(4,2,e,t)}function Bs(e,t){return zs(4,4,e,t)}function qs(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Vs(e,t,n){return n=null!=n?n.concat([e]):null,zs(4,4,qs.bind(null,t,e),n)}function Ws(){}function Hs(e,t){var n=Ss();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&bs(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ks(e,t){var n=Ss();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&bs(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Js(e,t,n){return 21&cs?(da(n,t)||(n=wn(),ds.lanes|=n,Mu|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Sl=!0),e.memoizedState=n)}function Qs(e,t){var n=En;En=0!==n&&4>n?n:4,e(!0);var r=us.transition;us.transition={};try{e(!1),t()}finally{En=n,us.transition=r}}function Gs(){return Ss().memoizedState}function Ys(e,t,n){var r=oc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Zs(e))el(t,n);else if(null!==(n=zi(e,t,n,r))){ic(n,e,r,ac()),tl(n,t,r)}}function Xs(e,t,n){var r=oc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Zs(e))el(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=o(i,n);if(a.hasEagerState=!0,a.eagerState=s,da(s,i)){var l=t.interleaved;return null===l?(a.next=a,Di(t)):(a.next=l.next,l.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=zi(e,t,a,r))&&(ic(n,e,r,a=ac()),tl(n,t,r))}}function Zs(e){var t=e.alternate;return e===ds||null!==t&&t===ds}function el(e,t){ms=ps=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function tl(e,t,n){if(4194240&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Sn(e,n)}}var nl={readContext:$i,useCallback:vs,useContext:vs,useEffect:vs,useImperativeHandle:vs,useInsertionEffect:vs,useLayoutEffect:vs,useMemo:vs,useReducer:vs,useRef:vs,useState:vs,useDebugValue:vs,useDeferredValue:vs,useTransition:vs,useMutableSource:vs,useSyncExternalStore:vs,useId:vs,unstable_isNewReconciler:!1},rl={readContext:$i,useCallback:function(e,t){return ks().memoizedState=[e,void 0===t?null:t],e},useContext:$i,useEffect:Us,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Ds(4194308,4,qs.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ds(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ds(4,2,e,t)},useMemo:function(e,t){var n=ks();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ks();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ys.bind(null,ds,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ks().memoizedState=e},useState:Ls,useDebugValue:Ws,useDeferredValue:function(e){return ks().memoizedState=e},useTransition:function(){var e=Ls(!1),t=e[0];return e=Qs.bind(null,e[1]),ks().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ds,a=ks();if(ui){if(void 0===n)throw Error(ue(407));n=n()}else{if(n=t(),null===Iu)throw Error(ue(349));30&cs||Cs(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Us(Ns.bind(null,r,o,e),[e]),r.flags|=2048,$s(9,Os.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=ks(),t=Iu.identifierPrefix;if(ui){var n=ni;t=":"+t+"R"+(n=(ti&~(1<<32-dn(ti)-1)).toString(32)+n),0<(n=gs++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ys++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},al={readContext:$i,useCallback:Hs,useContext:$i,useEffect:Ms,useImperativeHandle:Vs,useInsertionEffect:Fs,useLayoutEffect:Bs,useMemo:Ks,useReducer:xs,useRef:As,useState:function(){return xs(Es)},useDebugValue:Ws,useDeferredValue:function(e){return Js(Ss(),fs.memoizedState,e)},useTransition:function(){return[xs(Es)[0],Ss().memoizedState]},useMutableSource:Ts,useSyncExternalStore:Ps,useId:Gs,unstable_isNewReconciler:!1},ol={readContext:$i,useCallback:Hs,useContext:$i,useEffect:Ms,useImperativeHandle:Vs,useInsertionEffect:Fs,useLayoutEffect:Bs,useMemo:Ks,useReducer:js,useRef:As,useState:function(){return js(Es)},useDebugValue:Ws,useDeferredValue:function(e){var t=Ss();return null===fs?t.memoizedState=e:Js(t,fs.memoizedState,e)},useTransition:function(){return[js(Es)[0],Ss().memoizedState]},useMutableSource:Ts,useSyncExternalStore:Ps,useId:Gs,unstable_isNewReconciler:!1};function il(e,t){if(e&&e.defaultProps){for(var n in t=Fe({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function sl(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:Fe({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ll={isMounted:function(e){return!!(e=e._reactInternals)&&Ht(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ac(),a=oc(e),o=qi(r,a);o.payload=t,null!=n&&(o.callback=n),null!==(t=Vi(e,o,a))&&(ic(t,e,a,r),Wi(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ac(),a=oc(e),o=qi(r,a);o.tag=1,o.payload=t,null!=n&&(o.callback=n),null!==(t=Vi(e,o,a))&&(ic(t,e,a,r),Wi(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ac(),r=oc(e),a=qi(n,r);a.tag=2,null!=t&&(a.callback=t),null!==(t=Vi(e,a,r))&&(ic(t,e,r,n),Wi(t,e,r))}};function ul(e,t,n,r,a,o,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!fa(n,r)||!fa(a,o))}function cl(e,t,n){var r=!1,a=Ro,o=t.contextType;return"object"==typeof o&&null!==o?o=$i(o):(a=Do(t)?$o:Io.current,o=(r=null!=(r=t.contextTypes))?Ao(e,a):Ro),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ll,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function dl(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ll.enqueueReplaceState(t,t.state,null)}function fl(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Fi(e);var o=t.contextType;"object"==typeof o&&null!==o?a.context=$i(o):(o=Do(t)?$o:Io.current,a.context=Ao(e,o)),a.state=e.memoizedState,"function"==typeof(o=t.getDerivedStateFromProps)&&(sl(e,t,o,n),a.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(t=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ll.enqueueReplaceState(a,a.state,null),Ki(e,n,a,r),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4194308)}function hl(e,t){try{var n="",r=t;do{n+=We(r),r=r.return}while(r);var a=n}catch(o){a="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:a,digest:null}}function pl(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ml(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var gl="function"==typeof WeakMap?WeakMap:Map;function yl(e,t,n){(n=qi(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ju||(Ju=!0,Qu=r),ml(0,t)},n}function vl(e,t,n){(n=qi(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ml(0,t)}}var o=e.stateNode;return null!==o&&"function"==typeof o.componentDidCatch&&(n.callback=function(){ml(0,t),"function"!=typeof r&&(null===Gu?Gu=new Set([this]):Gu.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function bl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new gl;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Cc.bind(null,e,t,n),t.then(e,e))}function wl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function _l(e,t,n,r,a){return 1&e.mode?(e.flags|=65536,e.lanes=a,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=qi(-1,1)).tag=2,Vi(n,t,1))),n.lanes|=1),e)}var kl=Ee.ReactCurrentOwner,Sl=!1;function El(e,t,n,r){t.child=null===e?ji(t,null,n,r):xi(t,e.child,n,r)}function xl(e,t,n,r,a){n=n.render;var o=t.ref;return Li(t,a),r=ws(e,t,n,r,o,a),n=_s(),null===e||Sl?(ui&&n&&oi(t),t.flags|=1,El(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Jl(e,t,a))}function jl(e,t,n,r,a){if(null===e){var o=n.type;return"function"!=typeof o||Ac(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=zc(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Tl(e,t,o,r,a))}if(o=e.child,!(e.lanes&a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:fa)(i,r)&&e.ref===t.ref)return Jl(e,t,a)}return t.flags|=1,(e=Dc(o,r)).ref=t.ref,e.return=t,t.child=e}function Tl(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(fa(o,r)&&e.ref===t.ref){if(Sl=!1,t.pendingProps=r=o,!(e.lanes&a))return t.lanes=e.lanes,Jl(e,t,a);131072&e.flags&&(Sl=!0)}}return Ol(e,t,n,r,a)}function Pl(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,No(Du,Au),Au|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,No(Du,Au),Au|=r}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},No(Du,Au),Au|=n;else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,No(Du,Au),Au|=r;return El(e,t,a,n),t.child}function Cl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ol(e,t,n,r,a){var o=Do(n)?$o:Io.current;return o=Ao(t,o),Li(t,a),n=ws(e,t,n,r,o,a),r=_s(),null===e||Sl?(ui&&r&&oi(t),t.flags|=1,El(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Jl(e,t,a))}function Nl(e,t,n,r,a){if(Do(n)){var o=!0;Fo(t)}else o=!1;if(Li(t,a),null===t.stateNode)Kl(e,t),cl(t,n,r),fl(t,n,r,a),r=!0;else if(null===e){var i=t.stateNode,s=t.memoizedProps;i.props=s;var l=i.context,u=n.contextType;"object"==typeof u&&null!==u?u=$i(u):u=Ao(t,u=Do(n)?$o:Io.current);var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof i.getSnapshotBeforeUpdate;d||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(s!==r||l!==u)&&dl(t,i,r,u),Mi=!1;var f=t.memoizedState;i.state=f,Ki(t,r,i,a),l=t.memoizedState,s!==r||f!==l||Lo.current||Mi?("function"==typeof c&&(sl(t,n,c,r),l=t.memoizedState),(s=Mi||ul(t,n,s,r,f,l,u))?(d||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=u,r=s):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Bi(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:il(t.type,s),i.props=u,d=t.pendingProps,f=i.context,"object"==typeof(l=n.contextType)&&null!==l?l=$i(l):l=Ao(t,l=Do(n)?$o:Io.current);var h=n.getDerivedStateFromProps;(c="function"==typeof h||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(s!==d||f!==l)&&dl(t,i,r,l),Mi=!1,f=t.memoizedState,i.state=f,Ki(t,r,i,a);var p=t.memoizedState;s!==d||f!==p||Lo.current||Mi?("function"==typeof h&&(sl(t,n,h,r),p=t.memoizedState),(u=Mi||ul(t,n,u,r,f,p,l)||!1)?(c||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,p,l),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,p,l)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),i.props=r,i.state=p,i.context=l,r=u):("function"!=typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Rl(e,t,n,r,o,a)}function Rl(e,t,n,r,a,o){Cl(e,t);var i=!!(128&t.flags);if(!r&&!i)return a&&Bo(t,n,!1),Jl(e,t,o);r=t.stateNode,kl.current=t;var s=i&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=xi(t,e.child,null,o),t.child=xi(t,null,s,o)):El(e,t,s,o),t.memoizedState=r.state,a&&Bo(t,n,!0),t.child}function Il(e){var t=e.stateNode;t.pendingContext?Uo(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Uo(0,t.context,!1),es(e,t.containerInfo)}function Ll(e,t,n,r,a){return vi(),bi(a),t.flags|=256,El(e,t,n,r),t.child}var $l,Al,Dl,zl,Ul={dehydrated:null,treeContext:null,retryLane:0};function Ml(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fl(e,t,n){var r,a=t.pendingProps,o=as.current,i=!1,s=!!(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&!!(2&o)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),No(as,1&o),null===e)return pi(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},1&a||null===i?i=Mc(s,a,0,null):(i.childLanes=0,i.pendingProps=s),e=Uc(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ml(n),t.memoizedState=Ul,e):Bl(t,s));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,i){if(n)return 256&t.flags?(t.flags&=-257,ql(e,t,i,r=pl(Error(ue(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Mc({mode:"visible",children:r.children},a,0,null),(o=Uc(o,a,i,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,1&t.mode&&xi(t,e.child,null,i),t.child.memoizedState=Ml(i),t.memoizedState=Ul,o);if(!(1&t.mode))return ql(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,ql(e,t,i,r=pl(o=Error(ue(419)),r,void 0))}if(s=!!(i&e.childLanes),Sl||s){if(null!==(r=Iu)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=a&(r.suspendedLanes|i)?0:a)&&a!==o.retryLane&&(o.retryLane=a,Ui(e,a),ic(r,e,a,-1))}return bc(),ql(e,t,i,r=pl(Error(ue(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Nc.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,li=po(a.nextSibling),si=t,ui=!0,ci=null,null!==e&&(Xo[Zo++]=ti,Xo[Zo++]=ni,Xo[Zo++]=ei,ti=e.id,ni=e.overflow,ei=t),t=Bl(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,o,n);if(i){i=a.fallback,s=t.mode,r=(o=e.child).sibling;var l={mode:"hidden",children:a.children};return 1&s||t.child===o?(a=Dc(o,l)).subtreeFlags=14680064&o.subtreeFlags:((a=t.child).childLanes=0,a.pendingProps=l,t.deletions=null),null!==r?i=Dc(r,i):(i=Uc(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Ml(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Ul,a}return e=(i=e.child).sibling,a=Dc(i,{mode:"visible",children:a.children}),!(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Bl(e,t){return(t=Mc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function ql(e,t,n,r){return null!==r&&bi(r),xi(t,e.child,null,n),(e=Bl(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Vl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ii(e.return,t,n)}function Wl(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Hl(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(El(e,t,r.children,n),2&(r=as.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Vl(e,n,t);else if(19===e.tag)Vl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(No(as,r),1&t.mode)switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===os(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Wl(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===os(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Wl(t,!0,n,null,o);break;case"together":Wl(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Kl(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Jl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Mu|=t.lanes,!(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(ue(153));if(null!==t.child){for(n=Dc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Dc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ql(e,t){if(!ui)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Gl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Yl(e,t,n){var r=t.pendingProps;switch(ii(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Gl(t),null;case 1:case 17:return Do(t.type)&&zo(),Gl(t),null;case 3:return r=t.stateNode,ts(),Oo(Lo),Oo(Io),ss(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(gi(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==ci&&(cc(ci),ci=null))),Al(e,t),Gl(t),null;case 5:rs(t);var a=Zi(Xi.current);if(n=t.type,null!==e&&null!=t.stateNode)Dl(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(ue(166));return Gl(t),null}if(e=Zi(Gi.current),gi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[yo]=t,r[vo]=o,e=!!(1&t.mode),n){case"dialog":qa("cancel",r),qa("close",r);break;case"iframe":case"object":case"embed":qa("load",r);break;case"video":case"audio":for(a=0;a<Ua.length;a++)qa(Ua[a],r);break;case"source":qa("error",r);break;case"img":case"image":case"link":qa("error",r),qa("load",r);break;case"details":qa("toggle",r);break;case"input":et(r,o),qa("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},qa("invalid",r);break;case"textarea":lt(r,o),qa("invalid",r)}for(var i in kt(n,o),a=null,o)if(o.hasOwnProperty(i)){var s=o[i];"children"===i?"string"==typeof s?r.textContent!==s&&(!0!==o.suppressHydrationWarning&&no(r.textContent,s,e),a=["children",s]):"number"==typeof s&&r.textContent!==""+s&&(!0!==o.suppressHydrationWarning&&no(r.textContent,s,e),a=["children",""+s]):de.hasOwnProperty(i)&&null!=s&&"onScroll"===i&&qa("scroll",r)}switch(n){case"input":Ge(r),rt(r,o,!0);break;case"textarea":Ge(r),ct(r);break;case"select":case"option":break;default:"function"==typeof o.onClick&&(r.onclick=ro)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{i=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=dt(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),"select"===n&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[yo]=t,e[vo]=r,$l(e,t,!1,!1),t.stateNode=e;e:{switch(i=St(n,r),n){case"dialog":qa("cancel",e),qa("close",e),a=r;break;case"iframe":case"object":case"embed":qa("load",e),a=r;break;case"video":case"audio":for(a=0;a<Ua.length;a++)qa(Ua[a],e);a=r;break;case"source":qa("error",e),a=r;break;case"img":case"image":case"link":qa("error",e),qa("load",e),a=r;break;case"details":qa("toggle",e),a=r;break;case"input":et(e,r),a=Ze(e,r),qa("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=Fe({},r,{value:void 0}),qa("invalid",e);break;case"textarea":lt(e,r),a=st(e,r),qa("invalid",e)}for(o in kt(n,a),s=a)if(s.hasOwnProperty(o)){var l=s[o];"style"===o?wt(e,l):"dangerouslySetInnerHTML"===o?null!=(l=l?l.__html:void 0)&&mt(e,l):"children"===o?"string"==typeof l?("textarea"!==n||""!==l)&&gt(e,l):"number"==typeof l&&gt(e,""+l):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(de.hasOwnProperty(o)?null!=l&&"onScroll"===o&&qa("scroll",e):null!=l&&Se(e,o,l,i))}switch(n){case"input":Ge(e),rt(e,r,!1);break;case"textarea":Ge(e),ct(e);break;case"option":null!=r.value&&e.setAttribute("value",""+Je(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?it(e,!!r.multiple,o,!1):null!=r.defaultValue&&it(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof a.onClick&&(e.onclick=ro)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Gl(t),null;case 6:if(e&&null!=t.stateNode)zl(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(ue(166));if(n=Zi(Xi.current),Zi(Gi.current),gi(t)){if(r=t.stateNode,n=t.memoizedProps,r[yo]=t,(o=r.nodeValue!==n)&&null!==(e=si))switch(e.tag){case 3:no(r.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&no(r.nodeValue,n,!!(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[yo]=t,t.stateNode=r}return Gl(t),null;case 13:if(Oo(as),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ui&&null!==li&&1&t.mode&&!(128&t.flags))yi(),vi(),t.flags|=98560,o=!1;else if(o=gi(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(ue(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(ue(317));o[yo]=t}else vi(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Gl(t),o=!1}else null!==ci&&(cc(ci),ci=null),o=!0;if(!o)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,1&t.mode&&(null===e||1&as.current?0===zu&&(zu=3):bc())),null!==t.updateQueue&&(t.flags|=4),Gl(t),null);case 4:return ts(),Al(e,t),null===e&&Ha(t.stateNode.containerInfo),Gl(t),null;case 10:return Ri(t.type._context),Gl(t),null;case 19:if(Oo(as),null===(o=t.memoizedState))return Gl(t),null;if(r=!!(128&t.flags),null===(i=o.rendering))if(r)Ql(o,!1);else{if(0!==zu||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(i=os(e))){for(t.flags|=128,Ql(o,!1),null!==(r=i.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(i=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return No(as,1&as.current|2),t.child}e=e.sibling}null!==o.tail&&tn()>Hu&&(t.flags|=128,r=!0,Ql(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=os(i))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ql(o,!0),null===o.tail&&"hidden"===o.tailMode&&!i.alternate&&!ui)return Gl(t),null}else 2*tn()-o.renderingStartTime>Hu&&1073741824!==n&&(t.flags|=128,r=!0,Ql(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(null!==(n=o.last)?n.sibling=i:t.child=i,o.last=i)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=tn(),t.sibling=null,n=as.current,No(as,r?1&n|2:1&n),t):(Gl(t),null);case 22:case 23:return mc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&1&t.mode?!!(1073741824&Au)&&(Gl(t),6&t.subtreeFlags&&(t.flags|=8192)):Gl(t),null;case 24:case 25:return null}throw Error(ue(156,t.tag))}function Xl(e,t){switch(ii(t),t.tag){case 1:return Do(t.type)&&zo(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ts(),Oo(Lo),Oo(Io),ss(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return rs(t),null;case 13:if(Oo(as),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(ue(340));vi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Oo(as),null;case 4:return ts(),null;case 10:return Ri(t.type._context),null;case 22:case 23:return mc(),null;default:return null}}$l=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Al=function(){},Dl=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Zi(Gi.current);var o,i=null;switch(n){case"input":a=Ze(e,a),r=Ze(e,r),i=[];break;case"select":a=Fe({},a,{value:void 0}),r=Fe({},r,{value:void 0}),i=[];break;case"textarea":a=st(e,a),r=st(e,r),i=[];break;default:"function"!=typeof a.onClick&&"function"==typeof r.onClick&&(e.onclick=ro)}for(u in kt(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var s=a[u];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(de.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(s=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&l!==s&&(null!=l||null!=s))if("style"===u)if(s){for(o in s)!s.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&s[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(i||(i=[]),i.push(u,n)),n=l;else"dangerouslySetInnerHTML"===u?(l=l?l.__html:void 0,s=s?s.__html:void 0,null!=l&&s!==l&&(i=i||[]).push(u,l)):"children"===u?"string"!=typeof l&&"number"!=typeof l||(i=i||[]).push(u,""+l):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(de.hasOwnProperty(u)?(null!=l&&"onScroll"===u&&qa("scroll",e),i||s===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},zl=function(e,t,n,r){n!==r&&(t.flags|=4)};var Zl=!1,eu=!1,tu="function"==typeof WeakSet?WeakSet:Set,nu=null;function ru(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(r){Pc(e,t,r)}else n.current=null}function au(e,t,n){try{n()}catch(r){Pc(e,t,r)}}var ou=!1;function iu(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&au(t,n,o)}a=a.next}while(a!==r)}}function su(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function lu(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function uu(e){var t=e.alternate;null!==t&&(e.alternate=null,uu(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[yo],delete t[vo],delete t[wo],delete t[_o],delete t[ko])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function cu(e){return 5===e.tag||3===e.tag||4===e.tag}function du(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||cu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function fu(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=ro));else if(4!==r&&null!==(e=e.child))for(fu(e,t,n),e=e.sibling;null!==e;)fu(e,t,n),e=e.sibling}function hu(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(hu(e,t,n),e=e.sibling;null!==e;)hu(e,t,n),e=e.sibling}var pu=null,mu=!1;function gu(e,t,n){for(n=n.child;null!==n;)yu(e,t,n),n=n.sibling}function yu(e,t,n){if(cn&&"function"==typeof cn.onCommitFiberUnmount)try{cn.onCommitFiberUnmount(un,n)}catch(s){}switch(n.tag){case 5:eu||ru(n,t);case 6:var r=pu,a=mu;pu=null,gu(e,t,n),mu=a,null!==(pu=r)&&(mu?(e=pu,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):pu.removeChild(n.stateNode));break;case 18:null!==pu&&(mu?(e=pu,n=n.stateNode,8===e.nodeType?ho(e.parentNode,n):1===e.nodeType&&ho(e,n),Kn(e)):ho(pu,n.stateNode));break;case 4:r=pu,a=mu,pu=n.stateNode.containerInfo,mu=!0,gu(e,t,n),pu=r,mu=a;break;case 0:case 11:case 14:case 15:if(!eu&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,i=o.destroy;o=o.tag,void 0!==i&&(2&o||4&o)&&au(n,t,i),a=a.next}while(a!==r)}gu(e,t,n);break;case 1:if(!eu&&(ru(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Pc(n,t,s)}gu(e,t,n);break;case 21:gu(e,t,n);break;case 22:1&n.mode?(eu=(r=eu)||null!==n.memoizedState,gu(e,t,n),eu=r):gu(e,t,n);break;default:gu(e,t,n)}}function vu(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new tu),t.forEach((function(t){var r=Rc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function bu(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:pu=s.stateNode,mu=!1;break e;case 3:case 4:pu=s.stateNode.containerInfo,mu=!0;break e}s=s.return}if(null===pu)throw Error(ue(160));yu(o,i,a),pu=null,mu=!1;var l=a.alternate;null!==l&&(l.return=null),a.return=null}catch(u){Pc(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)wu(t,e),t=t.sibling}function wu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(bu(t,e),_u(e),4&r){try{iu(3,e,e.return),su(3,e)}catch(m){Pc(e,e.return,m)}try{iu(5,e,e.return)}catch(m){Pc(e,e.return,m)}}break;case 1:bu(t,e),_u(e),512&r&&null!==n&&ru(n,n.return);break;case 5:if(bu(t,e),_u(e),512&r&&null!==n&&ru(n,n.return),32&e.flags){var a=e.stateNode;try{gt(a,"")}catch(m){Pc(e,e.return,m)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,i=null!==n?n.memoizedProps:o,s=e.type,l=e.updateQueue;if(e.updateQueue=null,null!==l)try{"input"===s&&"radio"===o.type&&null!=o.name&&tt(a,o),St(s,i);var u=St(s,o);for(i=0;i<l.length;i+=2){var c=l[i],d=l[i+1];"style"===c?wt(a,d):"dangerouslySetInnerHTML"===c?mt(a,d):"children"===c?gt(a,d):Se(a,c,d,u)}switch(s){case"input":nt(a,o);break;case"textarea":ut(a,o);break;case"select":var f=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var h=o.value;null!=h?it(a,!!o.multiple,h,!1):f!==!!o.multiple&&(null!=o.defaultValue?it(a,!!o.multiple,o.defaultValue,!0):it(a,!!o.multiple,o.multiple?[]:"",!1))}a[vo]=o}catch(m){Pc(e,e.return,m)}}break;case 6:if(bu(t,e),_u(e),4&r){if(null===e.stateNode)throw Error(ue(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(m){Pc(e,e.return,m)}}break;case 3:if(bu(t,e),_u(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Kn(t.containerInfo)}catch(m){Pc(e,e.return,m)}break;case 4:default:bu(t,e),_u(e);break;case 13:bu(t,e),_u(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Wu=tn())),4&r&&vu(e);break;case 22:if(c=null!==n&&null!==n.memoizedState,1&e.mode?(eu=(u=eu)||c,bu(t,e),eu=u):bu(t,e),_u(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!c&&1&e.mode)for(nu=e,c=e.child;null!==c;){for(d=nu=c;null!==nu;){switch(h=(f=nu).child,f.tag){case 0:case 11:case 14:case 15:iu(4,f,f.return);break;case 1:ru(f,f.return);var p=f.stateNode;if("function"==typeof p.componentWillUnmount){r=f,n=f.return;try{t=r,p.props=t.memoizedProps,p.state=t.memoizedState,p.componentWillUnmount()}catch(m){Pc(r,n,m)}}break;case 5:ru(f,f.return);break;case 22:if(null!==f.memoizedState){xu(d);continue}}null!==h?(h.return=f,nu=h):xu(d)}c=c.sibling}e:for(c=null,d=e;;){if(5===d.tag){if(null===c){c=d;try{a=d.stateNode,u?"function"==typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(s=d.stateNode,i=null!=(l=d.memoizedProps.style)&&l.hasOwnProperty("display")?l.display:null,s.style.display=bt("display",i))}catch(m){Pc(e,e.return,m)}}}else if(6===d.tag){if(null===c)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(m){Pc(e,e.return,m)}}else if((22!==d.tag&&23!==d.tag||null===d.memoizedState||d===e)&&null!==d.child){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;null===d.sibling;){if(null===d.return||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:bu(t,e),_u(e),4&r&&vu(e);case 21:}}function _u(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(cu(n)){var r=n;break e}n=n.return}throw Error(ue(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(gt(a,""),r.flags&=-33),hu(e,du(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;fu(e,du(e),o);break;default:throw Error(ue(161))}}catch(i){Pc(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function ku(e,t,n){nu=e,Su(e)}function Su(e,t,n){for(var r=!!(1&e.mode);null!==nu;){var a=nu,o=a.child;if(22===a.tag&&r){var i=null!==a.memoizedState||Zl;if(!i){var s=a.alternate,l=null!==s&&null!==s.memoizedState||eu;s=Zl;var u=eu;if(Zl=i,(eu=l)&&!u)for(nu=a;null!==nu;)l=(i=nu).child,22===i.tag&&null!==i.memoizedState?ju(a):null!==l?(l.return=i,nu=l):ju(a);for(;null!==o;)nu=o,Su(o),o=o.sibling;nu=a,Zl=s,eu=u}Eu(e)}else 8772&a.subtreeFlags&&null!==o?(o.return=a,nu=o):Eu(e)}}function Eu(e){for(;null!==nu;){var t=nu;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:eu||su(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!eu)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:il(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Ji(t,o,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ji(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var c=u.memoizedState;if(null!==c){var d=c.dehydrated;null!==d&&Kn(d)}}}break;default:throw Error(ue(163))}eu||512&t.flags&&lu(t)}catch(f){Pc(t,t.return,f)}}if(t===e){nu=null;break}if(null!==(n=t.sibling)){n.return=t.return,nu=n;break}nu=t.return}}function xu(e){for(;null!==nu;){var t=nu;if(t===e){nu=null;break}var n=t.sibling;if(null!==n){n.return=t.return,nu=n;break}nu=t.return}}function ju(e){for(;null!==nu;){var t=nu;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{su(4,t)}catch(l){Pc(t,n,l)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(l){Pc(t,a,l)}}var o=t.return;try{lu(t)}catch(l){Pc(t,o,l)}break;case 5:var i=t.return;try{lu(t)}catch(l){Pc(t,i,l)}}}catch(l){Pc(t,t.return,l)}if(t===e){nu=null;break}var s=t.sibling;if(null!==s){s.return=t.return,nu=s;break}nu=t.return}}var Tu,Pu=Math.ceil,Cu=Ee.ReactCurrentDispatcher,Ou=Ee.ReactCurrentOwner,Nu=Ee.ReactCurrentBatchConfig,Ru=0,Iu=null,Lu=null,$u=0,Au=0,Du=Co(0),zu=0,Uu=null,Mu=0,Fu=0,Bu=0,qu=null,Vu=null,Wu=0,Hu=1/0,Ku=null,Ju=!1,Qu=null,Gu=null,Yu=!1,Xu=null,Zu=0,ec=0,tc=null,nc=-1,rc=0;function ac(){return 6&Ru?tn():-1!==nc?nc:nc=tn()}function oc(e){return 1&e.mode?2&Ru&&0!==$u?$u&-$u:null!==wi.transition?(0===rc&&(rc=wn()),rc):0!==(e=En)?e:e=void 0===(e=window.event)?16:tr(e.type):1}function ic(e,t,n,r){if(50<ec)throw ec=0,tc=null,Error(ue(185));kn(e,n,r),2&Ru&&e===Iu||(e===Iu&&(!(2&Ru)&&(Fu|=n),4===zu&&dc(e,$u)),sc(e,r),1===n&&0===Ru&&!(1&t.mode)&&(Hu=tn()+500,Vo&&Ko()))}function sc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-dn(o),s=1<<i,l=a[i];-1===l?s&n&&!(s&r)||(a[i]=vn(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}(e,t);var r=yn(e,e===Iu?$u:0);if(0===r)null!==n&&Xt(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Xt(n),1===t)0===e.tag?function(e){Vo=!0,Ho(e)}(fc.bind(null,e)):Ho(fc.bind(null,e)),co((function(){!(6&Ru)&&Ko()})),n=null;else{switch(xn(r)){case 1:n=rn;break;case 4:n=an;break;case 16:default:n=on;break;case 536870912:n=ln}n=Ic(n,lc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function lc(e,t){if(nc=-1,rc=0,6&Ru)throw Error(ue(327));var n=e.callbackNode;if(jc()&&e.callbackNode!==n)return null;var r=yn(e,e===Iu?$u:0);if(0===r)return null;if(30&r||r&e.expiredLanes||t)t=wc(e,r);else{t=r;var a=Ru;Ru|=2;var o=vc();for(Iu===e&&$u===t||(Ku=null,Hu=tn()+500,gc(e,t));;)try{kc();break}catch(s){yc(e,s)}Ni(),Cu.current=o,Ru=a,null!==Lu?t=0:(Iu=null,$u=0,t=zu)}if(0!==t){if(2===t&&(0!==(a=bn(e))&&(r=a,t=uc(e,a))),1===t)throw n=Uu,gc(e,0),dc(e,r),sc(e,tn()),n;if(6===t)dc(e,r);else{if(a=e.current.alternate,!(30&r||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!da(o(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)||(t=wc(e,r),2===t&&(o=bn(e),0!==o&&(r=o,t=uc(e,o))),1!==t)))throw n=Uu,gc(e,0),dc(e,r),sc(e,tn()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(ue(345));case 2:case 5:xc(e,Vu,Ku);break;case 3:if(dc(e,r),(130023424&r)===r&&10<(t=Wu+500-tn())){if(0!==yn(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ac(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=so(xc.bind(null,e,Vu,Ku),t);break}xc(e,Vu,Ku);break;case 4:if(dc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-dn(r);o=1<<i,(i=t[i])>a&&(a=i),r&=~o}if(r=a,10<(r=(120>(r=tn()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Pu(r/1960))-r)){e.timeoutHandle=so(xc.bind(null,e,Vu,Ku),r);break}xc(e,Vu,Ku);break;default:throw Error(ue(329))}}}return sc(e,tn()),e.callbackNode===n?lc.bind(null,e):null}function uc(e,t){var n=qu;return e.current.memoizedState.isDehydrated&&(gc(e,t).flags|=256),2!==(e=wc(e,t))&&(t=Vu,Vu=n,null!==t&&cc(t)),e}function cc(e){null===Vu?Vu=e:Vu.push.apply(Vu,e)}function dc(e,t){for(t&=~Bu,t&=~Fu,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-dn(t),r=1<<n;e[n]=-1,t&=~r}}function fc(e){if(6&Ru)throw Error(ue(327));jc();var t=yn(e,0);if(!(1&t))return sc(e,tn()),null;var n=wc(e,t);if(0!==e.tag&&2===n){var r=bn(e);0!==r&&(t=r,n=uc(e,r))}if(1===n)throw n=Uu,gc(e,0),dc(e,t),sc(e,tn()),n;if(6===n)throw Error(ue(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xc(e,Vu,Ku),sc(e,tn()),null}function hc(e,t){var n=Ru;Ru|=1;try{return e(t)}finally{0===(Ru=n)&&(Hu=tn()+500,Vo&&Ko())}}function pc(e){null!==Xu&&0===Xu.tag&&!(6&Ru)&&jc();var t=Ru;Ru|=1;var n=Nu.transition,r=En;try{if(Nu.transition=null,En=1,e)return e()}finally{En=r,Nu.transition=n,!(6&(Ru=t))&&Ko()}}function mc(){Au=Du.current,Oo(Du)}function gc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,lo(n)),null!==Lu)for(n=Lu.return;null!==n;){var r=n;switch(ii(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&zo();break;case 3:ts(),Oo(Lo),Oo(Io),ss();break;case 5:rs(r);break;case 4:ts();break;case 13:case 19:Oo(as);break;case 10:Ri(r.type._context);break;case 22:case 23:mc()}n=n.return}if(Iu=e,Lu=e=Dc(e.current,null),$u=Au=t,zu=0,Uu=null,Bu=Fu=Mu=0,Vu=qu=null,null!==Ai){for(t=0;t<Ai.length;t++)if(null!==(r=(n=Ai[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var i=o.next;o.next=a,r.next=i}n.pending=r}Ai=null}return e}function yc(e,t){for(;;){var n=Lu;try{if(Ni(),ls.current=nl,ps){for(var r=ds.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}ps=!1}if(cs=0,hs=fs=ds=null,ms=!1,gs=0,Ou.current=null,null===n||null===n.return){zu=1,Uu=t,Lu=null;break}e:{var o=e,i=n.return,s=n,l=t;if(t=$u,s.flags|=32768,null!==l&&"object"==typeof l&&"function"==typeof l.then){var u=l,c=s,d=c.tag;if(!(1&c.mode||0!==d&&11!==d&&15!==d)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var h=wl(i);if(null!==h){h.flags&=-257,_l(h,i,s,0,t),1&h.mode&&bl(o,u,t),l=u;var p=(t=h).updateQueue;if(null===p){var m=new Set;m.add(l),t.updateQueue=m}else p.add(l);break e}if(!(1&t)){bl(o,u,t),bc();break e}l=Error(ue(426))}else if(ui&&1&s.mode){var g=wl(i);if(null!==g){!(65536&g.flags)&&(g.flags|=256),_l(g,i,s,0,t),bi(hl(l,s));break e}}o=l=hl(l,s),4!==zu&&(zu=2),null===qu?qu=[o]:qu.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Hi(o,yl(0,l,t));break e;case 1:s=l;var y=o.type,v=o.stateNode;if(!(128&o.flags||"function"!=typeof y.getDerivedStateFromError&&(null===v||"function"!=typeof v.componentDidCatch||null!==Gu&&Gu.has(v)))){o.flags|=65536,t&=-t,o.lanes|=t,Hi(o,vl(o,s,t));break e}}o=o.return}while(null!==o)}Ec(n)}catch(b){t=b,Lu===n&&null!==n&&(Lu=n=n.return);continue}break}}function vc(){var e=Cu.current;return Cu.current=nl,null===e?nl:e}function bc(){0!==zu&&3!==zu&&2!==zu||(zu=4),null===Iu||!(268435455&Mu)&&!(268435455&Fu)||dc(Iu,$u)}function wc(e,t){var n=Ru;Ru|=2;var r=vc();for(Iu===e&&$u===t||(Ku=null,gc(e,t));;)try{_c();break}catch(a){yc(e,a)}if(Ni(),Ru=n,Cu.current=r,null!==Lu)throw Error(ue(261));return Iu=null,$u=0,zu}function _c(){for(;null!==Lu;)Sc(Lu)}function kc(){for(;null!==Lu&&!Zt();)Sc(Lu)}function Sc(e){var t=Tu(e.alternate,e,Au);e.memoizedProps=e.pendingProps,null===t?Ec(e):Lu=t,Ou.current=null}function Ec(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Xl(n,t)))return n.flags&=32767,void(Lu=n);if(null===e)return zu=6,void(Lu=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=Yl(n,t,Au)))return void(Lu=n);if(null!==(t=t.sibling))return void(Lu=t);Lu=t=e}while(null!==t);0===zu&&(zu=5)}function xc(e,t,n){var r=En,a=Nu.transition;try{Nu.transition=null,En=1,function(e,t,n,r){do{jc()}while(null!==Xu);if(6&Ru)throw Error(ue(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(ue(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-dn(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,o),e===Iu&&(Lu=Iu=null,$u=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||Yu||(Yu=!0,Ic(on,(function(){return jc(),null}))),o=!!(15990&n.flags),!!(15990&n.subtreeFlags)||o){o=Nu.transition,Nu.transition=null;var i=En;En=1;var s=Ru;Ru|=4,Ou.current=null,function(e,t){if(ao=Qn,ya(e=ga())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(w){n=null;break e}var i=0,s=-1,l=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var h;d!==n||0!==a&&3!==d.nodeType||(s=i+a),d!==o||0!==r&&3!==d.nodeType||(l=i+r),3===d.nodeType&&(i+=d.nodeValue.length),null!==(h=d.firstChild);)f=d,d=h;for(;;){if(d===e)break t;if(f===n&&++u===a&&(s=i),f===o&&++c===r&&(l=i),null!==(h=d.nextSibling))break;f=(d=f).parentNode}d=h}n=-1===s||-1===l?null:{start:s,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(oo={focusedElem:e,selectionRange:n},Qn=!1,nu=t;null!==nu;)if(e=(t=nu).child,1028&t.subtreeFlags&&null!==e)e.return=t,nu=e;else for(;null!==nu;){t=nu;try{var p=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==p){var m=p.memoizedProps,g=p.memoizedState,y=t.stateNode,v=y.getSnapshotBeforeUpdate(t.elementType===t.type?m:il(t.type,m),g);y.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(ue(163))}}catch(w){Pc(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,nu=e;break}nu=t.return}p=ou,ou=!1}(e,n),wu(n,e),va(oo),Qn=!!ao,oo=ao=null,e.current=n,ku(n),en(),Ru=s,En=i,Nu.transition=o}else e.current=n;if(Yu&&(Yu=!1,Xu=e,Zu=a),o=e.pendingLanes,0===o&&(Gu=null),function(e){if(cn&&"function"==typeof cn.onCommitFiberRoot)try{cn.onCommitFiberRoot(un,e,void 0,!(128&~e.current.flags))}catch(t){}}(n.stateNode),sc(e,tn()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Ju)throw Ju=!1,e=Qu,Qu=null,e;!!(1&Zu)&&0!==e.tag&&jc(),o=e.pendingLanes,1&o?e===tc?ec++:(ec=0,tc=e):ec=0,Ko()}(e,t,n,r)}finally{Nu.transition=a,En=r}return null}function jc(){if(null!==Xu){var e=xn(Zu),t=Nu.transition,n=En;try{if(Nu.transition=null,En=16>e?16:e,null===Xu)var r=!1;else{if(e=Xu,Xu=null,Zu=0,6&Ru)throw Error(ue(331));var a=Ru;for(Ru|=4,nu=e.current;null!==nu;){var o=nu,i=o.child;if(16&nu.flags){var s=o.deletions;if(null!==s){for(var l=0;l<s.length;l++){var u=s[l];for(nu=u;null!==nu;){var c=nu;switch(c.tag){case 0:case 11:case 15:iu(8,c,o)}var d=c.child;if(null!==d)d.return=c,nu=d;else for(;null!==nu;){var f=(c=nu).sibling,h=c.return;if(uu(c),c===u){nu=null;break}if(null!==f){f.return=h,nu=f;break}nu=h}}}var p=o.alternate;if(null!==p){var m=p.child;if(null!==m){p.child=null;do{var g=m.sibling;m.sibling=null,m=g}while(null!==m)}}nu=o}}if(2064&o.subtreeFlags&&null!==i)i.return=o,nu=i;else e:for(;null!==nu;){if(2048&(o=nu).flags)switch(o.tag){case 0:case 11:case 15:iu(9,o,o.return)}var y=o.sibling;if(null!==y){y.return=o.return,nu=y;break e}nu=o.return}}var v=e.current;for(nu=v;null!==nu;){var b=(i=nu).child;if(2064&i.subtreeFlags&&null!==b)b.return=i,nu=b;else e:for(i=v;null!==nu;){if(2048&(s=nu).flags)try{switch(s.tag){case 0:case 11:case 15:su(9,s)}}catch(_){Pc(s,s.return,_)}if(s===i){nu=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,nu=w;break e}nu=s.return}}if(Ru=a,Ko(),cn&&"function"==typeof cn.onPostCommitFiberRoot)try{cn.onPostCommitFiberRoot(un,e)}catch(_){}r=!0}return r}finally{En=n,Nu.transition=t}}return!1}function Tc(e,t,n){e=Vi(e,t=yl(0,t=hl(n,t),1),1),t=ac(),null!==e&&(kn(e,1,t),sc(e,t))}function Pc(e,t,n){if(3===e.tag)Tc(e,e,n);else for(;null!==t;){if(3===t.tag){Tc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Gu||!Gu.has(r))){t=Vi(t,e=vl(t,e=hl(n,e),1),1),e=ac(),null!==t&&(kn(t,1,e),sc(t,e));break}}t=t.return}}function Cc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ac(),e.pingedLanes|=e.suspendedLanes&n,Iu===e&&($u&n)===n&&(4===zu||3===zu&&(130023424&$u)===$u&&500>tn()-Wu?gc(e,0):Bu|=n),sc(e,t)}function Oc(e,t){0===t&&(1&e.mode?(t=mn,!(130023424&(mn<<=1))&&(mn=4194304)):t=1);var n=ac();null!==(e=Ui(e,t))&&(kn(e,t,n),sc(e,n))}function Nc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Oc(e,n)}function Rc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(ue(314))}null!==r&&r.delete(t),Oc(e,n)}function Ic(e,t){return Yt(e,t)}function Lc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $c(e,t,n,r){return new Lc(e,t,n,r)}function Ac(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Dc(e,t){var n=e.alternate;return null===n?((n=$c(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function zc(e,t,n,r,a,o){var i=2;if(r=e,"function"==typeof e)Ac(e)&&(i=1);else if("string"==typeof e)i=5;else e:switch(e){case Te:return Uc(n.children,a,o,t);case Pe:i=8,a|=8;break;case Ce:return(e=$c(12,n,t,2|a)).elementType=Ce,e.lanes=o,e;case Ie:return(e=$c(13,n,t,a)).elementType=Ie,e.lanes=o,e;case Le:return(e=$c(19,n,t,a)).elementType=Le,e.lanes=o,e;case De:return Mc(n,a,o,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case Oe:i=10;break e;case Ne:i=9;break e;case Re:i=11;break e;case $e:i=14;break e;case Ae:i=16,r=null;break e}throw Error(ue(130,null==e?e:typeof e,""))}return(t=$c(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Uc(e,t,n,r){return(e=$c(7,e,r,t)).lanes=n,e}function Mc(e,t,n,r){return(e=$c(22,e,r,t)).elementType=De,e.lanes=n,e.stateNode={isHidden:!1},e}function Fc(e,t,n){return(e=$c(6,e,null,t)).lanes=n,e}function Bc(e,t,n){return(t=$c(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function qc(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=_n(0),this.expirationTimes=_n(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=_n(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Vc(e,t,n,r,a,o,i,s,l){return e=new qc(e,t,n,s,l),1===t?(t=1,!0===o&&(t|=8)):t=0,o=$c(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fi(o),e}function Wc(e){if(!e)return Ro;e:{if(Ht(e=e._reactInternals)!==e||1!==e.tag)throw Error(ue(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Do(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(ue(171))}if(1===e.tag){var n=e.type;if(Do(n))return Mo(e,n,t)}return t}function Hc(e,t,n,r,a,o,i,s,l){return(e=Vc(n,r,!0,e,0,o,0,s,l)).context=Wc(null),n=e.current,(o=qi(r=ac(),a=oc(n))).callback=null!=t?t:null,Vi(n,o,a),e.current.lanes=a,kn(e,a,r),sc(e,r),e}function Kc(e,t,n,r){var a=t.current,o=ac(),i=oc(a);return n=Wc(n),null===t.context?t.context=n:t.pendingContext=n,(t=qi(o,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Vi(a,t,i))&&(ic(e,a,i,o),Wi(e,a,i)),i}function Jc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Qc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Gc(e,t){Qc(e,t),(e=e.alternate)&&Qc(e,t)}Tu=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Lo.current)Sl=!0;else{if(!(e.lanes&n||128&t.flags))return Sl=!1,function(e,t,n){switch(t.tag){case 3:Il(t),vi();break;case 5:ns(t);break;case 1:Do(t.type)&&Fo(t);break;case 4:es(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;No(Ti,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(No(as,1&as.current),t.flags|=128,null):n&t.child.childLanes?Fl(e,t,n):(No(as,1&as.current),null!==(e=Jl(e,t,n))?e.sibling:null);No(as,1&as.current);break;case 19:if(r=!!(n&t.childLanes),128&e.flags){if(r)return Hl(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),No(as,as.current),r)break;return null;case 22:case 23:return t.lanes=0,Pl(e,t,n)}return Jl(e,t,n)}(e,t,n);Sl=!!(131072&e.flags)}else Sl=!1,ui&&1048576&t.flags&&ai(t,Yo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Kl(e,t),e=t.pendingProps;var a=Ao(t,Io.current);Li(t,n),a=ws(null,t,r,e,a,n);var o=_s();return t.flags|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Do(r)?(o=!0,Fo(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Fi(t),a.updater=ll,t.stateNode=a,a._reactInternals=t,fl(t,r,e,n),t=Rl(null,t,r,!0,o,n)):(t.tag=0,ui&&o&&oi(t),El(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Kl(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"==typeof e)return Ac(e)?1:0;if(null!=e){if((e=e.$$typeof)===Re)return 11;if(e===$e)return 14}return 2}(r),e=il(r,e),a){case 0:t=Ol(null,t,r,e,n);break e;case 1:t=Nl(null,t,r,e,n);break e;case 11:t=xl(null,t,r,e,n);break e;case 14:t=jl(null,t,r,il(r.type,e),n);break e}throw Error(ue(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ol(e,t,r,a=t.elementType===r?a:il(r,a),n);case 1:return r=t.type,a=t.pendingProps,Nl(e,t,r,a=t.elementType===r?a:il(r,a),n);case 3:e:{if(Il(t),null===e)throw Error(ue(387));r=t.pendingProps,a=(o=t.memoizedState).element,Bi(e,t),Ki(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ll(e,t,r,n,a=hl(Error(ue(423)),t));break e}if(r!==a){t=Ll(e,t,r,n,a=hl(Error(ue(424)),t));break e}for(li=po(t.stateNode.containerInfo.firstChild),si=t,ui=!0,ci=null,n=ji(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(vi(),r===a){t=Jl(e,t,n);break e}El(e,t,r,n)}t=t.child}return t;case 5:return ns(t),null===e&&pi(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,io(r,a)?i=null:null!==o&&io(r,o)&&(t.flags|=32),Cl(e,t),El(e,t,i,n),t.child;case 6:return null===e&&pi(t),null;case 13:return Fl(e,t,n);case 4:return es(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xi(t,null,r,n):El(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,xl(e,t,r,a=t.elementType===r?a:il(r,a),n);case 7:return El(e,t,t.pendingProps,n),t.child;case 8:case 12:return El(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,i=a.value,No(Ti,r._currentValue),r._currentValue=i,null!==o)if(da(o.value,i)){if(o.children===a.children&&!Lo.current){t=Jl(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var s=o.dependencies;if(null!==s){i=o.child;for(var l=s.firstContext;null!==l;){if(l.context===r){if(1===o.tag){(l=qi(-1,n&-n)).tag=2;var u=o.updateQueue;if(null!==u){var c=(u=u.shared).pending;null===c?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),Ii(o.return,n,t),s.lanes|=n;break}l=l.next}}else if(10===o.tag)i=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(i=o.return))throw Error(ue(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Ii(i,n,t),i=o.sibling}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===t){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}El(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Li(t,n),r=r(a=$i(a)),t.flags|=1,El(e,t,r,n),t.child;case 14:return a=il(r=t.type,t.pendingProps),jl(e,t,r,a=il(r.type,a),n);case 15:return Tl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:il(r,a),Kl(e,t),t.tag=1,Do(r)?(e=!0,Fo(t)):e=!1,Li(t,n),cl(t,r,a),fl(t,r,a,n),Rl(null,t,r,!0,e,n);case 19:return Hl(e,t,n);case 22:return Pl(e,t,n)}throw Error(ue(156,t.tag))};var Yc="function"==typeof reportError?reportError:function(e){console.error(e)};function Xc(e){this._internalRoot=e}function Zc(e){this._internalRoot=e}function ed(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function td(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function nd(){}function rd(e,t,n,r,a){var o=n._reactRootContainer;if(o){var i=o;if("function"==typeof a){var s=a;a=function(){var e=Jc(i);s.call(e)}}Kc(t,i,e,a)}else i=function(e,t,n,r,a){if(a){if("function"==typeof r){var o=r;r=function(){var e=Jc(i);o.call(e)}}var i=Hc(t,r,e,0,null,!1,0,"",nd);return e._reactRootContainer=i,e[bo]=i.current,Ha(8===e.nodeType?e.parentNode:e),pc(),i}for(;a=e.lastChild;)e.removeChild(a);if("function"==typeof r){var s=r;r=function(){var e=Jc(l);s.call(e)}}var l=Vc(e,0,!1,null,0,!1,0,"",nd);return e._reactRootContainer=l,e[bo]=l.current,Ha(8===e.nodeType?e.parentNode:e),pc((function(){Kc(t,l,n,r)})),l}(n,t,e,a,r);return Jc(i)}Zc.prototype.render=Xc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(ue(409));Kc(e,t,null,null)},Zc.prototype.unmount=Xc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;pc((function(){Kc(null,e,null,null)})),t[bo]=null}},Zc.prototype.unstable_scheduleHydration=function(e){if(e){var t=Cn();e={blockedOn:null,target:e,priority:t};for(var n=0;n<zn.length&&0!==t&&t<zn[n].priority;n++);zn.splice(n,0,e),0===n&&Bn(e)}},jn=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=gn(t.pendingLanes);0!==n&&(Sn(t,1|n),sc(t,tn()),!(6&Ru)&&(Hu=tn()+500,Ko()))}break;case 13:pc((function(){var t=Ui(e,1);if(null!==t){var n=ac();ic(t,e,1,n)}})),Gc(e,1)}},Tn=function(e){if(13===e.tag){var t=Ui(e,134217728);if(null!==t)ic(t,e,134217728,ac());Gc(e,134217728)}},Pn=function(e){if(13===e.tag){var t=oc(e),n=Ui(e,t);if(null!==n)ic(n,e,t,ac());Gc(e,t)}},Cn=function(){return En},On=function(e,t){var n=En;try{return En=e,t()}finally{En=n}},jt=function(e,t,n){switch(t){case"input":if(nt(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=jo(r);if(!a)throw Error(ue(90));Ye(r),nt(r,a)}}}break;case"textarea":ut(e,n);break;case"select":null!=(t=n.value)&&it(e,!!n.multiple,t,!1)}},Rt=hc,It=pc;var ad={usingClientEntryPoint:!1,Events:[Eo,xo,jo,Ot,Nt,hc]},od={findFiberByHostInstance:So,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},id={bundleType:od.bundleType,version:od.version,rendererPackageName:od.rendererPackageName,rendererConfig:od.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ee.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Qt(e))?null:e.stateNode},findFiberByHostInstance:od.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var sd=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!sd.isDisabled&&sd.supportsFiber)try{un=sd.inject(id),cn=sd}catch(pt){}}re.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ad,re.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!ed(t))throw Error(ue(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:je,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},re.createRoot=function(e,t){if(!ed(e))throw Error(ue(299));var n=!1,r="",a=Yc;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Vc(e,1,!1,null,0,n,0,r,a),e[bo]=t.current,Ha(8===e.nodeType?e.parentNode:e),new Xc(t)},re.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(ue(188));throw e=Object.keys(e).join(","),Error(ue(268,e))}return e=null===(e=Qt(t))?null:e.stateNode},re.flushSync=function(e){return pc(e)},re.hydrate=function(e,t,n){if(!td(t))throw Error(ue(200));return rd(null,e,t,!0,n)},re.hydrateRoot=function(e,t,n){if(!ed(e))throw Error(ue(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",i=Yc;if(null!=n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Hc(t,null,e,1,null!=n?n:null,a,0,o,i),e[bo]=t.current,Ha(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Zc(t)},re.render=function(e,t,n){if(!td(t))throw Error(ue(200));return rd(null,e,t,!1,n)},re.unmountComponentAtNode=function(e){if(!td(e))throw Error(ue(40));return!!e._reactRootContainer&&(pc((function(){rd(null,null,e,!1,(function(){e._reactRootContainer=null,e[bo]=null}))})),!0)},re.unstable_batchedUpdates=hc,re.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!td(n))throw Error(ue(200));if(null==e||void 0===e._reactInternals)throw Error(ue(38));return rd(e,t,n,!1,r)},re.version="18.3.1-next-f1338f8080-20240426",function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),ne.exports=re;var ld=ne.exports,ud=ld;te.createRoot=ud.createRoot,te.hydrateRoot=ud.hydrateRoot;var cd={exports:{}},dd={},fd=V;var hd="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},pd=fd.useState,md=fd.useEffect,gd=fd.useLayoutEffect,yd=fd.useDebugValue;function vd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!hd(e,n)}catch(r){return!0}}var bd="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=pd({inst:{value:n,getSnapshot:t}}),a=r[0].inst,o=r[1];return gd((function(){a.value=n,a.getSnapshot=t,vd(a)&&o({inst:a})}),[e,n,t]),md((function(){return vd(a)&&o({inst:a}),e((function(){vd(a)&&o({inst:a})}))}),[e]),yd(n),n};dd.useSyncExternalStore=void 0!==fd.useSyncExternalStore?fd.useSyncExternalStore:bd,cd.exports=dd;var wd=cd.exports,_d={exports:{}},kd={},Sd=V,Ed=wd;var xd="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},jd=Ed.useSyncExternalStore,Td=Sd.useRef,Pd=Sd.useEffect,Cd=Sd.useMemo,Od=Sd.useDebugValue;kd.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var o=Td(null);if(null===o.current){var i={hasValue:!1,value:null};o.current=i}else i=o.current;o=Cd((function(){function e(e){if(!l){if(l=!0,o=e,e=r(e),void 0!==a&&i.hasValue){var t=i.value;if(a(t,e))return s=t}return s=e}if(t=s,xd(o,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(o=e,t):(o=e,s=n)}var o,s,l=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]}),[t,n,r,a]);var s=jd(e,o[0],o[1]);return Pd((function(){i.hasValue=!0,i.value=s}),[s]),Od(s),s},_d.exports=kd;var Nd=_d.exports;let Rd=function(e){e()};const Id=Symbol.for("react-redux-context"),Ld="undefined"!=typeof globalThis?globalThis:{};function $d(){var e;if(!V.createContext)return{};const t=null!=(e=Ld[Id])?e:Ld[Id]=new Map;let n=t.get(V.createContext);return n||(n=V.createContext(null),t.set(V.createContext,n)),n}const Ad=$d();function Dd(e=Ad){return function(){return V.useContext(e)}}const zd=Dd();let Ud=()=>{throw new Error("uSES not initialized!")};const Md=(e,t)=>e===t;function Fd(e=Ad){const t=e===Ad?zd:Dd(e);return function(e,n={}){const{equalityFn:r=Md,stabilityCheck:a,noopCheck:o}="function"==typeof n?{equalityFn:n}:n,{store:i,subscription:s,getServerState:l,stabilityCheck:u,noopCheck:c}=t();V.useRef(!0);const d=V.useCallback({[e.name]:t=>e(t)}[e.name],[e,u,a]),f=Ud(s.addNestedSub,i.getState,l||i.getState,d,r);return V.useDebugValue(f),f}}const Bd=Fd();var qd={exports:{}},Vd={},Wd="function"==typeof Symbol&&Symbol.for,Hd=Wd?Symbol.for("react.element"):60103,Kd=Wd?Symbol.for("react.portal"):60106,Jd=Wd?Symbol.for("react.fragment"):60107,Qd=Wd?Symbol.for("react.strict_mode"):60108,Gd=Wd?Symbol.for("react.profiler"):60114,Yd=Wd?Symbol.for("react.provider"):60109,Xd=Wd?Symbol.for("react.context"):60110,Zd=Wd?Symbol.for("react.async_mode"):60111,ef=Wd?Symbol.for("react.concurrent_mode"):60111,tf=Wd?Symbol.for("react.forward_ref"):60112,nf=Wd?Symbol.for("react.suspense"):60113,rf=Wd?Symbol.for("react.suspense_list"):60120,af=Wd?Symbol.for("react.memo"):60115,of=Wd?Symbol.for("react.lazy"):60116,sf=Wd?Symbol.for("react.block"):60121,lf=Wd?Symbol.for("react.fundamental"):60117,uf=Wd?Symbol.for("react.responder"):60118,cf=Wd?Symbol.for("react.scope"):60119;function df(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case Hd:switch(e=e.type){case Zd:case ef:case Jd:case Gd:case Qd:case nf:return e;default:switch(e=e&&e.$$typeof){case Xd:case tf:case of:case af:case Yd:return e;default:return t}}case Kd:return t}}}function ff(e){return df(e)===ef}Vd.AsyncMode=Zd,Vd.ConcurrentMode=ef,Vd.ContextConsumer=Xd,Vd.ContextProvider=Yd,Vd.Element=Hd,Vd.ForwardRef=tf,Vd.Fragment=Jd,Vd.Lazy=of,Vd.Memo=af,Vd.Portal=Kd,Vd.Profiler=Gd,Vd.StrictMode=Qd,Vd.Suspense=nf,Vd.isAsyncMode=function(e){return ff(e)||df(e)===Zd},Vd.isConcurrentMode=ff,Vd.isContextConsumer=function(e){return df(e)===Xd},Vd.isContextProvider=function(e){return df(e)===Yd},Vd.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===Hd},Vd.isForwardRef=function(e){return df(e)===tf},Vd.isFragment=function(e){return df(e)===Jd},Vd.isLazy=function(e){return df(e)===of},Vd.isMemo=function(e){return df(e)===af},Vd.isPortal=function(e){return df(e)===Kd},Vd.isProfiler=function(e){return df(e)===Gd},Vd.isStrictMode=function(e){return df(e)===Qd},Vd.isSuspense=function(e){return df(e)===nf},Vd.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===Jd||e===ef||e===Gd||e===Qd||e===nf||e===rf||"object"==typeof e&&null!==e&&(e.$$typeof===of||e.$$typeof===af||e.$$typeof===Yd||e.$$typeof===Xd||e.$$typeof===tf||e.$$typeof===lf||e.$$typeof===uf||e.$$typeof===cf||e.$$typeof===sf)},Vd.typeOf=df,qd.exports=Vd;var hf=qd.exports,pf={};pf[hf.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},pf[hf.Memo]={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0};var mf,gf={},yf=Symbol.for("react.element"),vf=Symbol.for("react.portal"),bf=Symbol.for("react.fragment"),wf=Symbol.for("react.strict_mode"),_f=Symbol.for("react.profiler"),kf=Symbol.for("react.provider"),Sf=Symbol.for("react.context"),Ef=Symbol.for("react.server_context"),xf=Symbol.for("react.forward_ref"),jf=Symbol.for("react.suspense"),Tf=Symbol.for("react.suspense_list"),Pf=Symbol.for("react.memo"),Cf=Symbol.for("react.lazy"),Of=Symbol.for("react.offscreen");
/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function Nf(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case yf:switch(e=e.type){case bf:case _f:case wf:case jf:case Tf:return e;default:switch(e=e&&e.$$typeof){case Ef:case Sf:case xf:case Cf:case Pf:case kf:return e;default:return t}}case vf:return t}}}function Rf(){const e=Rd;let t=null,n=null;return{clear(){t=null,n=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let r=!0,a=n={callback:e,next:null,prev:n};return a.prev?a.prev.next=a:t=a,function(){r&&null!==t&&(r=!1,a.next?a.next.prev=a.prev:n=a.prev,a.prev?a.prev.next=a.next:t=a.next)}}}}mf=Symbol.for("react.module.reference"),gf.ContextConsumer=Sf,gf.ContextProvider=kf,gf.Element=yf,gf.ForwardRef=xf,gf.Fragment=bf,gf.Lazy=Cf,gf.Memo=Pf,gf.Portal=vf,gf.Profiler=_f,gf.StrictMode=wf,gf.Suspense=jf,gf.SuspenseList=Tf,gf.isAsyncMode=function(){return!1},gf.isConcurrentMode=function(){return!1},gf.isContextConsumer=function(e){return Nf(e)===Sf},gf.isContextProvider=function(e){return Nf(e)===kf},gf.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===yf},gf.isForwardRef=function(e){return Nf(e)===xf},gf.isFragment=function(e){return Nf(e)===bf},gf.isLazy=function(e){return Nf(e)===Cf},gf.isMemo=function(e){return Nf(e)===Pf},gf.isPortal=function(e){return Nf(e)===vf},gf.isProfiler=function(e){return Nf(e)===_f},gf.isStrictMode=function(e){return Nf(e)===wf},gf.isSuspense=function(e){return Nf(e)===jf},gf.isSuspenseList=function(e){return Nf(e)===Tf},gf.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===bf||e===_f||e===wf||e===jf||e===Tf||e===Of||"object"==typeof e&&null!==e&&(e.$$typeof===Cf||e.$$typeof===Pf||e.$$typeof===kf||e.$$typeof===Sf||e.$$typeof===xf||e.$$typeof===mf||void 0!==e.getModuleId)},gf.typeOf=Nf;const If={notify(){},get:()=>[]};const Lf=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?V.useLayoutEffect:V.useEffect;function $f({store:e,context:t,children:n,serverState:r,stabilityCheck:a="once",noopCheck:o="once"}){const i=V.useMemo((()=>{const t=function(e,t){let n,r=If,a=0,o=!1;function i(){u.onStateChange&&u.onStateChange()}function s(){a++,n||(n=t?t.addNestedSub(i):e.subscribe(i),r=Rf())}function l(){a--,n&&0===a&&(n(),n=void 0,r.clear(),r=If)}const u={addNestedSub:function(e){s();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),l())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:i,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,s())},tryUnsubscribe:function(){o&&(o=!1,l())},getListeners:()=>r};return u}(e);return{store:e,subscription:t,getServerState:r?()=>r:void 0,stabilityCheck:a,noopCheck:o}}),[e,r,a,o]),s=V.useMemo((()=>e.getState()),[e]);Lf((()=>{const{subscription:t}=i;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),s!==e.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}}),[i,s]);const l=t||Ad;return V.createElement(l.Provider,{value:i},n)}function Af(e=Ad){const t=e===Ad?zd:Dd(e);return function(){const{store:e}=t();return e}}const Df=Af();function zf(e=Ad){const t=e===Ad?Df:Af(e);return function(){return t().dispatch}}const Uf=zf();var Mf,Ff;function Bf(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Bf(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function qf(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Bf(e))&&(r&&(r+=" "),r+=t);return r}Mf=Nd.useSyncExternalStoreWithSelector,Ud=Mf,Ff=ld.unstable_batchedUpdates,Rd=Ff;const Vf=e=>"number"==typeof e&&!isNaN(e),Wf=e=>"string"==typeof e,Hf=e=>"function"==typeof e,Kf=e=>Wf(e)||Hf(e)?e:null,Jf=e=>V.isValidElement(e)||Wf(e)||Hf(e)||Vf(e);function Qf(e){let{enter:t,exit:n,appendPosition:r=!1,collapse:a=!0,collapseDuration:o=300}=e;return function(e){let{children:i,position:s,preventExitTransition:l,done:u,nodeRef:c,isIn:d}=e;const f=r?`${t}--${s}`:t,h=r?`${n}--${s}`:n,p=V.useRef(0);return V.useLayoutEffect((()=>{const e=c.current,t=f.split(" "),n=r=>{r.target===c.current&&(e.dispatchEvent(new Event("d")),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===p.current&&"animationcancel"!==r.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)}),[]),V.useEffect((()=>{const e=c.current,t=()=>{e.removeEventListener("animationend",t),a?function(e,t,n){void 0===n&&(n=300);const{scrollHeight:r,style:a}=e;requestAnimationFrame((()=>{a.minHeight="initial",a.height=r+"px",a.transition=`all ${n}ms`,requestAnimationFrame((()=>{a.height="0",a.padding="0",a.margin="0",setTimeout(t,n)}))}))}(e,u,o):u()};d||(l?t():(p.current=1,e.className+=` ${h}`,e.addEventListener("animationend",t)))}),[d]),W.createElement(W.Fragment,null,i)}}function Gf(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}const Yf={list:new Map,emitQueue:new Map,on(e,t){return this.list.has(e)||this.list.set(e,[]),this.list.get(e).push(t),this},off(e,t){if(t){const n=this.list.get(e).filter((e=>e!==t));return this.list.set(e,n),this}return this.list.delete(e),this},cancelEmit(e){const t=this.emitQueue.get(e);return t&&(t.forEach(clearTimeout),this.emitQueue.delete(e)),this},emit(e){this.list.has(e)&&this.list.get(e).forEach((t=>{const n=setTimeout((()=>{t(...[].slice.call(arguments,1))}),0);this.emitQueue.has(e)||this.emitQueue.set(e,[]),this.emitQueue.get(e).push(n)}))}},Xf=e=>{let{theme:t,type:n,...r}=e;return W.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${n})`,...r})},Zf={info:function(e){return W.createElement(Xf,{...e},W.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return W.createElement(Xf,{...e},W.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return W.createElement(Xf,{...e},W.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return W.createElement(Xf,{...e},W.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return W.createElement("div",{className:"Toastify__spinner"})}};function eh(e){return e.targetTouches&&e.targetTouches.length>=1?e.targetTouches[0].clientX:e.clientX}function th(e){return e.targetTouches&&e.targetTouches.length>=1?e.targetTouches[0].clientY:e.clientY}function nh(e){let{closeToast:t,theme:n,ariaLabel:r="close"}=e;return W.createElement("button",{className:`Toastify__close-button Toastify__close-button--${n}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":r},W.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},W.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function rh(e){let{delay:t,isRunning:n,closeToast:r,type:a="default",hide:o,className:i,style:s,controlledProgress:l,progress:u,rtl:c,isIn:d,theme:f}=e;const h=o||l&&0===u,p={...s,animationDuration:`${t}ms`,animationPlayState:n?"running":"paused",opacity:h?0:1};l&&(p.transform=`scaleX(${u})`);const m=qf("Toastify__progress-bar",l?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${f}`,`Toastify__progress-bar--${a}`,{"Toastify__progress-bar--rtl":c}),g=Hf(i)?i({rtl:c,type:a,defaultClassName:m}):qf(m,i);return W.createElement("div",{role:"progressbar","aria-hidden":h?"true":"false","aria-label":"notification timer",className:g,style:p,[l&&u>=1?"onTransitionEnd":"onAnimationEnd"]:l&&u<1?null:()=>{d&&r()}})}const ah=e=>{const{isRunning:t,preventExitTransition:n,toastRef:r,eventHandlers:a}=function(e){const[t,n]=V.useState(!1),[r,a]=V.useState(!1),o=V.useRef(null),i=V.useRef({start:0,x:0,y:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,boundingRect:null,didMove:!1}).current,s=V.useRef(e),{autoClose:l,pauseOnHover:u,closeToast:c,onClick:d,closeOnClick:f}=e;function h(t){if(e.draggable){"touchstart"===t.nativeEvent.type&&t.nativeEvent.preventDefault(),i.didMove=!1,document.addEventListener("mousemove",y),document.addEventListener("mouseup",v),document.addEventListener("touchmove",y),document.addEventListener("touchend",v);const n=o.current;i.canCloseOnClick=!0,i.canDrag=!0,i.boundingRect=n.getBoundingClientRect(),n.style.transition="",i.x=eh(t.nativeEvent),i.y=th(t.nativeEvent),"x"===e.draggableDirection?(i.start=i.x,i.removalDistance=n.offsetWidth*(e.draggablePercent/100)):(i.start=i.y,i.removalDistance=n.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent/100))}}function p(t){if(i.boundingRect){const{top:n,bottom:r,left:a,right:o}=i.boundingRect;"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&i.x>=a&&i.x<=o&&i.y>=n&&i.y<=r?g():m()}}function m(){n(!0)}function g(){n(!1)}function y(n){const r=o.current;i.canDrag&&r&&(i.didMove=!0,t&&g(),i.x=eh(n),i.y=th(n),i.delta="x"===e.draggableDirection?i.x-i.start:i.y-i.start,i.start!==i.x&&(i.canCloseOnClick=!1),r.style.transform=`translate${e.draggableDirection}(${i.delta}px)`,r.style.opacity=""+(1-Math.abs(i.delta/i.removalDistance)))}function v(){document.removeEventListener("mousemove",y),document.removeEventListener("mouseup",v),document.removeEventListener("touchmove",y),document.removeEventListener("touchend",v);const t=o.current;if(i.canDrag&&i.didMove&&t){if(i.canDrag=!1,Math.abs(i.delta)>i.removalDistance)return a(!0),void e.closeToast();t.style.transition="transform 0.2s, opacity 0.2s",t.style.transform=`translate${e.draggableDirection}(0)`,t.style.opacity="1"}}V.useEffect((()=>{s.current=e})),V.useEffect((()=>(o.current&&o.current.addEventListener("d",m,{once:!0}),Hf(e.onOpen)&&e.onOpen(V.isValidElement(e.children)&&e.children.props),()=>{const e=s.current;Hf(e.onClose)&&e.onClose(V.isValidElement(e.children)&&e.children.props)})),[]),V.useEffect((()=>(e.pauseOnFocusLoss&&(document.hasFocus()||g(),window.addEventListener("focus",m),window.addEventListener("blur",g)),()=>{e.pauseOnFocusLoss&&(window.removeEventListener("focus",m),window.removeEventListener("blur",g))})),[e.pauseOnFocusLoss]);const b={onMouseDown:h,onTouchStart:h,onMouseUp:p,onTouchEnd:p};return l&&u&&(b.onMouseEnter=g,b.onMouseLeave=m),f&&(b.onClick=e=>{d&&d(e),i.canCloseOnClick&&c()}),{playToast:m,pauseToast:g,isRunning:t,preventExitTransition:r,toastRef:o,eventHandlers:b}}(e),{closeButton:o,children:i,autoClose:s,onClick:l,type:u,hideProgressBar:c,closeToast:d,transition:f,position:h,className:p,style:m,bodyClassName:g,bodyStyle:y,progressClassName:v,progressStyle:b,updateId:w,role:_,progress:k,rtl:S,toastId:E,deleteToast:x,isIn:j,isLoading:T,iconOut:P,closeOnClick:C,theme:O}=e,N=qf("Toastify__toast",`Toastify__toast-theme--${O}`,`Toastify__toast--${u}`,{"Toastify__toast--rtl":S},{"Toastify__toast--close-on-click":C}),R=Hf(p)?p({rtl:S,position:h,type:u,defaultClassName:N}):qf(N,p),I=!!k||!s,L={closeToast:d,type:u,theme:O};let $=null;return!1===o||($=Hf(o)?o(L):V.isValidElement(o)?V.cloneElement(o,L):nh(L)),W.createElement(f,{isIn:j,done:x,position:h,preventExitTransition:n,nodeRef:r},W.createElement("div",{id:E,onClick:l,className:R,...a,style:m,ref:r},W.createElement("div",{...j&&{role:_},className:Hf(g)?g({type:u}):qf("Toastify__toast-body",g),style:y},null!=P&&W.createElement("div",{className:qf("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!T})},P),W.createElement("div",null,i)),$,W.createElement(rh,{...w&&!I?{key:`pb-${w}`}:{},rtl:S,theme:O,delay:s,isRunning:t,isIn:j,closeToast:d,hide:c,type:u,style:b,className:v,controlledProgress:I,progress:k||0})))},oh=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},ih=Qf(oh("bounce",!0));Qf(oh("slide",!0)),Qf(oh("zoom")),Qf(oh("flip"));const sh=V.forwardRef(((e,t)=>{const{getToastToRender:n,containerRef:r,isToastActive:a}=function(e){const[,t]=V.useReducer((e=>e+1),0),[n,r]=V.useState([]),a=V.useRef(null),o=V.useRef(new Map).current,i=e=>-1!==n.indexOf(e),s=V.useRef({toastKey:1,displayedToast:0,count:0,queue:[],props:e,containerId:null,isToastActive:i,getToast:e=>o.get(e)}).current;function l(e){let{containerId:t}=e;const{limit:n}=s.props;!n||t&&s.containerId!==t||(s.count-=s.queue.length,s.queue=[])}function u(e){r((t=>null==e?[]:t.filter((t=>t!==e))))}function c(){const{toastContent:e,toastProps:t,staleId:n}=s.queue.shift();f(e,t,n)}function d(e,n){let{delay:r,staleId:i,...l}=n;if(!Jf(e)||(d=l,!a.current||s.props.enableMultiContainer&&d.containerId!==s.props.containerId||o.has(d.toastId)&&null==d.updateId))return;var d;const{toastId:h,updateId:p,data:m}=l,{props:g}=s,y=()=>u(h),v=null==p;v&&s.count++;const b={...g,style:g.toastStyle,key:s.toastKey++,...Object.fromEntries(Object.entries(l).filter((e=>{let[t,n]=e;return null!=n}))),toastId:h,updateId:p,data:m,closeToast:y,isIn:!1,className:Kf(l.className||g.toastClassName),bodyClassName:Kf(l.bodyClassName||g.bodyClassName),progressClassName:Kf(l.progressClassName||g.progressClassName),autoClose:!l.isLoading&&(w=l.autoClose,_=g.autoClose,!1===w||Vf(w)&&w>0?w:_),deleteToast(){const e=Gf(o.get(h),"removed");o.delete(h),Yf.emit(4,e);const n=s.queue.length;if(s.count=null==h?s.count-s.displayedToast:s.count-1,s.count<0&&(s.count=0),n>0){const e=null==h?s.props.limit:1;if(1===n||1===e)s.displayedToast++,c();else{const t=e>n?n:e;s.displayedToast=t;for(let e=0;e<t;e++)c()}}else t()}};var w,_;b.iconOut=function(e){let{theme:t,type:n,isLoading:r,icon:a}=e,o=null;const i={theme:t,type:n};return!1===a||(Hf(a)?o=a(i):V.isValidElement(a)?o=V.cloneElement(a,i):Wf(a)||Vf(a)?o=a:r?o=Zf.spinner():n in Zf&&(o=Zf[n](i))),o}(b),Hf(l.onOpen)&&(b.onOpen=l.onOpen),Hf(l.onClose)&&(b.onClose=l.onClose),b.closeButton=g.closeButton,!1===l.closeButton||Jf(l.closeButton)?b.closeButton=l.closeButton:!0===l.closeButton&&(b.closeButton=!Jf(g.closeButton)||g.closeButton);let k=e;V.isValidElement(e)&&!Wf(e.type)?k=V.cloneElement(e,{closeToast:y,toastProps:b,data:m}):Hf(e)&&(k=e({closeToast:y,toastProps:b,data:m})),g.limit&&g.limit>0&&s.count>g.limit&&v?s.queue.push({toastContent:k,toastProps:b,staleId:i}):Vf(r)?setTimeout((()=>{f(k,b,i)}),r):f(k,b,i)}function f(e,t,n){const{toastId:a}=t;n&&o.delete(n);const i={content:e,props:t};o.set(a,i),r((e=>[...e,a].filter((e=>e!==n)))),Yf.emit(4,Gf(i,null==i.props.updateId?"added":"updated"))}return V.useEffect((()=>(s.containerId=e.containerId,Yf.cancelEmit(3).on(0,d).on(1,(e=>a.current&&u(e))).on(5,l).emit(2,s),()=>{o.clear(),Yf.emit(3,s)})),[]),V.useEffect((()=>{s.props=e,s.isToastActive=i,s.displayedToast=n.length})),{getToastToRender:function(t){const n=new Map,r=Array.from(o.values());return e.newestOnTop&&r.reverse(),r.forEach((e=>{const{position:t}=e.props;n.has(t)||n.set(t,[]),n.get(t).push(e)})),Array.from(n,(e=>t(e[0],e[1])))},containerRef:a,isToastActive:i}}(e),{className:o,style:i,rtl:s,containerId:l}=e;function u(e){const t=qf("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":s});return Hf(o)?o({position:e,rtl:s,defaultClassName:t}):qf(t,Kf(o))}return V.useEffect((()=>{t&&(t.current=r.current)}),[]),W.createElement("div",{ref:r,className:"Toastify",id:l},n(((e,t)=>{const n=t.length?{...i}:{...i,pointerEvents:"none"};return W.createElement("div",{className:u(e),style:n,key:`container-${e}`},t.map(((e,n)=>{let{content:r,props:o}=e;return W.createElement(ah,{...o,isIn:a(o.toastId),style:{...o.style,"--nth":n+1,"--len":t.length},key:`toast-${o.key}`},r)})))})))}));sh.displayName="ToastContainer",sh.defaultProps={position:"top-right",transition:ih,autoClose:5e3,closeButton:nh,pauseOnHover:!0,pauseOnFocusLoss:!0,closeOnClick:!0,draggable:!0,draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};let lh,uh=new Map,ch=[],dh=1;function fh(){return""+dh++}function hh(e){return e&&(Wf(e.toastId)||Vf(e.toastId))?e.toastId:fh()}function ph(e,t){return uh.size>0?Yf.emit(0,e,t):ch.push({content:e,options:t}),t.toastId}function mh(e,t){return{...t,type:t&&t.type||e,toastId:hh(t)}}function gh(e){return(t,n)=>ph(t,mh(e,n))}function yh(e,t){return ph(e,mh("default",t))}
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function vh(){return vh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},vh.apply(this,arguments)}var bh,wh;yh.loading=(e,t)=>ph(e,mh("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),yh.promise=function(e,t,n){let r,{pending:a,error:o,success:i}=t;a&&(r=Wf(a)?yh.loading(a,n):yh.loading(a.render,{...n,...a}));const s={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},l=(e,t,a)=>{if(null==t)return void yh.dismiss(r);const o={type:e,...s,...n,data:a},i=Wf(t)?{render:t}:t;return r?yh.update(r,{...o,...i}):yh(i.render,{...o,...i}),a},u=Hf(e)?e():e;return u.then((e=>l("success",i,e))).catch((e=>l("error",o,e))),u},yh.success=gh("success"),yh.info=gh("info"),yh.error=gh("error"),yh.warning=gh("warning"),yh.warn=yh.warning,yh.dark=(e,t)=>ph(e,mh("default",{theme:"dark",...t})),yh.dismiss=e=>{uh.size>0?Yf.emit(1,e):ch=ch.filter((t=>null!=e&&t.options.toastId!==e))},yh.clearWaitingQueue=function(e){return void 0===e&&(e={}),Yf.emit(5,e)},yh.isActive=e=>{let t=!1;return uh.forEach((n=>{n.isToastActive&&n.isToastActive(e)&&(t=!0)})),t},yh.update=function(e,t){void 0===t&&(t={}),setTimeout((()=>{const n=function(e,t){let{containerId:n}=t;const r=uh.get(n||lh);return r&&r.getToast(e)}(e,t);if(n){const{props:r,content:a}=n,o={delay:100,...r,...t,toastId:t.toastId||e,updateId:fh()};o.toastId!==e&&(o.staleId=e);const i=o.render||a;delete o.render,ph(i,o)}}),0)},yh.done=e=>{yh.update(e,{progress:1})},yh.onChange=e=>(Yf.on(4,e),()=>{Yf.off(4,e)}),yh.POSITION={TOP_LEFT:"top-left",TOP_RIGHT:"top-right",TOP_CENTER:"top-center",BOTTOM_LEFT:"bottom-left",BOTTOM_RIGHT:"bottom-right",BOTTOM_CENTER:"bottom-center"},yh.TYPE={INFO:"info",SUCCESS:"success",WARNING:"warning",ERROR:"error",DEFAULT:"default"},Yf.on(2,(e=>{lh=e.containerId||e,uh.set(lh,e),ch.forEach((e=>{Yf.emit(0,e.content,e.options)})),ch=[]})).on(3,(e=>{uh.delete(e.containerId||e),0===uh.size&&Yf.off(0).off(1).off(5)})),(wh=bh||(bh={})).Pop="POP",wh.Push="PUSH",wh.Replace="REPLACE";const _h="popstate";function kh(e){return void 0===e&&(e={}),function(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,s=bh.Pop,l=null,u=c();null==u&&(u=0,i.replaceState(vh({},i.state,{idx:u}),""));function c(){return(i.state||{idx:null}).idx}function d(){s=bh.Pop;let e=c(),t=null==e?null:e-u;u=e,l&&l({action:s,location:m.location,delta:t})}function f(e,t){s=bh.Push;let r=jh(m.location,e,t);n&&n(r,e),u=c()+1;let d=xh(r,u),f=m.createHref(r);try{i.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(f)}o&&l&&l({action:s,location:m.location,delta:1})}function h(e,t){s=bh.Replace;let r=jh(m.location,e,t);n&&n(r,e),u=c();let a=xh(r,u),d=m.createHref(r);i.replaceState(a,"",d),o&&l&&l({action:s,location:m.location,delta:0})}function p(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"==typeof e?e:Th(e);return n=n.replace(/ $/,"%20"),Sh(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let m={get action(){return s},get location(){return e(a,i)},listen(e){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(_h,d),l=e,()=>{a.removeEventListener(_h,d),l=null}},createHref:e=>t(a,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:f,replace:h,go:e=>i.go(e)};return m}((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return jh("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:Th(t)}),null,e)}function Sh(e,t){if(!1===e||null==e)throw new Error(t)}function Eh(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function xh(e,t){return{usr:e.state,key:e.key,idx:t}}function jh(e,t,n,r){return void 0===n&&(n=null),vh({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?Ph(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function Th(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function Ph(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}var Ch,Oh;function Nh(e,t,n){return void 0===n&&(n="/"),function(e,t,n,r){let a="string"==typeof t?Ph(t):t,o=Wh(a.pathname||"/",n);if(null==o)return null;let i=Rh(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(i);let s=null;for(let l=0;null==s&&l<i.length;++l){let e=Vh(o);s=Bh(i[l],e,r)}return s}(e,t,n,!1)}function Rh(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(Sh(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let s=Qh([r,i.relativePath]),l=n.concat(i);e.children&&e.children.length>0&&(Sh(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),Rh(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:Fh(s,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of Ih(e.path))a(e,t,r);else a(e,t)})),t}function Ih(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=Ih(r.join("/")),s=[];return s.push(...i.map((e=>""===e?o:[o,e].join("/")))),a&&s.push(...i),s.map((t=>e.startsWith("/")&&""===t?"/":t))}(Oh=Ch||(Ch={})).data="data",Oh.deferred="deferred",Oh.redirect="redirect",Oh.error="error";const Lh=/^:[\w-]+$/,$h=3,Ah=2,Dh=1,zh=10,Uh=-2,Mh=e=>"*"===e;function Fh(e,t){let n=e.split("/"),r=n.length;return n.some(Mh)&&(r+=Uh),t&&(r+=Ah),n.filter((e=>!Mh(e))).reduce(((e,t)=>e+(Lh.test(t)?$h:""===t?Dh:zh)),r)}function Bh(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},o="/",i=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=qh({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},u),d=e.route;if(!c&&l&&n&&!r[r.length-1].route.index&&(c=qh({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:Qh([o,c.pathname]),pathnameBase:Gh(Qh([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=Qh([o,c.pathnameBase]))}return i}function qh(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);Eh("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=s[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:i,pattern:e}}function Vh(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return Eh(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Wh(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function Hh(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Kh(e,t){let n=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function Jh(e,t,n,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=Ph(e):(a=vh({},e),Sh(!a.pathname||!a.pathname.includes("?"),Hh("?","pathname","search",a)),Sh(!a.pathname||!a.pathname.includes("#"),Hh("#","pathname","hash",a)),Sh(!a.search||!a.search.includes("#"),Hh("#","search","hash",a)));let o,i=""===e||""===a.pathname,s=i?"/":a.pathname;if(null==s)o=n;else{let e=t.length-1;if(!r&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?Ph(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:Yh(r),hash:Xh(a)}}(a,o),u=s&&"/"!==s&&s.endsWith("/"),c=(i||"."===s)&&n.endsWith("/");return l.pathname.endsWith("/")||!u&&!c||(l.pathname+="/"),l}const Qh=e=>e.join("/").replace(/\/\/+/g,"/"),Gh=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Yh=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Xh=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const Zh=["post","put","patch","delete"];new Set(Zh);const ep=["get",...Zh];
/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function tp(){return tp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},tp.apply(this,arguments)}new Set(ep);const np=V.createContext(null),rp=V.createContext(null),ap=V.createContext(null),op=V.createContext(null),ip=V.createContext({outlet:null,matches:[],isDataRoute:!1}),sp=V.createContext(null);function lp(){return null!=V.useContext(op)}function up(){return lp()||Sh(!1),V.useContext(op).location}function cp(e){V.useContext(ap).static||V.useLayoutEffect(e)}function dp(){let{isDataRoute:e}=V.useContext(ip);return e?function(){let{router:e}=function(){let e=V.useContext(np);return e||Sh(!1),e}(wp.UseNavigateStable),t=kp(_p.UseNavigateStable),n=V.useRef(!1);return cp((()=>{n.current=!0})),V.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"==typeof r?e.navigate(r):e.navigate(r,tp({fromRouteId:t},a)))}),[e,t])}():function(){lp()||Sh(!1);let e=V.useContext(np),{basename:t,future:n,navigator:r}=V.useContext(ap),{matches:a}=V.useContext(ip),{pathname:o}=up(),i=JSON.stringify(Kh(a,n.v7_relativeSplatPath)),s=V.useRef(!1);return cp((()=>{s.current=!0})),V.useCallback((function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"==typeof n)return void r.go(n);let l=Jh(n,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:Qh([t,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)}),[t,r,i,o,e])}()}const fp=V.createContext(null);function hp(){let{matches:e}=V.useContext(ip),t=e[e.length-1];return t?t.params:{}}function pp(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=V.useContext(ap),{matches:a}=V.useContext(ip),{pathname:o}=up(),i=JSON.stringify(Kh(a,r.v7_relativeSplatPath));return V.useMemo((()=>Jh(e,JSON.parse(i),o,"path"===n)),[e,i,o,n])}function mp(e,t){return function(e,t,n,r){lp()||Sh(!1);let{navigator:a,static:o}=V.useContext(ap),{matches:i}=V.useContext(ip),s=i[i.length-1],l=s?s.params:{};!s||s.pathname;let u=s?s.pathnameBase:"/";s&&s.route;let c,d=up();if(t){var f;let e="string"==typeof t?Ph(t):t;"/"===u||(null==(f=e.pathname)?void 0:f.startsWith(u))||Sh(!1),c=e}else c=d;let h=c.pathname||"/",p=h;if("/"!==u){let e=u.replace(/^\//,"").split("/");p="/"+h.replace(/^\//,"").split("/").slice(e.length).join("/")}let m=!o&&n&&n.matches&&n.matches.length>0?n.matches:Nh(e,{pathname:p}),g=function(e,t,n,r){var a;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===r&&(r=null);if(null==e){var o;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(o=r)&&o.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(a=n)?void 0:a.errors;if(null!=s){let e=i.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||Sh(!1),i=i.slice(0,Math.min(i.length,e+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<i.length;c++){let e=i[c];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(u=c),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){l=!0,i=u>=0?i.slice(0,u+1):[i[0]];break}}}return i.reduceRight(((e,r,a)=>{let o,c=!1,d=null,f=null;var h;n&&(o=s&&r.route.id?s[r.route.id]:void 0,d=r.route.errorElement||yp,l&&(u<0&&0===a?(h="route-fallback",!1||Sp[h]||(Sp[h]=!0),c=!0,f=null):u===a&&(c=!0,f=r.route.hydrateFallbackElement||null)));let p=t.concat(i.slice(0,a+1)),m=()=>{let t;return t=o?d:c?f:r.route.Component?V.createElement(r.route.Component,null):r.route.element?r.route.element:e,V.createElement(bp,{match:r,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?V.createElement(vp,{location:n.location,revalidation:n.revalidation,component:d,error:o,children:m(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):m()}),null)}(m&&m.map((e=>Object.assign({},e,{params:Object.assign({},l,e.params),pathname:Qh([u,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?u:Qh([u,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,n,r);if(t&&g)return V.createElement(op.Provider,{value:{location:tp({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:bh.Pop}},g);return g}(e,t)}function gp(){let e=function(){var e;let t=V.useContext(sp),n=function(){let e=V.useContext(rp);return e||Sh(!1),e}(_p.UseRouteError),r=kp(_p.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return V.createElement(V.Fragment,null,V.createElement("h2",null,"Unexpected Application Error!"),V.createElement("h3",{style:{fontStyle:"italic"}},t),n?V.createElement("pre",{style:r},n):null,null)}const yp=V.createElement(gp,null);class vp extends V.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?V.createElement(ip.Provider,{value:this.props.routeContext},V.createElement(sp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function bp(e){let{routeContext:t,match:n,children:r}=e,a=V.useContext(np);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),V.createElement(ip.Provider,{value:t},r)}var wp=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(wp||{}),_p=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(_p||{});function kp(e){let t=function(){let e=V.useContext(ip);return e||Sh(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||Sh(!1),n.route.id}const Sp={};function Ep(e){let{to:t,replace:n,state:r,relative:a}=e;lp()||Sh(!1);let{future:o,static:i}=V.useContext(ap),{matches:s}=V.useContext(ip),{pathname:l}=up(),u=dp(),c=Jh(t,Kh(s,o.v7_relativeSplatPath),l,"path"===a),d=JSON.stringify(c);return V.useEffect((()=>u(JSON.parse(d),{replace:n,state:r,relative:a})),[u,d,a,n,r]),null}function xp(e){return function(e){let t=V.useContext(ip).outlet;return t?V.createElement(fp.Provider,{value:e},t):t}(e.context)}function jp(e){Sh(!1)}function Tp(e){let{basename:t="/",children:n=null,location:r,navigationType:a=bh.Pop,navigator:o,static:i=!1,future:s}=e;lp()&&Sh(!1);let l=t.replace(/^\/*/,"/"),u=V.useMemo((()=>({basename:l,navigator:o,static:i,future:tp({v7_relativeSplatPath:!1},s)})),[l,s,o,i]);"string"==typeof r&&(r=Ph(r));let{pathname:c="/",search:d="",hash:f="",state:h=null,key:p="default"}=r,m=V.useMemo((()=>{let e=Wh(c,l);return null==e?null:{location:{pathname:e,search:d,hash:f,state:h,key:p},navigationType:a}}),[l,c,d,f,h,p,a]);return null==m?null:V.createElement(ap.Provider,{value:u},V.createElement(op.Provider,{children:n,value:m}))}function Pp(e){let{children:t,location:n}=e;return mp(Cp(t),n)}function Cp(e,t){void 0===t&&(t=[]);let n=[];return V.Children.forEach(e,((e,r)=>{if(!V.isValidElement(e))return;let a=[...t,r];if(e.type===V.Fragment)return void n.push.apply(n,Cp(e.props.children,a));e.type!==jp&&Sh(!1),e.props.index&&e.props.children&&Sh(!1);let o={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=Cp(e.props.children,a)),n.push(o)})),n}
/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Op(){return Op=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Op.apply(this,arguments)}new Promise((()=>{}));const Np=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(ok){}const Rp=H.startTransition;function Ip(e){let{basename:t,children:n,future:r,window:a}=e,o=V.useRef();null==o.current&&(o.current=kh({window:a,v5Compat:!0}));let i=o.current,[s,l]=V.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},c=V.useCallback((e=>{u&&Rp?Rp((()=>l(e))):l(e)}),[l,u]);return V.useLayoutEffect((()=>i.listen(c)),[i,c]),V.useEffect((()=>{return null==(e=r)||e.v7_startTransition,void 0!==(null==e?void 0:e.v7_relativeSplatPath)||t&&t.v7_relativeSplatPath,void(t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation));var e,t}),[r]),V.createElement(Tp,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:i,future:r})}const Lp="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,$p=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ap=V.forwardRef((function(e,t){let n,{onClick:r,relative:a,reloadDocument:o,replace:i,state:s,target:l,to:u,preventScrollReset:c,viewTransition:d}=e,f=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,Np),{basename:h}=V.useContext(ap),p=!1;if("string"==typeof u&&$p.test(u)&&(n=u,Lp))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),n=Wh(t.pathname,h);t.origin===e.origin&&null!=n?u=n+t.search+t.hash:p=!0}catch(ok){}let m=function(e,t){let{relative:n}=void 0===t?{}:t;lp()||Sh(!1);let{basename:r,navigator:a}=V.useContext(ap),{hash:o,pathname:i,search:s}=pp(e,{relative:n}),l=i;return"/"!==r&&(l="/"===i?r:Qh([r,i])),a.createHref({pathname:l,search:s,hash:o})}(u,{relative:a}),g=function(e,t){let{target:n,replace:r,state:a,preventScrollReset:o,relative:i,viewTransition:s}=void 0===t?{}:t,l=dp(),u=up(),c=pp(e,{relative:i});return V.useCallback((t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,n)){t.preventDefault();let n=void 0!==r?r:Th(u)===Th(c);l(e,{replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:s})}}),[u,l,c,r,a,n,e,o,i,s])}(u,{replace:i,state:s,target:l,preventScrollReset:c,relative:a,viewTransition:d});return V.createElement("a",Op({},f,{href:n||m,onClick:p||o?r:function(e){r&&r(e),e.defaultPrevented||g(e)},ref:t,target:l}))}));var Dp,zp,Up,Mp;(zp=Dp||(Dp={})).UseScrollRestoration="useScrollRestoration",zp.UseSubmit="useSubmit",zp.UseSubmitFetcher="useSubmitFetcher",zp.UseFetcher="useFetcher",zp.useViewTransitionState="useViewTransitionState",(Mp=Up||(Up={})).UseFetcher="useFetcher",Mp.UseFetchers="useFetchers",Mp.UseScrollRestoration="useScrollRestoration";const Fp={},Bp=function(e,t,n){if(!t||0===t.length)return e();const r=document.getElementsByTagName("link");return Promise.all(t.map((e=>{if((e=function(e){return"/"+e}(e))in Fp)return;Fp[e]=!0;const t=e.endsWith(".css"),a=t?'[rel="stylesheet"]':"";if(!!n)for(let n=r.length-1;n>=0;n--){const a=r[n];if(a.href===e&&(!t||"stylesheet"===a.rel))return}else if(document.querySelector(`link[href="${e}"]${a}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script",o.crossOrigin=""),o.href=e,document.head.appendChild(o),t?new Promise(((t,n)=>{o.addEventListener("load",t),o.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0}))).then((()=>e())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))},qp=({children:e})=>{const{isAuthenticated:t,loading:n}=Bd((e=>e.auth));return n?ee.jsx("div",{className:"flex justify-center items-center h-screen",children:"Cargando..."}):t?e:ee.jsx(Ep,{to:"/login",replace:!0})},Vp=({children:e})=>{const{user:t,isAuthenticated:n,loading:r}=Bd((e=>e.auth));return console.log("AdminRoute component",{user:t,isAuthenticated:n,loading:r}),r?ee.jsx("div",{className:"flex justify-center items-center h-screen",children:"Cargando..."}):n?t&&"admin"===t.role?e:(console.log("Usuario no es admin, redirigiendo",{user:t}),ee.jsx(Ep,{to:"/",replace:!0})):(console.log("Usuario no está autenticado, redirigiendo a login"),ee.jsx(Ep,{to:"/login",replace:!0}))},Wp=({children:e})=>{const{user:t,loading:n}=Bd((e=>e.auth));return n?ee.jsx("div",{className:"flex justify-center items-center h-screen",children:"Cargando..."}):!t||"professional"!==t.role&&"admin"!==t.role?ee.jsx(Ep,{to:"/",replace:!0}):e},Hp=({children:e})=>{const{user:t,loading:n}=Bd((e=>e.auth));return n?ee.jsx("div",{className:"flex justify-center items-center h-screen",children:"Cargando..."}):t?e:ee.jsx(Ep,{to:"/login",replace:!0})};function Kp(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function Jp(e){return!!e&&!!e[Lm]}function Qp(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===$m}(e)||Array.isArray(e)||!!e[Im]||!!(null===(t=e.constructor)||void 0===t?void 0:t[Im])||tm(e)||nm(e))}function Gp(e,t,n){void 0===n&&(n=!1),0===Yp(e)?(n?Object.keys:Am)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e)})):e.forEach((function(n,r){return t(r,n,e)}))}function Yp(e){var t=e[Lm];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:tm(e)?2:nm(e)?3:0}function Xp(e,t){return 2===Yp(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function Zp(e,t,n){var r=Yp(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function em(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function tm(e){return Cm&&e instanceof Map}function nm(e){return Om&&e instanceof Set}function rm(e){return e.o||e.t}function am(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=Dm(e);delete t[Lm];for(var n=Am(t),r=0;r<n.length;r++){var a=n[r],o=t[a];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[a]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[a]})}return Object.create(Object.getPrototypeOf(e),t)}function om(e,t){return void 0===t&&(t=!1),sm(e)||Jp(e)||!Qp(e)||(Yp(e)>1&&(e.set=e.add=e.clear=e.delete=im),Object.freeze(e),t&&Gp(e,(function(e,t){return om(t,!0)}),!0)),e}function im(){Kp(2)}function sm(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function lm(e){var t=zm[e];return t||Kp(18,e),t}function um(){return Tm}function cm(e,t){t&&(lm("Patches"),e.u=[],e.s=[],e.v=t)}function dm(e){fm(e),e.p.forEach(pm),e.p=null}function fm(e){e===Tm&&(Tm=e.l)}function hm(e){return Tm={p:[],l:Tm,h:e,m:!0,_:0}}function pm(e){var t=e[Lm];0===t.i||1===t.i?t.j():t.g=!0}function mm(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.O||lm("ES5").S(t,e,r),r?(n[Lm].P&&(dm(t),Kp(4)),Qp(e)&&(e=gm(t,e),t.l||vm(t,e)),t.u&&lm("Patches").M(n[Lm].t,e,t.u,t.s)):e=gm(t,n,[]),dm(t),t.u&&t.v(t.u,t.s),e!==Rm?e:void 0}function gm(e,t,n){if(sm(t))return t;var r=t[Lm];if(!r)return Gp(t,(function(a,o){return ym(e,r,t,a,o,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return vm(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var a=4===r.i||5===r.i?r.o=am(r.k):r.o,o=a,i=!1;3===r.i&&(o=new Set(a),a.clear(),i=!0),Gp(o,(function(t,o){return ym(e,r,a,t,o,n,i)})),vm(e,a,!1),n&&e.u&&lm("Patches").N(r,n,e.u,e.s)}return r.o}function ym(e,t,n,r,a,o,i){if(Jp(a)){var s=gm(e,a,o&&t&&3!==t.i&&!Xp(t.R,r)?o.concat(r):void 0);if(Zp(n,r,s),!Jp(s))return;e.m=!1}else i&&n.add(a);if(Qp(a)&&!sm(a)){if(!e.h.D&&e._<1)return;gm(e,a),t&&t.A.l||vm(e,a)}}function vm(e,t,n){void 0===n&&(n=!1),!e.l&&e.h.D&&e.m&&om(t,n)}function bm(e,t){var n=e[Lm];return(n?rm(n):e)[t]}function wm(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function _m(e){e.P||(e.P=!0,e.l&&_m(e.l))}function km(e){e.o||(e.o=am(e.t))}function Sm(e,t,n){var r=tm(t)?lm("MapSet").F(t,n):nm(t)?lm("MapSet").T(t,n):e.O?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:um(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},a=r,o=Um;n&&(a=[r],o=Mm);var i=Proxy.revocable(a,o),s=i.revoke,l=i.proxy;return r.k=l,r.j=s,l}(t,n):lm("ES5").J(t,n);return(n?n.A:um()).p.push(r),r}function Em(e){return Jp(e)||Kp(22,e),function e(t){if(!Qp(t))return t;var n,r=t[Lm],a=Yp(t);if(r){if(!r.P&&(r.i<4||!lm("ES5").K(r)))return r.t;r.I=!0,n=xm(t,a),r.I=!1}else n=xm(t,a);return Gp(n,(function(t,a){r&&function(e,t){return 2===Yp(e)?e.get(t):e[t]}(r.t,t)===a||Zp(n,t,e(a))})),3===a?new Set(n):n}(e)}function xm(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return am(e)}var jm,Tm,Pm="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Cm="undefined"!=typeof Map,Om="undefined"!=typeof Set,Nm="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,Rm=Pm?Symbol.for("immer-nothing"):((jm={})["immer-nothing"]=!0,jm),Im=Pm?Symbol.for("immer-draftable"):"__$immer_draftable",Lm=Pm?Symbol.for("immer-state"):"__$immer_state",$m=""+Object.prototype.constructor,Am="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,Dm=Object.getOwnPropertyDescriptors||function(e){var t={};return Am(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)})),t},zm={},Um={get:function(e,t){if(t===Lm)return e;var n,r,a,o=rm(e);if(!Xp(o,t))return n=e,(a=wm(o,t))?"value"in a?a.value:null===(r=a.get)||void 0===r?void 0:r.call(n.k):void 0;var i=o[t];return e.I||!Qp(i)?i:i===bm(e.t,t)?(km(e),e.o[t]=Sm(e.A.h,i,e)):i},has:function(e,t){return t in rm(e)},ownKeys:function(e){return Reflect.ownKeys(rm(e))},set:function(e,t,n){var r=wm(rm(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var a=bm(rm(e),t),o=null==a?void 0:a[Lm];if(o&&o.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(em(n,a)&&(void 0!==n||Xp(e.t,t)))return!0;km(e),_m(e)}return e.o[t]===n&&(void 0!==n||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==bm(e.t,t)||t in e.t?(e.R[t]=!1,km(e),_m(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=rm(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){Kp(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){Kp(12)}},Mm={};Gp(Um,(function(e,t){Mm[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),Mm.deleteProperty=function(e,t){return Mm.set.call(this,e,t,void 0)},Mm.set=function(e,t,n){return Um.set.call(this,e[0],t,n,e[0])};var Fm=function(){function e(e){var t=this;this.O=Nm,this.D=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var a=n;n=e;var o=t;return function(e){var t=this;void 0===e&&(e=a);for(var r=arguments.length,i=Array(r>1?r-1:0),s=1;s<r;s++)i[s-1]=arguments[s];return o.produce(e,(function(e){var r;return(r=n).call.apply(r,[t,e].concat(i))}))}}var i;if("function"!=typeof n&&Kp(6),void 0!==r&&"function"!=typeof r&&Kp(7),Qp(e)){var s=hm(t),l=Sm(t,e,void 0),u=!0;try{i=n(l),u=!1}finally{u?dm(s):fm(s)}return"undefined"!=typeof Promise&&i instanceof Promise?i.then((function(e){return cm(s,r),mm(e,s)}),(function(e){throw dm(s),e})):(cm(s,r),mm(i,s))}if(!e||"object"!=typeof e){if(void 0===(i=n(e))&&(i=e),i===Rm&&(i=void 0),t.D&&om(i,!0),r){var c=[],d=[];lm("Patches").M(e,i,c,d),r(c,d)}return i}Kp(21,e)},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,a=Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(a))}))};var r,a,o=t.produce(e,n,(function(e,t){r=e,a=t}));return"undefined"!=typeof Promise&&o instanceof Promise?o.then((function(e){return[e,r,a]})):[o,r,a]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){Qp(e)||Kp(8),Jp(e)&&(e=Em(e));var t=hm(this),n=Sm(this,e,void 0);return n[Lm].C=!0,fm(t),n},t.finishDraft=function(e,t){var n=(e&&e[Lm]).A;return cm(n,t),mm(void 0,n)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!Nm&&Kp(20),this.O=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var a=lm("Patches").$;return Jp(e)?a(e,t):this.produce(e,(function(e){return a(e,t)}))},e}(),Bm=new Fm,qm=Bm.produce;function Vm(e){return(Vm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Wm(e){var t=function(e,t){if("object"!=Vm(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Vm(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Vm(t)?t:t+""}function Hm(e,t,n){return(t=Wm(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Km(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Jm(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Km(Object(n),!0).forEach((function(t){Hm(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Km(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Qm(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}Bm.produceWithPatches.bind(Bm),Bm.setAutoFreeze.bind(Bm),Bm.setUseProxies.bind(Bm),Bm.applyPatches.bind(Bm),Bm.createDraft.bind(Bm),Bm.finishDraft.bind(Bm);var Gm="function"==typeof Symbol&&Symbol.observable||"@@observable",Ym=function(){return Math.random().toString(36).substring(7).split("").join(".")},Xm={INIT:"@@redux/INIT"+Ym(),REPLACE:"@@redux/REPLACE"+Ym(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Ym()}};function Zm(e,t,n){var r;if("function"==typeof t&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw new Error(Qm(0));if("function"==typeof t&&void 0===n&&(n=t,t=void 0),void 0!==n){if("function"!=typeof n)throw new Error(Qm(1));return n(Zm)(e,t)}if("function"!=typeof e)throw new Error(Qm(2));var a=e,o=t,i=[],s=i,l=!1;function u(){s===i&&(s=i.slice())}function c(){if(l)throw new Error(Qm(3));return o}function d(e){if("function"!=typeof e)throw new Error(Qm(4));if(l)throw new Error(Qm(5));var t=!0;return u(),s.push(e),function(){if(t){if(l)throw new Error(Qm(6));t=!1,u();var n=s.indexOf(e);s.splice(n,1),i=null}}}function f(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw new Error(Qm(7));if(void 0===e.type)throw new Error(Qm(8));if(l)throw new Error(Qm(9));try{l=!0,o=a(o,e)}finally{l=!1}for(var t=i=s,n=0;n<t.length;n++){(0,t[n])()}return e}return f({type:Xm.INIT}),(r={dispatch:f,subscribe:d,getState:c,replaceReducer:function(e){if("function"!=typeof e)throw new Error(Qm(10));a=e,f({type:Xm.REPLACE})}})[Gm]=function(){var e,t=d;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(Qm(11));function n(){e.next&&e.next(c())}return n(),{unsubscribe:t(n)}}})[Gm]=function(){return this},e},r}function eg(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var a=t[r];"function"==typeof e[a]&&(n[a]=e[a])}var o,i=Object.keys(n);try{!function(e){Object.keys(e).forEach((function(t){var n=e[t];if(void 0===n(void 0,{type:Xm.INIT}))throw new Error(Qm(12));if(void 0===n(void 0,{type:Xm.PROBE_UNKNOWN_ACTION()}))throw new Error(Qm(13))}))}(n)}catch(ok){o=ok}return function(e,t){if(void 0===e&&(e={}),o)throw o;for(var r=!1,a={},s=0;s<i.length;s++){var l=i[s],u=n[l],c=e[l],d=u(c,t);if(void 0===d)throw t&&t.type,new Error(Qm(14));a[l]=d,r=r||d!==c}return(r=r||i.length!==Object.keys(e).length)?a:e}}function tg(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function ng(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(Qm(15))},a={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},o=t.map((function(e){return e(a)}));return r=tg.apply(void 0,o)(n.dispatch),Jm(Jm({},n),{},{dispatch:r})}}}function rg(e){return function(t){var n=t.dispatch,r=t.getState;return function(t){return function(a){return"function"==typeof a?a(n,r,e):t(a)}}}}var ag=rg();ag.withExtraArgument=rg;const og=ag;var ig,sg=globalThis&&globalThis.__extends||(ig=function(e,t){return(ig=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}ig(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),lg=globalThis&&globalThis.__generator||function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(ok){o=[6,ok],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}},ug=globalThis&&globalThis.__spreadArray||function(e,t){for(var n=0,r=t.length,a=e.length;n<r;n++,a++)e[a]=t[n];return e},cg=Object.defineProperty,dg=Object.defineProperties,fg=Object.getOwnPropertyDescriptors,hg=Object.getOwnPropertySymbols,pg=Object.prototype.hasOwnProperty,mg=Object.prototype.propertyIsEnumerable,gg=function(e,t,n){return t in e?cg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},yg=function(e,t){for(var n in t||(t={}))pg.call(t,n)&&gg(e,n,t[n]);if(hg)for(var r=0,a=hg(t);r<a.length;r++){n=a[r];mg.call(t,n)&&gg(e,n,t[n])}return e},vg=function(e,t){return dg(e,fg(t))},bg="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?tg:tg.apply(null,arguments)};function wg(e,t){function n(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(t){var a=t.apply(void 0,n);if(!a)throw new Error("prepareAction did not return an object");return yg(yg({type:e,payload:a.payload},"meta"in a&&{meta:a.meta}),"error"in a&&{error:a.error})}return{type:e,payload:n[0]}}return n.toString=function(){return""+e},n.type=e,n.match=function(t){return t.type===e},n}var _g=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return sg(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,ug([void 0],e[0].concat(this)))):new(t.bind.apply(t,ug([void 0],e.concat(this))))},t}(Array),kg=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return sg(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,ug([void 0],e[0].concat(this)))):new(t.bind.apply(t,ug([void 0],e.concat(this))))},t}(Array);function Sg(e){return Qp(e)?qm(e,(function(){})):e}function Eg(){return function(e){return function(e){void 0===e&&(e={});var t=e.thunk,n=void 0===t||t;e.immutableCheck,e.serializableCheck,e.actionCreatorCheck;var r=new _g;n&&("boolean"==typeof n?r.push(og):r.push(og.withExtraArgument(n.extraArgument)));return r}(e)}}function xg(e){var t,n={},r=[],a={addCase:function(e,t){var r="string"==typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in n)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return n[r]=t,a},addMatcher:function(e,t){return r.push({matcher:e,reducer:t}),a},addDefaultCase:function(e){return t=e,a}};return e(a),[n,r,t]}function jg(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var n,r="function"==typeof e.initialState?e.initialState:Sg(e.initialState),a=e.reducers||{},o=Object.keys(a),i={},s={},l={};function u(){var t="function"==typeof e.extraReducers?xg(e.extraReducers):[e.extraReducers],n=t[0],a=void 0===n?{}:n,o=t[1],i=void 0===o?[]:o,l=t[2],u=void 0===l?void 0:l,c=yg(yg({},a),s);return function(e,t,n,r){void 0===n&&(n=[]);var a,o="function"==typeof t?xg(t):[t,n,r],i=o[0],s=o[1],l=o[2];if("function"==typeof e)a=function(){return Sg(e())};else{var u=Sg(e);a=function(){return u}}function c(e,t){void 0===e&&(e=a());var n=ug([i[t.type]],s.filter((function(e){return(0,e.matcher)(t)})).map((function(e){return e.reducer})));return 0===n.filter((function(e){return!!e})).length&&(n=[l]),n.reduce((function(e,n){if(n){var r;if(Jp(e))return void 0===(r=n(e,t))?e:r;if(Qp(e))return qm(e,(function(e){return n(e,t)}));if(void 0===(r=n(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return e}),e)}return c.getInitialState=a,c}(r,(function(e){for(var t in c)e.addCase(t,c[t]);for(var n=0,r=i;n<r.length;n++){var a=r[n];e.addMatcher(a.matcher,a.reducer)}u&&e.addDefaultCase(u)}))}return o.forEach((function(e){var n,r,o=a[e],u=t+"/"+e;"reducer"in o?(n=o.reducer,r=o.prepare):n=o,i[e]=n,s[u]=n,l[e]=r?wg(u,r):wg(u)})),{name:t,reducer:function(e,t){return n||(n=u()),n(e,t)},actions:l,caseReducers:i,getInitialState:function(){return n||(n=u()),n.getInitialState()}}}var Tg=["name","message","stack","code"],Pg=function(e,t){this.payload=e,this.meta=t},Cg=function(e,t){this.payload=e,this.meta=t},Og=function(e){if("object"==typeof e&&null!==e){for(var t={},n=0,r=Tg;n<r.length;n++){var a=r[n];"string"==typeof e[a]&&(t[a]=e[a])}return t}return{message:String(e)}};function Ng(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}!function(){function e(e,t,n){var r=wg(e+"/fulfilled",(function(e,t,n,r){return{payload:e,meta:vg(yg({},r||{}),{arg:n,requestId:t,requestStatus:"fulfilled"})}})),a=wg(e+"/pending",(function(e,t,n){return{payload:void 0,meta:vg(yg({},n||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),o=wg(e+"/rejected",(function(e,t,r,a,o){return{payload:a,error:(n&&n.serializeError||Og)(e||"Rejected"),meta:vg(yg({},o||{}),{arg:r,requestId:t,rejectedWithValue:!!a,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),i="undefined"!=typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){},e}();return Object.assign((function(e){return function(s,l,u){var c,d=(null==n?void 0:n.idGenerator)?n.idGenerator(e):function(e){void 0===e&&(e=21);for(var t="",n=e;n--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t}(),f=new i;function h(e){c=e,f.abort()}var p=function(){return i=this,p=null,m=function(){var i,p,m,g,y,v;return lg(this,(function(b){switch(b.label){case 0:return b.trys.push([0,4,,5]),g=null==(i=null==n?void 0:n.condition)?void 0:i.call(n,e,{getState:l,extra:u}),null===(w=g)||"object"!=typeof w||"function"!=typeof w.then?[3,2]:[4,g];case 1:g=b.sent(),b.label=2;case 2:if(!1===g||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return y=new Promise((function(e,t){return f.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:c||"Aborted"})}))})),s(a(d,e,null==(p=null==n?void 0:n.getPendingMeta)?void 0:p.call(n,{requestId:d,arg:e},{getState:l,extra:u}))),[4,Promise.race([y,Promise.resolve(t(e,{dispatch:s,getState:l,extra:u,requestId:d,signal:f.signal,abort:h,rejectWithValue:function(e,t){return new Pg(e,t)},fulfillWithValue:function(e,t){return new Cg(e,t)}})).then((function(t){if(t instanceof Pg)throw t;return t instanceof Cg?r(t.payload,d,e,t.meta):r(t,d,e)}))])];case 3:return m=b.sent(),[3,5];case 4:return v=b.sent(),m=v instanceof Pg?o(null,d,e,v.payload,v.meta):o(v,d,e),[3,5];case 5:return n&&!n.dispatchConditionRejection&&o.match(m)&&m.meta.condition||s(m),[2,m]}var w}))},new Promise((function(e,t){var n=function(e){try{a(m.next(e))}catch(ok){t(ok)}},r=function(e){try{a(m.throw(e))}catch(ok){t(ok)}},a=function(t){return t.done?e(t.value):Promise.resolve(t.value).then(n,r)};a((m=m.apply(i,p)).next())}));var i,p,m}();return Object.assign(p,{abort:h,requestId:d,arg:e,unwrap:function(){return p.then(Ng)}})}}),{pending:a,rejected:o,fulfilled:r,typePrefix:e})}e.withTypes=function(){return e}}();var Rg="listenerMiddleware";wg(Rg+"/add"),wg(Rg+"/removeAll"),wg(Rg+"/remove"),"function"==typeof queueMicrotask&&queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis),function(){function e(e,t){var n=i[e];return n?n.enumerable=t:i[e]=n={configurable:!0,enumerable:t,get:function(){var t=this[Lm];return Um.get(t,e)},set:function(t){var n=this[Lm];Um.set(n,e,t)}},n}function t(e){for(var t=e.length-1;t>=0;t--){var a=e[t][Lm];if(!a.P)switch(a.i){case 5:r(a)&&_m(a);break;case 4:n(a)&&_m(a)}}}function n(e){for(var t=e.t,n=e.k,r=Am(n),a=r.length-1;a>=0;a--){var o=r[a];if(o!==Lm){var i=t[o];if(void 0===i&&!Xp(t,o))return!0;var s=n[o],l=s&&s[Lm];if(l?l.t!==i:!em(s,i))return!0}}var u=!!t[Lm];return r.length!==Am(t).length+(u?0:1)}function r(e){var t=e.k;if(t.length!==e.t.length)return!0;var n=Object.getOwnPropertyDescriptor(t,t.length-1);if(n&&!n.get)return!0;for(var r=0;r<t.length;r++)if(!t.hasOwnProperty(r))return!0;return!1}var a,o,i={};o={J:function(t,n){var r=Array.isArray(t),a=function(t,n){if(t){for(var r=Array(n.length),a=0;a<n.length;a++)Object.defineProperty(r,""+a,e(a,!0));return r}var o=Dm(n);delete o[Lm];for(var i=Am(o),s=0;s<i.length;s++){var l=i[s];o[l]=e(l,t||!!o[l].enumerable)}return Object.create(Object.getPrototypeOf(n),o)}(r,t),o={i:r?5:4,A:n?n.A:um(),P:!1,I:!1,R:{},l:n,t:t,k:a,o:null,g:!1,C:!1};return Object.defineProperty(a,Lm,{value:o,writable:!0}),a},S:function(e,n,a){a?Jp(n)&&n[Lm].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var n=t[Lm];if(n){var a=n.t,o=n.k,i=n.R,s=n.i;if(4===s)Gp(o,(function(t){t!==Lm&&(void 0!==a[t]||Xp(a,t)?i[t]||e(o[t]):(i[t]=!0,_m(n)))})),Gp(a,(function(e){void 0!==o[e]||Xp(o,e)||(i[e]=!1,_m(n))}));else if(5===s){if(r(n)&&(_m(n),i.length=!0),o.length<a.length)for(var l=o.length;l<a.length;l++)i[l]=!1;else for(var u=a.length;u<o.length;u++)i[u]=!0;for(var c=Math.min(o.length,a.length),d=0;d<c;d++)o.hasOwnProperty(d)||(i[d]=!0),void 0===i[d]&&e(o[d])}}}}(e.p[0]),t(e.p))},K:function(e){return 4===e.i?n(e):r(e)}},zm[a="ES5"]||(zm[a]=o)}();const Ig=()=>{try{const e=sessionStorage.getItem("user")||localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error al obtener el usuario del storage:",e),null}},Lg=jg({name:"auth",initialState:{user:Ig(),token:sessionStorage.getItem("token")||localStorage.getItem("token")||null,isAuthenticated:!!Ig(),loading:!1,error:null},reducers:{loginSuccess:(e,t)=>{e.user=t.payload.user,e.token=t.payload.token,e.isAuthenticated=!0,e.role=t.payload.user.role,e.loading=!1,e.error=null},authLoading:e=>{e.loading=!0,e.error=null},authError:(e,t)=>{e.loading=!1,e.error=t.payload},logout:e=>{e.user=null,e.token=null,e.isAuthenticated=!1,e.loading=!1,e.error=null},updateUser:(e,t)=>{e.user={...e.user,...t.payload}}}}),{loginSuccess:$g,authLoading:Ag,authError:Dg,logout:zg,updateUser:Ug}=Lg.actions,Mg=Lg.reducer,Fg=({isOpen:e,toggleSidebar:t})=>{const n=up(),r=Bd((e=>e.auth.user)),a=Uf(),o=dp(),[i,s]=V.useState({dashboard:!0,candidates:!0}),l=(null==r?void 0:r.role)||"student",u=e=>{s((t=>({...t,[e]:!t[e]})))},c=e=>{if("/"===e){return"/"===n.pathname}return n.pathname===e||n.pathname.startsWith(e)&&(n.pathname.length===e.length||"/"===n.pathname[e.length])},d={admin:[{path:"/admin/dashboard",icon:"th-large",label:"Dashboard",key:"dashboard"},{path:"/admin",icon:"home",label:"Inicio",key:"home"},{path:"/admin/patients",icon:"user-injured",label:"Pacientes",key:"patients"},{path:"/test",icon:"clipboard-list",label:"Cuestionario",key:"tests"},{path:"/admin/reports",icon:"chart-bar",label:"Resultados",key:"results"},{path:"/admin/administration",icon:"shield-alt",label:"Administración",key:"administration"},{path:"/admin/tests",icon:"vial",label:"Pruebas CRUD",key:"crud-tests"},{path:"/settings",icon:"cog",label:"Configuración",key:"settings"},{path:"/help",icon:"question-circle",label:"Ayuda",key:"help"}],professional:[{path:"/professional/dashboard",icon:"th-large",label:"Dashboard",key:"dashboard"},{path:"/professional",icon:"home",label:"Inicio",key:"home"},{path:"/professional/patients",icon:"user-injured",label:"Pacientes",key:"patients"},{path:"/professional/tests",icon:"clipboard-list",label:"Cuestionario",key:"tests"},{path:"/professional/reports",icon:"chart-bar",label:"Resultados",key:"results"},{path:"/professional/students",icon:"shield-alt",label:"Administración",key:"administration"},{path:"/settings",icon:"cog",label:"Configuración",key:"settings"},{path:"/help",icon:"question-circle",label:"Ayuda",key:"help"}],student:[{path:"/student/dashboard",icon:"th-large",label:"Dashboard",key:"dashboard"},{path:"/student",icon:"home",label:"Inicio",key:"home"},{path:"/student/patients",icon:"user-injured",label:"Pacientes",key:"patients"},{path:"/student/tests",icon:"clipboard-list",label:"Cuestionario",key:"tests"},{path:"/student/results",icon:"chart-bar",label:"Resultados",key:"results"},{path:"/student/profile",icon:"shield-alt",label:"Administración",key:"administration"},{path:"/settings",icon:"cog",label:"Configuración",key:"settings"},{path:"/help",icon:"question-circle",label:"Ayuda",key:"help"}]},f=d[l]||d.student,h=f.filter((e=>i[e.key]));return ee.jsxs("div",{className:"sidebar bg-[#121940] text-[#a4b1cd] fixed top-0 left-0 h-full z-50 transition-all duration-300 ease-in-out\n                     "+(e?"w-64":"w-[70px]"),children:[ee.jsxs("div",{className:"sidebar-header p-5 flex justify-between items-center border-b border-opacity-10 border-white",children:[e&&ee.jsxs("h1",{className:"text-xl font-bold text-white",children:["Activatu",ee.jsx("span",{className:"text-[#ffda0a]",children:"mente"})]}),ee.jsx("button",{onClick:t,className:"text-[#a4b1cd] cursor-pointer",children:ee.jsx("i",{className:"fas "+(e?"fa-chevron-left":"fa-chevron-right")})})]}),h.length>0&&ee.jsxs("div",{className:"sidebar-section py-4 border-b border-opacity-5 border-white",children:[e&&ee.jsx("h2",{className:"uppercase text-xs px-5 mb-2 tracking-wider",children:"Favoritos"}),ee.jsx("ul",{className:"menu-list",children:h.map((t=>ee.jsxs("li",{className:"flex items-center justify-between p-3 px-5 hover:bg-opacity-5 hover:bg-white\n                          "+(c(t.path)?"bg-[#222b5a] text-white":""),children:[ee.jsxs(Ap,{to:t.path,className:"flex items-center flex-grow",children:[ee.jsx("i",{className:`fas fa-${t.icon} ${e?"mr-3":""} w-5 text-center`}),e&&ee.jsx("span",{children:t.label})]}),e&&ee.jsx("span",{className:"star cursor-pointer "+(i[t.key]?"text-[#ffda0a]":""),onClick:()=>u(t.key),children:ee.jsx("i",{className:(i[t.key]?"fas":"far")+" fa-star"})})]},`fav-${t.key}`)))})]}),ee.jsx("div",{className:"sidebar-section py-4",children:ee.jsx("ul",{className:"menu-list",children:f.map((t=>ee.jsxs("li",{className:"flex items-center justify-between p-3 px-5 hover:bg-opacity-5 hover:bg-white\n                          "+(c(t.path)?"bg-[#222b5a] text-white":""),children:[ee.jsxs(Ap,{to:t.path,className:"flex items-center flex-grow",children:[ee.jsx("i",{className:`fas fa-${t.icon} ${e?"mr-3":""} w-5 text-center`}),e&&ee.jsx("span",{children:t.label})]}),e&&ee.jsx("span",{className:"star cursor-pointer "+(i[t.key]?"text-[#ffda0a]":""),onClick:()=>u(t.key),children:ee.jsx("i",{className:(i[t.key]?"fas":"far")+" fa-star"})})]},t.key)))})}),ee.jsx("div",{className:"sidebar-footer mt-auto p-5 border-t border-opacity-10 border-white",children:ee.jsxs("button",{onClick:()=>{window.confirm("¿Está seguro que desea cerrar sesión?")&&(a(zg()),o("/login"))},className:"flex items-center justify-center text-white bg-gradient-to-r from-red-600 to-red-800 hover:from-red-700 hover:to-red-900 transition-all duration-200 py-2 px-4 rounded-lg w-full",children:[ee.jsx("i",{className:"fas fa-sign-out-alt "+(e?"mr-2":"")}),e&&ee.jsx("span",{children:"Cerrar Sesión"})]})})]})},Bg=()=>{const e=up(),{user:t}=Bd((e=>e.auth)),n={admin:"Administrador",professional:"Profesional",student:"Estudiante",dashboard:"Dashboard",patients:"Pacientes",tests:"Cuestionarios",reports:"Reportes",results:"Resultados",administration:"Administración",settings:"Configuración",help:"Ayuda",profile:"Perfil",users:"Usuarios",institutions:"Instituciones",students:"Estudiantes"},r=V.useMemo((()=>{const t=e.pathname.split("/").filter((e=>e));return 0===t.length?[{name:"Inicio",path:"/",active:!0}]:t.map(((e,r)=>{const a=`/${t.slice(0,r+1).join("/")}`,o=r===t.length-1;return{name:n[e]||e.charAt(0).toUpperCase()+e.slice(1),path:a,active:o}}))}),[e.pathname]);return t&&0!==r.length?ee.jsx("nav",{className:"bg-gray-100 py-2 px-4 rounded-md mb-4",children:ee.jsxs("ol",{className:"flex flex-wrap items-center text-sm",children:[ee.jsxs("li",{className:"flex items-center",children:[ee.jsx(Ap,{to:`/${t.role}`,className:"text-blue-600 hover:text-blue-800",children:"Inicio"}),r.length>0&&ee.jsx("span",{className:"mx-2 text-gray-500",children:ee.jsx("i",{className:"fas fa-chevron-right text-xs"})})]}),r.map(((e,t)=>ee.jsx("li",{className:"flex items-center",children:e.active?ee.jsx("span",{className:"text-gray-700 font-medium",children:e.name}):ee.jsxs(ee.Fragment,{children:[ee.jsx(Ap,{to:e.path,className:"text-blue-600 hover:text-blue-800",children:e.name}),ee.jsx("span",{className:"mx-2 text-gray-500",children:ee.jsx("i",{className:"fas fa-chevron-right text-xs"})})]})},e.path)))]})}):null},qg=({children:e})=>{const t=up(),[n,r]=V.useState(t),[a,o]=V.useState("fadeIn");return V.useEffect((()=>{if(t.pathname!==n.pathname){o("fadeOut");const e=setTimeout((()=>{r(t),o("fadeIn")}),300);return()=>clearTimeout(e)}}),[t,n]),ee.jsx("div",{className:`page-transition ${a}`,children:e})};class Vg extends Error{constructor(e,t="FunctionsError",n){super(e),this.name=t,this.context=n}}class Wg extends Vg{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class Hg extends Vg{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class Kg extends Vg{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var Jg,Qg;(Qg=Jg||(Jg={})).Any="any",Qg.ApNortheast1="ap-northeast-1",Qg.ApNortheast2="ap-northeast-2",Qg.ApSouth1="ap-south-1",Qg.ApSoutheast1="ap-southeast-1",Qg.ApSoutheast2="ap-southeast-2",Qg.CaCentral1="ca-central-1",Qg.EuCentral1="eu-central-1",Qg.EuWest1="eu-west-1",Qg.EuWest2="eu-west-2",Qg.EuWest3="eu-west-3",Qg.SaEast1="sa-east-1",Qg.UsEast1="us-east-1",Qg.UsWest1="us-west-1",Qg.UsWest2="us-west-2";var Gg=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(ok){o(ok)}}function s(e){try{l(r.throw(e))}catch(ok){o(ok)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};class Yg{constructor(e,{headers:t={},customFetch:n,region:r=Jg.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Bp((()=>Promise.resolve().then((()=>cy))),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)})(n)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var n;return Gg(this,void 0,void 0,(function*(){try{const{headers:r,method:a,body:o}=t;let i,s={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(s["x-region"]=l),o&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(s["Content-Type"]="application/octet-stream",i=o):"string"==typeof o?(s["Content-Type"]="text/plain",i=o):"undefined"!=typeof FormData&&o instanceof FormData?i=o:(s["Content-Type"]="application/json",i=JSON.stringify(o)));const u=yield this.fetch(`${this.url}/${e}`,{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},s),this.headers),r),body:i}).catch((e=>{throw new Wg(e)})),c=u.headers.get("x-relay-error");if(c&&"true"===c)throw new Hg(u);if(!u.ok)throw new Kg(u);let d,f=(null!==(n=u.headers.get("Content-Type"))&&void 0!==n?n:"text/plain").split(";")[0].trim();return d="application/json"===f?yield u.json():"application/octet-stream"===f?yield u.blob():"text/event-stream"===f?u:"multipart/form-data"===f?yield u.formData():yield u.text(),{data:d,error:null}}catch(r){return{data:null,error:r}}}))}}var Xg={},Zg={},ey={},ty={},ny={},ry={},ay=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const oy=ay.fetch,iy=ay.fetch.bind(ay),sy=ay.Headers,ly=ay.Request,uy=ay.Response,cy=Object.freeze(Object.defineProperty({__proto__:null,Headers:sy,Request:ly,Response:uy,default:iy,fetch:oy},Symbol.toStringTag,{value:"Module"})),dy=o(cy);var fy={};Object.defineProperty(fy,"__esModule",{value:!0});let hy=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};fy.default=hy;var py=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ry,"__esModule",{value:!0});const my=py(dy),gy=py(fy);ry.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=my.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let n=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then((async e=>{var t,n,r;let a=null,o=null,i=null,s=e.status,l=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(o="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const r=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),u=null===(n=e.headers.get("content-range"))||void 0===n?void 0:n.split("/");r&&u&&u.length>1&&(i=parseInt(u[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(a={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,i=null,s=406,l="Not Acceptable"):o=1===o.length?o[0]:null)}else{const t=await e.text();try{a=JSON.parse(t),Array.isArray(a)&&404===e.status&&(o=[],a=null,s=200,l="OK")}catch(u){404===e.status&&""===t?(s=204,l="No Content"):a={message:t}}if(a&&this.isMaybeSingle&&(null===(r=null==a?void 0:a.details)||void 0===r?void 0:r.includes("0 rows"))&&(a=null,s=200,l="OK"),a&&this.shouldThrowOnError)throw new gy.default(a)}return{error:a,data:o,count:i,status:s,statusText:l}}));return this.shouldThrowOnError||(n=n.catch((e=>{var t,n,r;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(n=null==e?void 0:e.stack)&&void 0!==n?n:""}`,hint:"",code:`${null!==(r=null==e?void 0:e.code)&&void 0!==r?r:""}`},data:null,count:null,status:0,statusText:""}}))),n.then(e,t)}returns(){return this}overrideTypes(){return this}};var yy=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ny,"__esModule",{value:!0});const vy=yy(ry);let by=class extends vy.default{select(e){let t=!1;const n=(null!=e?e:"*").split("").map((e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e))).join("");return this.url.searchParams.set("select",n),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:n,foreignTable:r,referencedTable:a=r}={}){const o=a?`${a}.order`:"order",i=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${i?`${i},`:""}${e}.${t?"asc":"desc"}${void 0===n?"":n?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:n=t}={}){const r=void 0===n?"limit":`${n}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:n,referencedTable:r=n}={}){const a=void 0===r?"offset":`${r}.offset`,o=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(a,`${e}`),this.url.searchParams.set(o,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:n=!1,buffers:r=!1,wal:a=!1,format:o="text"}={}){var i;const s=[e?"analyze":null,t?"verbose":null,n?"settings":null,r?"buffers":null,a?"wal":null].filter(Boolean).join("|"),l=null!==(i=this.headers.Accept)&&void 0!==i?i:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${l}"; options=${s};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};ny.default=by;var wy=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ty,"__esModule",{value:!0});const _y=wy(ny);let ky=class extends _y.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const n=Array.from(new Set(t)).map((e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`)).join(",");return this.url.searchParams.append(e,`in.(${n})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:n,type:r}={}){let a="";"plain"===r?a="pl":"phrase"===r?a="ph":"websearch"===r&&(a="w");const o=void 0===n?"":`(${n})`;return this.url.searchParams.append(e,`${a}fts${o}.${t}`),this}match(e){return Object.entries(e).forEach((([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)})),this}not(e,t,n){return this.url.searchParams.append(e,`not.${t}.${n}`),this}or(e,{foreignTable:t,referencedTable:n=t}={}){const r=n?`${n}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,n){return this.url.searchParams.append(e,`${t}.${n}`),this}};ty.default=ky;var Sy=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ey,"__esModule",{value:!0});const Ey=Sy(ty);ey.default=class{constructor(e,{headers:t={},schema:n,fetch:r}){this.url=e,this.headers=t,this.schema=n,this.fetch=r}select(e,{head:t=!1,count:n}={}){const r=t?"HEAD":"GET";let a=!1;const o=(null!=e?e:"*").split("").map((e=>/\s/.test(e)&&!a?"":('"'===e&&(a=!a),e))).join("");return this.url.searchParams.set("select",o),n&&(this.headers.Prefer=`count=${n}`),new Ey.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:n=!0}={}){const r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),n||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>`"${e}"`));this.url.searchParams.set("columns",e.join(","))}}return new Ey.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:n=!1,count:r,defaultToNull:a=!0}={}){const o=[`resolution=${n?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),r&&o.push(`count=${r}`),a||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>`"${e}"`));this.url.searchParams.set("columns",e.join(","))}}return new Ey.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const n=[];return this.headers.Prefer&&n.push(this.headers.Prefer),t&&n.push(`count=${t}`),this.headers.Prefer=n.join(","),new Ey.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new Ey.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var xy={},jy={};Object.defineProperty(jy,"__esModule",{value:!0}),jy.version=void 0,jy.version="0.0.0-automated",Object.defineProperty(xy,"__esModule",{value:!0}),xy.DEFAULT_HEADERS=void 0;const Ty=jy;xy.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${Ty.version}`};var Py=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Zg,"__esModule",{value:!0});const Cy=Py(ey),Oy=Py(ty),Ny=xy;Zg.default=class e{constructor(e,{headers:t={},schema:n,fetch:r}={}){this.url=e,this.headers=Object.assign(Object.assign({},Ny.DEFAULT_HEADERS),t),this.schemaName=n,this.fetch=r}from(e){const t=new URL(`${this.url}/${e}`);return new Cy.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:n=!1,get:r=!1,count:a}={}){let o;const i=new URL(`${this.url}/rpc/${e}`);let s;n||r?(o=n?"HEAD":"GET",Object.entries(t).filter((([e,t])=>void 0!==t)).map((([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`])).forEach((([e,t])=>{i.searchParams.append(e,t)}))):(o="POST",s=t);const l=Object.assign({},this.headers);return a&&(l.Prefer=`count=${a}`),new Oy.default({method:o,url:i,headers:l,schema:this.schemaName,body:s,fetch:this.fetch,allowEmpty:!1})}};var Ry=r&&r.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Xg,"__esModule",{value:!0}),Xg.PostgrestError=Xg.PostgrestBuilder=Xg.PostgrestTransformBuilder=Xg.PostgrestFilterBuilder=Xg.PostgrestQueryBuilder=Xg.PostgrestClient=void 0;const Iy=Ry(Zg);Xg.PostgrestClient=Iy.default;const Ly=Ry(ey);Xg.PostgrestQueryBuilder=Ly.default;const $y=Ry(ty);Xg.PostgrestFilterBuilder=$y.default;const Ay=Ry(ny);Xg.PostgrestTransformBuilder=Ay.default;const Dy=Ry(ry);Xg.PostgrestBuilder=Dy.default;const zy=Ry(fy);Xg.PostgrestError=zy.default;var Uy=Xg.default={PostgrestClient:Iy.default,PostgrestQueryBuilder:Ly.default,PostgrestFilterBuilder:$y.default,PostgrestTransformBuilder:Ay.default,PostgrestBuilder:Dy.default,PostgrestError:zy.default};const{PostgrestClient:My,PostgrestQueryBuilder:Fy,PostgrestFilterBuilder:By,PostgrestTransformBuilder:qy,PostgrestBuilder:Vy,PostgrestError:Wy}=Uy,Hy={"X-Client-Info":"realtime-js/2.11.2"};var Ky,Jy,Qy,Gy,Yy,Xy,Zy,ev,tv,nv,rv;(Jy=Ky||(Ky={}))[Jy.connecting=0]="connecting",Jy[Jy.open=1]="open",Jy[Jy.closing=2]="closing",Jy[Jy.closed=3]="closed",(Gy=Qy||(Qy={})).closed="closed",Gy.errored="errored",Gy.joined="joined",Gy.joining="joining",Gy.leaving="leaving",(Xy=Yy||(Yy={})).close="phx_close",Xy.error="phx_error",Xy.join="phx_join",Xy.reply="phx_reply",Xy.leave="phx_leave",Xy.access_token="access_token",(Zy||(Zy={})).websocket="websocket",(tv=ev||(ev={})).Connecting="connecting",tv.Open="open",tv.Closing="closing",tv.Closed="closed";class av{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),n=new TextDecoder;return this._decodeBroadcast(e,t,n)}_decodeBroadcast(e,t,n){const r=t.getUint8(1),a=t.getUint8(2);let o=this.HEADER_LENGTH+2;const i=n.decode(e.slice(o,o+r));o+=r;const s=n.decode(e.slice(o,o+a));o+=a;return{ref:null,topic:i,event:s,payload:JSON.parse(n.decode(e.slice(o,e.byteLength)))}}}class ov{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout((()=>{this.tries=this.tries+1,this.callback()}),this.timerCalc(this.tries+1))}}(rv=nv||(nv={})).abstime="abstime",rv.bool="bool",rv.date="date",rv.daterange="daterange",rv.float4="float4",rv.float8="float8",rv.int2="int2",rv.int4="int4",rv.int4range="int4range",rv.int8="int8",rv.int8range="int8range",rv.json="json",rv.jsonb="jsonb",rv.money="money",rv.numeric="numeric",rv.oid="oid",rv.reltime="reltime",rv.text="text",rv.time="time",rv.timestamp="timestamp",rv.timestamptz="timestamptz",rv.timetz="timetz",rv.tsrange="tsrange",rv.tstzrange="tstzrange";const iv=(e,t,n={})=>{var r;const a=null!==(r=n.skipTypes)&&void 0!==r?r:[];return Object.keys(t).reduce(((n,r)=>(n[r]=sv(r,e,t,a),n)),{})},sv=(e,t,n,r)=>{const a=t.find((t=>t.name===e)),o=null==a?void 0:a.type,i=n[e];return o&&!r.includes(o)?lv(o,i):uv(i)},lv=(e,t)=>{if("_"===e.charAt(0)){const n=e.slice(1,e.length);return hv(t,n)}switch(e){case nv.bool:return cv(t);case nv.float4:case nv.float8:case nv.int2:case nv.int4:case nv.int8:case nv.numeric:case nv.oid:return dv(t);case nv.json:case nv.jsonb:return fv(t);case nv.timestamp:return pv(t);case nv.abstime:case nv.date:case nv.daterange:case nv.int4range:case nv.int8range:case nv.money:case nv.reltime:case nv.text:case nv.time:case nv.timestamptz:case nv.timetz:case nv.tsrange:case nv.tstzrange:default:return uv(t)}},uv=e=>e,cv=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},dv=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},fv=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},hv=(e,t)=>{if("string"!=typeof e)return e;const n=e.length-1,r=e[n];if("{"===e[0]&&"}"===r){let r;const o=e.slice(1,n);try{r=JSON.parse("["+o+"]")}catch(a){r=o?o.split(","):[]}return r.map((e=>lv(t,e)))}return e},pv=e=>"string"==typeof e?e.replace(" ","T"):e,mv=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class gv{constructor(e,t,n={},r=1e4){this.channel=e,this.event=t,this.payload=n,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var n;return this._hasReceived(e)&&t(null===(n=this.receivedResp)||void 0===n?void 0:n.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},(e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)})),this.timeoutTimer=setTimeout((()=>{this.trigger("timeout",{})}),this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter((t=>t.status===e)).forEach((e=>e.callback(t)))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var yv,vv,bv,wv,_v,kv,Sv,Ev;(vv=yv||(yv={})).SYNC="sync",vv.JOIN="join",vv.LEAVE="leave";class xv{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const n=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(n.state,{},(e=>{const{onJoin:t,onLeave:n,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=xv.syncState(this.state,e,t,n),this.pendingDiffs.forEach((e=>{this.state=xv.syncDiff(this.state,e,t,n)})),this.pendingDiffs=[],r()})),this.channel._on(n.diff,{},(e=>{const{onJoin:t,onLeave:n,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=xv.syncDiff(this.state,e,t,n),r())})),this.onJoin(((e,t,n)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:n})})),this.onLeave(((e,t,n)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:n})})),this.onSync((()=>{this.channel._trigger("presence",{event:"sync"})}))}static syncState(e,t,n,r){const a=this.cloneDeep(e),o=this.transformState(t),i={},s={};return this.map(a,((e,t)=>{o[e]||(s[e]=t)})),this.map(o,((e,t)=>{const n=a[e];if(n){const r=t.map((e=>e.presence_ref)),a=n.map((e=>e.presence_ref)),o=t.filter((e=>a.indexOf(e.presence_ref)<0)),l=n.filter((e=>r.indexOf(e.presence_ref)<0));o.length>0&&(i[e]=o),l.length>0&&(s[e]=l)}else i[e]=t})),this.syncDiff(a,{joins:i,leaves:s},n,r)}static syncDiff(e,t,n,r){const{joins:a,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return n||(n=()=>{}),r||(r=()=>{}),this.map(a,((t,r)=>{var a;const o=null!==(a=e[t])&&void 0!==a?a:[];if(e[t]=this.cloneDeep(r),o.length>0){const n=e[t].map((e=>e.presence_ref)),r=o.filter((e=>n.indexOf(e.presence_ref)<0));e[t].unshift(...r)}n(t,o,r)})),this.map(o,((t,n)=>{let a=e[t];if(!a)return;const o=n.map((e=>e.presence_ref));a=a.filter((e=>o.indexOf(e.presence_ref)<0)),e[t]=a,r(t,a,n),0===a.length&&delete e[t]})),e}static map(e,t){return Object.getOwnPropertyNames(e).map((n=>t(n,e[n])))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce(((t,n)=>{const r=e[n];return t[n]="metas"in r?r.metas.map((e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e))):r,t}),{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(wv=bv||(bv={})).ALL="*",wv.INSERT="INSERT",wv.UPDATE="UPDATE",wv.DELETE="DELETE",(kv=_v||(_v={})).BROADCAST="broadcast",kv.PRESENCE="presence",kv.POSTGRES_CHANGES="postgres_changes",kv.SYSTEM="system",(Ev=Sv||(Sv={})).SUBSCRIBED="SUBSCRIBED",Ev.TIMED_OUT="TIMED_OUT",Ev.CLOSED="CLOSED",Ev.CHANNEL_ERROR="CHANNEL_ERROR";class jv{constructor(e,t={config:{}},n){this.topic=e,this.params=t,this.socket=n,this.bindings={},this.state=Qy.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new gv(this,Yy.join,this.params,this.timeout),this.rejoinTimer=new ov((()=>this._rejoinUntilConnected()),this.socket.reconnectAfterMs),this.joinPush.receive("ok",(()=>{this.state=Qy.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach((e=>e.send())),this.pushBuffer=[]})),this._onClose((()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=Qy.closed,this.socket._remove(this)})),this._onError((e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=Qy.errored,this.rejoinTimer.scheduleTimeout())})),this.joinPush.receive("timeout",(()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=Qy.errored,this.rejoinTimer.scheduleTimeout())})),this._on(Yy.reply,{},((e,t)=>{this._trigger(this._replyEventName(t),e)})),this.presence=new xv(this),this.broadcastEndpointURL=mv(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var n,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:a,presence:o,private:i}}=this.params;this._onError((t=>null==e?void 0:e(Sv.CHANNEL_ERROR,t))),this._onClose((()=>null==e?void 0:e(Sv.CLOSED)));const s={},l={broadcast:a,presence:o,postgres_changes:null!==(r=null===(n=this.bindings.postgres_changes)||void 0===n?void 0:n.map((e=>e.filter)))&&void 0!==r?r:[],private:i};this.socket.accessTokenValue&&(s.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},s)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",(async({postgres_changes:t})=>{var n;if(this.socket.setAuth(),void 0!==t){const r=this.bindings.postgres_changes,a=null!==(n=null==r?void 0:r.length)&&void 0!==n?n:0,o=[];for(let n=0;n<a;n++){const a=r[n],{filter:{event:i,schema:s,table:l,filter:u}}=a,c=t&&t[n];if(!c||c.event!==i||c.schema!==s||c.table!==l||c.filter!==u)return this.unsubscribe(),void(null==e||e(Sv.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));o.push(Object.assign(Object.assign({},a),{id:c.id}))}return this.bindings.postgres_changes=o,void(e&&e(Sv.SUBSCRIBED))}null==e||e(Sv.SUBSCRIBED)})).receive("error",(t=>{null==e||e(Sv.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))})).receive("timeout",(()=>{null==e||e(Sv.TIMED_OUT)}))}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,n){return this._on(e,t,n)}async send(e,t={}){var n,r;if(this._canPush()||"broadcast"!==e.type)return new Promise((n=>{var r,a,o;const i=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(o=null===(a=null===(r=this.params)||void 0===r?void 0:r.config)||void 0===a?void 0:a.broadcast)||void 0===o?void 0:o.ack)||n("ok"),i.receive("ok",(()=>n("ok"))),i.receive("error",(()=>n("error"))),i.receive("timeout",(()=>n("timed out")))}));{const{event:o,payload:i}=e,s={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:i,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,s,null!==(n=t.timeout)&&void 0!==n?n:this.timeout);return await(null===(r=e.body)||void 0===r?void 0:r.cancel()),e.ok?"ok":"error"}catch(a){return"AbortError"===a.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=Qy.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Yy.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise((n=>{const r=new gv(this,Yy.leave,{},e);r.receive("ok",(()=>{t(),n("ok")})).receive("timeout",(()=>{t(),n("timed out")})).receive("error",(()=>{n("error")})),r.send(),this._canPush()||r.trigger("ok",{})}))}async _fetchWithTimeout(e,t,n){const r=new AbortController,a=setTimeout((()=>r.abort()),n),o=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(a),o}_push(e,t,n=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new gv(this,e,t,n);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,n){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,n){var r,a;const o=e.toLocaleLowerCase(),{close:i,error:s,leave:l,join:u}=Yy;if(n&&[i,s,l,u].indexOf(o)>=0&&n!==this._joinRef())return;let c=this._onMessage(o,t,n);if(t&&!c)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?null===(r=this.bindings.postgres_changes)||void 0===r||r.filter((e=>{var t,n,r;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(r=null===(n=e.filter)||void 0===n?void 0:n.event)||void 0===r?void 0:r.toLocaleLowerCase())===o})).map((e=>e.callback(c,n))):null===(a=this.bindings[o])||void 0===a||a.filter((e=>{var n,r,a,i,s,l;if(["broadcast","presence","postgres_changes"].includes(o)){if("id"in e){const o=e.id,i=null===(n=e.filter)||void 0===n?void 0:n.event;return o&&(null===(r=t.ids)||void 0===r?void 0:r.includes(o))&&("*"===i||(null==i?void 0:i.toLocaleLowerCase())===(null===(a=t.data)||void 0===a?void 0:a.type.toLocaleLowerCase()))}{const n=null===(s=null===(i=null==e?void 0:e.filter)||void 0===i?void 0:i.event)||void 0===s?void 0:s.toLocaleLowerCase();return"*"===n||n===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===o})).map((e=>{if("object"==typeof c&&"ids"in c){const e=c.data,{schema:t,table:n,commit_timestamp:r,type:a,errors:o}=e,i={schema:t,table:n,commit_timestamp:r,eventType:a,new:{},old:{},errors:o};c=Object.assign(Object.assign({},i),this._getPayloadRecords(e))}e.callback(c,n)}))}_isClosed(){return this.state===Qy.closed}_isJoined(){return this.state===Qy.joined}_isJoining(){return this.state===Qy.joining}_isLeaving(){return this.state===Qy.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,n){const r=e.toLocaleLowerCase(),a={type:r,filter:t,callback:n};return this.bindings[r]?this.bindings[r].push(a):this.bindings[r]=[a],this}_off(e,t){const n=e.toLocaleLowerCase();return this.bindings[n]=this.bindings[n].filter((e=>{var r;return!((null===(r=e.type)||void 0===r?void 0:r.toLocaleLowerCase())===n&&jv.isEqual(e.filter,t))})),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(Yy.close,{},e)}_onError(e){this._on(Yy.error,{},(t=>e(t)))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Qy.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=iv(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=iv(e.columns,e.old_record)),t}}const Tv=()=>{},Pv="undefined"!=typeof WebSocket;class Cv{constructor(e,t){var n;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=Hy,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Tv,this.conn=null,this.sendBuffer=[],this.serializer=new av,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Bp((()=>Promise.resolve().then((()=>cy))),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${Zy.websocket}`,this.httpEndpoint=mv(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const r=null===(n=null==t?void 0:t.params)||void 0===n?void 0:n.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new ov((async()=>{this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn)if(this.transport)this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});else{if(Pv)return this.conn=new WebSocket(this.endpointURL()),void this.setupConnection();this.conn=new Ov(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),Bp((()=>import("./browser-42fe52e4.js").then((e=>e.b))),[]).then((({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()}))}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map((e=>e.unsubscribe())));return this.disconnect(),e}log(e,t,n){this.logger(e,t,n)}connectionState(){switch(this.conn&&this.conn.readyState){case Ky.connecting:return ev.Connecting;case Ky.open:return ev.Open;case Ky.closing:return ev.Closing;default:return ev.Closed}}isConnected(){return this.connectionState()===ev.Open}channel(e,t={config:{}}){const n=new jv(`realtime:${e}`,t,this);return this.channels.push(n),n}push(e){const{topic:t,event:n,payload:r,ref:a}=e,o=()=>{this.encode(e,(e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)}))};this.log("push",`${t} ${n} (${a})`,r),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(t){let e=null;try{e=JSON.parse(atob(t.split(".")[1]))}catch(n){}if(e&&e.exp){if(!(Math.floor(Date.now()/1e3)-e.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`)}this.accessTokenValue=t,this.channels.forEach((e=>{t&&e.updateJoinPayload({access_token:t}),e.joinedOnce&&e._isJoined()&&e._push(Yy.access_token,{access_token:t})}))}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach((e=>e())),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find((t=>t.topic===e&&(t._isJoined()||t._isJoining())));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter((t=>t._joinRef()!==e._joinRef()))}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,(e=>{let{topic:t,event:n,payload:r,ref:a}=e;a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${n} ${a&&"("+a+")"||""}`,r),this.channels.filter((e=>e._isMember(t))).forEach((e=>e._trigger(n,r,a))),this.stateChangeCallbacks.message.forEach((t=>t(e)))}))}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval((()=>this.sendHeartbeat()),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach((e=>e()))}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach((t=>t(e)))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach((t=>t(e)))}_triggerChanError(){this.channels.forEach((e=>e._trigger(Yy.error)))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const n=e.match(/\?/)?"&":"?";return`${e}${n}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class Ov{constructor(e,t,n){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=Ky.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=n.close}}class Nv extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function Rv(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class Iv extends Nv{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Lv extends Nv{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var $v=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(ok){o(ok)}}function s(e){try{l(r.throw(e))}catch(ok){o(ok)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const Av=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Bp((()=>Promise.resolve().then((()=>cy))),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},Dv=e=>{if(Array.isArray(e))return e.map((e=>Dv(e)));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach((([e,n])=>{const r=e.replace(/([-_][a-z])/gi,(e=>e.toUpperCase().replace(/[-_]/g,"")));t[r]=Dv(n)})),t};var zv=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(ok){o(ok)}}function s(e){try{l(r.throw(e))}catch(ok){o(ok)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const Uv=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Mv=(e,t,n)=>zv(void 0,void 0,void 0,(function*(){const r=yield $v(void 0,void 0,void 0,(function*(){return"undefined"==typeof Response?(yield Bp((()=>Promise.resolve().then((()=>cy))),void 0)).Response:Response}));e instanceof r&&!(null==n?void 0:n.noResolveJson)?e.json().then((n=>{t(new Iv(Uv(n),e.status||500))})).catch((e=>{t(new Lv(Uv(e),e))})):t(new Lv(Uv(e),e))}));function Fv(e,t,n,r,a,o){return zv(this,void 0,void 0,(function*(){return new Promise(((i,s)=>{e(n,((e,t,n,r)=>{const a={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?a:(a.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),r&&(a.body=JSON.stringify(r)),Object.assign(Object.assign({},a),n))})(t,r,a,o)).then((e=>{if(!e.ok)throw e;return(null==r?void 0:r.noResolveJson)?e:e.json()})).then((e=>i(e))).catch((e=>Mv(e,s,r)))}))}))}function Bv(e,t,n,r){return zv(this,void 0,void 0,(function*(){return Fv(e,"GET",t,n,r)}))}function qv(e,t,n,r,a){return zv(this,void 0,void 0,(function*(){return Fv(e,"POST",t,r,a,n)}))}function Vv(e,t,n,r,a){return zv(this,void 0,void 0,(function*(){return Fv(e,"DELETE",t,r,a,n)}))}var Wv=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(ok){o(ok)}}function s(e){try{l(r.throw(e))}catch(ok){o(ok)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const Hv={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Kv={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Jv{constructor(e,t={},n,r){this.url=e,this.headers=t,this.bucketId=n,this.fetch=Av(r)}uploadOrUpdate(e,t,n,r){return Wv(this,void 0,void 0,(function*(){try{let a;const o=Object.assign(Object.assign({},Kv),r);let i=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(o.upsert)});const s=o.metadata;"undefined"!=typeof Blob&&n instanceof Blob?(a=new FormData,a.append("cacheControl",o.cacheControl),s&&a.append("metadata",this.encodeMetadata(s)),a.append("",n)):"undefined"!=typeof FormData&&n instanceof FormData?(a=n,a.append("cacheControl",o.cacheControl),s&&a.append("metadata",this.encodeMetadata(s))):(a=n,i["cache-control"]=`max-age=${o.cacheControl}`,i["content-type"]=o.contentType,s&&(i["x-metadata"]=this.toBase64(this.encodeMetadata(s)))),(null==r?void 0:r.headers)&&(i=Object.assign(Object.assign({},i),r.headers));const l=this._removeEmptyFolders(t),u=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:a,headers:i},(null==o?void 0:o.duplex)?{duplex:o.duplex}:{})),d=yield c.json();if(c.ok)return{data:{path:l,id:d.Id,fullPath:d.Key},error:null};return{data:null,error:d}}catch(a){if(Rv(a))return{data:null,error:a};throw a}}))}upload(e,t,n){return Wv(this,void 0,void 0,(function*(){return this.uploadOrUpdate("POST",e,t,n)}))}uploadToSignedUrl(e,t,n,r){return Wv(this,void 0,void 0,(function*(){const a=this._removeEmptyFolders(e),o=this._getFinalPath(a),i=new URL(this.url+`/object/upload/sign/${o}`);i.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:Kv.upsert},r),o=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&n instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",n)):"undefined"!=typeof FormData&&n instanceof FormData?(e=n,e.append("cacheControl",t.cacheControl)):(e=n,o["cache-control"]=`max-age=${t.cacheControl}`,o["content-type"]=t.contentType);const s=yield this.fetch(i.toString(),{method:"PUT",body:e,headers:o}),l=yield s.json();if(s.ok)return{data:{path:a,fullPath:l.Key},error:null};return{data:null,error:l}}catch(s){if(Rv(s))return{data:null,error:s};throw s}}))}createSignedUploadUrl(e,t){return Wv(this,void 0,void 0,(function*(){try{let n=this._getFinalPath(e);const r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");const a=yield qv(this.fetch,`${this.url}/object/upload/sign/${n}`,{},{headers:r}),o=new URL(this.url+a.url),i=o.searchParams.get("token");if(!i)throw new Nv("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:i},error:null}}catch(n){if(Rv(n))return{data:null,error:n};throw n}}))}update(e,t,n){return Wv(this,void 0,void 0,(function*(){return this.uploadOrUpdate("PUT",e,t,n)}))}move(e,t,n){return Wv(this,void 0,void 0,(function*(){try{return{data:yield qv(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==n?void 0:n.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(Rv(r))return{data:null,error:r};throw r}}))}copy(e,t,n){return Wv(this,void 0,void 0,(function*(){try{return{data:{path:(yield qv(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==n?void 0:n.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(Rv(r))return{data:null,error:r};throw r}}))}createSignedUrl(e,t,n){return Wv(this,void 0,void 0,(function*(){try{let r=this._getFinalPath(e),a=yield qv(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==n?void 0:n.transform)?{transform:n.transform}:{}),{headers:this.headers});const o=(null==n?void 0:n.download)?`&download=${!0===n.download?"":n.download}`:"";return a={signedUrl:encodeURI(`${this.url}${a.signedURL}${o}`)},{data:a,error:null}}catch(r){if(Rv(r))return{data:null,error:r};throw r}}))}createSignedUrls(e,t,n){return Wv(this,void 0,void 0,(function*(){try{const r=yield qv(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),a=(null==n?void 0:n.download)?`&download=${!0===n.download?"":n.download}`:"";return{data:r.map((e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${a}`):null}))),error:null}}catch(r){if(Rv(r))return{data:null,error:r};throw r}}))}download(e,t){return Wv(this,void 0,void 0,(function*(){const n=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),a=r?`?${r}`:"";try{const t=this._getFinalPath(e),r=yield Bv(this.fetch,`${this.url}/${n}/${t}${a}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(o){if(Rv(o))return{data:null,error:o};throw o}}))}info(e){return Wv(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{const e=yield Bv(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Dv(e),error:null}}catch(n){if(Rv(n))return{data:null,error:n};throw n}}))}exists(e){return Wv(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{return yield function(e,t,n,r){return zv(this,void 0,void 0,(function*(){return Fv(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)}))}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(n){if(Rv(n)&&n instanceof Lv){const e=n.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:n}}throw n}}))}getPublicUrl(e,t){const n=this._getFinalPath(e),r=[],a=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==a&&r.push(a);const o=void 0!==(null==t?void 0:t.transform)?"render/image":"object",i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==i&&r.push(i);let s=r.join("&");return""!==s&&(s=`?${s}`),{data:{publicUrl:encodeURI(`${this.url}/${o}/public/${n}${s}`)}}}remove(e){return Wv(this,void 0,void 0,(function*(){try{return{data:yield Vv(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(Rv(t))return{data:null,error:t};throw t}}))}list(e,t,n){return Wv(this,void 0,void 0,(function*(){try{const r=Object.assign(Object.assign(Object.assign({},Hv),t),{prefix:e||""});return{data:yield qv(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},n),error:null}}catch(r){if(Rv(r))return{data:null,error:r};throw r}}))}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Qv={"X-Client-Info":"storage-js/2.7.1"};var Gv=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(ok){o(ok)}}function s(e){try{l(r.throw(e))}catch(ok){o(ok)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};class Yv{constructor(e,t={},n){this.url=e,this.headers=Object.assign(Object.assign({},Qv),t),this.fetch=Av(n)}listBuckets(){return Gv(this,void 0,void 0,(function*(){try{return{data:yield Bv(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(Rv(e))return{data:null,error:e};throw e}}))}getBucket(e){return Gv(this,void 0,void 0,(function*(){try{return{data:yield Bv(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(Rv(t))return{data:null,error:t};throw t}}))}createBucket(e,t={public:!1}){return Gv(this,void 0,void 0,(function*(){try{return{data:yield qv(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(Rv(n))return{data:null,error:n};throw n}}))}updateBucket(e,t){return Gv(this,void 0,void 0,(function*(){try{const n=yield function(e,t,n,r,a){return zv(this,void 0,void 0,(function*(){return Fv(e,"PUT",t,r,a,n)}))}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:n,error:null}}catch(n){if(Rv(n))return{data:null,error:n};throw n}}))}emptyBucket(e){return Gv(this,void 0,void 0,(function*(){try{return{data:yield qv(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(Rv(t))return{data:null,error:t};throw t}}))}deleteBucket(e){return Gv(this,void 0,void 0,(function*(){try{return{data:yield Vv(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(Rv(t))return{data:null,error:t};throw t}}))}}class Xv extends Yv{constructor(e,t={},n){super(e,t,n)}from(e){return new Jv(this.url,this.headers,e,this.fetch)}}let Zv="";Zv="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const eb={headers:{"X-Client-Info":`supabase-js-${Zv}/2.49.4`}},tb={schema:"public"},nb={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},rb={};var ab=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(ok){o(ok)}}function s(e){try{l(r.throw(e))}catch(ok){o(ok)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const ob=(e,t,n)=>{const r=(e=>{let t;return t=e||("undefined"==typeof fetch?iy:fetch),(...e)=>t(...e)})(n),a="undefined"==typeof Headers?sy:Headers;return(n,o)=>ab(void 0,void 0,void 0,(function*(){var i;const s=null!==(i=yield t())&&void 0!==i?i:e;let l=new a(null==o?void 0:o.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${s}`),r(n,Object.assign(Object.assign({},o),{headers:l}))}))};var ib=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(ok){o(ok)}}function s(e){try{l(r.throw(e))}catch(ok){o(ok)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};const sb="2.69.1",lb=3e4,ub=9e4,cb={"X-Client-Info":`gotrue-js/${sb}`},db="X-Supabase-Api-Version",fb={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},hb=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class pb extends Error{constructor(e,t,n){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=n}}function mb(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class gb extends pb{constructor(e,t,n){super(e,t,n),this.name="AuthApiError",this.status=t,this.code=n}}class yb extends pb{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class vb extends pb{constructor(e,t,n,r){super(e,n,r),this.name=t,this.status=n}}class bb extends vb{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class wb extends vb{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class _b extends vb{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class kb extends vb{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Sb extends vb{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Eb extends vb{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function xb(e){return mb(e)&&"AuthRetryableFetchError"===e.name}class jb extends vb{constructor(e,t,n){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=n}}class Tb extends vb{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Pb="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Cb=" \t\n\r=".split(""),Ob=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Cb.length;t+=1)e[Cb[t].charCodeAt(0)]=-2;for(let t=0;t<Pb.length;t+=1)e[Pb[t].charCodeAt(0)]=t;return e})();function Nb(e,t,n){const r=Ob[e];if(!(r>-1)){if(-2===r)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function Rb(e){const t=[],n=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},a={queue:0,queuedBits:0},o=e=>{!function(e,t,n){if(0===t.utf8seq){if(e<=127)return void n(e);for(let n=1;n<6;n+=1)if(!(e>>7-n&1)){t.utf8seq=n;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&n(t.codepoint)}}(e,r,n)};for(let i=0;i<e.length;i+=1)Nb(e.charCodeAt(i),a,o);return t.join("")}function Ib(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function Lb(e){const t=[],n={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let a=0;a<e.length;a+=1)Nb(e.charCodeAt(a),n,r);return new Uint8Array(t)}function $b(e){const t=[];return function(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const t=1024*(r-55296)&65535;r=65536+(e.charCodeAt(n+1)-56320&65535|t),n+=1}Ib(r,t)}}(e,(e=>t.push(e))),new Uint8Array(t)}const Ab=()=>"undefined"!=typeof window&&"undefined"!=typeof document,Db={tested:!1,writable:!1},zb=()=>{if(!Ab())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(ok){return!1}if(Db.tested)return Db.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Db.tested=!0,Db.writable=!0}catch(ok){Db.tested=!0,Db.writable=!1}return Db.writable};const Ub=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Bp((()=>Promise.resolve().then((()=>cy))),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},Mb=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Fb=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch(r){return n}},Bb=async(e,t)=>{await e.removeItem(t)};class qb{constructor(){this.promise=new qb.promiseConstructor(((e,t)=>{this.resolve=e,this.reject=t}))}}function Vb(e){const t=e.split(".");if(3!==t.length)throw new Tb("Invalid JWT structure");for(let n=0;n<t.length;n++)if(!hb.test(t[n]))throw new Tb("JWT not in base64url format");return{header:JSON.parse(Rb(t[0])),payload:JSON.parse(Rb(t[1])),signature:Lb(t[2]),raw:{header:t[0],payload:t[1]}}}function Wb(e){return("0"+e.toString(16)).substr(-2)}async function Hb(e){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),n=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(n);return Array.from(r).map((e=>String.fromCharCode(e))).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Kb(e,t,n=!1){const r=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let n="";for(let r=0;r<56;r++)n+=e.charAt(Math.floor(Math.random()*t));return n}return crypto.getRandomValues(e),Array.from(e,Wb).join("")}();let a=r;n&&(a+="/PASSWORD_RECOVERY"),await Mb(e,`${t}-code-verifier`,a);const o=await Hb(r);return[o,r===o?"plain":"s256"]}qb.promiseConstructor=Promise;const Jb=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;var Qb=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};const Gb=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Yb=[502,503,504];async function Xb(e){var t,n;if(!("object"==typeof(n=e)&&null!==n&&"status"in n&&"ok"in n&&"json"in n&&"function"==typeof n.json))throw new Eb(Gb(e),0);if(Yb.includes(e.status))throw new Eb(Gb(e),e.status);let r,a;try{r=await e.json()}catch(ok){throw new yb(Gb(ok),ok)}const o=function(e){const t=e.headers.get(db);if(!t)return null;if(!t.match(Jb))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(ok){return null}}(e);if(o&&o.getTime()>=fb.timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?a=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(a=r.error_code),a){if("weak_password"===a)throw new jb(Gb(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===a)throw new bb}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce(((e,t)=>e&&"string"==typeof t),!0))throw new jb(Gb(r),e.status,r.weak_password.reasons);throw new gb(Gb(r),e.status||500,a)}async function Zb(e,t,n,r){var a;const o=Object.assign({},null==r?void 0:r.headers);o[db]||(o[db]=fb.name),(null==r?void 0:r.jwt)&&(o.Authorization=`Bearer ${r.jwt}`);const i=null!==(a=null==r?void 0:r.query)&&void 0!==a?a:{};(null==r?void 0:r.redirectTo)&&(i.redirect_to=r.redirectTo);const s=Object.keys(i).length?"?"+new URLSearchParams(i).toString():"",l=await async function(e,t,n,r,a,o){const i=((e,t,n,r)=>{const a={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?a:(a.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),a.body=JSON.stringify(r),Object.assign(Object.assign({},a),n))})(t,r,a,o);let s;try{s=await e(n,Object.assign({},i))}catch(ok){throw console.error(ok),new Eb(Gb(ok),0)}s.ok||await Xb(s);if(null==r?void 0:r.noResolveJson)return s;try{return await s.json()}catch(ok){await Xb(ok)}}(e,t,n+s,{headers:o,noResolveJson:null==r?void 0:r.noResolveJson},{},null==r?void 0:r.body);return(null==r?void 0:r.xform)?null==r?void 0:r.xform(l):{data:Object.assign({},l),error:null}}function ew(e){var t;let n=null;var r;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:n,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function tw(e){const t=ew(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce(((e,t)=>e&&"string"==typeof t),!0)&&(t.data.weak_password=e.weak_password),t}function nw(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function rw(e){return{data:e,error:null}}function aw(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:a,verification_type:o}=e,i=Qb(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:n,hashed_token:r,redirect_to:a,verification_type:o},user:Object.assign({},i)},error:null}}function ow(e){return e}var iw=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};class sw{constructor({url:e="",headers:t={},fetch:n}){this.url=e,this.headers=t,this.fetch=Ub(n),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t="global"){try{return await Zb(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(n){if(mb(n))return{data:null,error:n};throw n}}async inviteUserByEmail(e,t={}){try{return await Zb(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:nw})}catch(n){if(mb(n))return{data:{user:null},error:n};throw n}}async generateLink(e){try{const{options:t}=e,n=iw(e,["options"]),r=Object.assign(Object.assign({},n),t);return"newEmail"in n&&(r.new_email=null==n?void 0:n.newEmail,delete r.newEmail),await Zb(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:aw,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(mb(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await Zb(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:nw})}catch(t){if(mb(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,n,r,a,o,i,s;try{const l={nextPage:null,lastPage:0,total:0},u=await Zb(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(n=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==n?n:"",per_page:null!==(a=null===(r=null==e?void 0:e.perPage)||void 0===r?void 0:r.toString())&&void 0!==a?a:""},xform:ow});if(u.error)throw u.error;const c=await u.json(),d=null!==(o=u.headers.get("x-total-count"))&&void 0!==o?o:0,f=null!==(s=null===(i=u.headers.get("link"))||void 0===i?void 0:i.split(","))&&void 0!==s?s:[];return f.length>0&&(f.forEach((e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),n=JSON.parse(e.split(";")[1].split("=")[1]);l[`${n}Page`]=t})),l.total=parseInt(d)),{data:Object.assign(Object.assign({},c),l),error:null}}catch(l){if(mb(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){try{return await Zb(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:nw})}catch(t){if(mb(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){try{return await Zb(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:nw})}catch(n){if(mb(n))return{data:{user:null},error:n};throw n}}async deleteUser(e,t=!1){try{return await Zb(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:nw})}catch(n){if(mb(n))return{data:{user:null},error:n};throw n}}async _listFactors(e){try{const{data:t,error:n}=await Zb(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:n}}catch(t){if(mb(t))return{data:null,error:t};throw t}}async _deleteFactor(e){try{return{data:await Zb(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(mb(t))return{data:null,error:t};throw t}}}const lw={getItem:e=>zb()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{zb()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{zb()&&globalThis.localStorage.removeItem(e)}};function uw(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}const cw=!!(globalThis&&zb()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class dw extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class fw extends dw{}async function hw(e,t,n){cw&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout((()=>{r.abort(),cw&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)}),t),await Promise.resolve().then((()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},(async r=>{if(!r){if(0===t)throw cw&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new fw(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(cw)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(ok){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",ok)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}cw&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,r.name);try{return await n()}finally{cw&&console.log("@supabase/gotrue-js: navigatorLock: released",e,r.name)}}))))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(ok){"undefined"!=typeof self&&(self.globalThis=self)}}();const pw={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:cb,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function mw(e,t,n){return await n()}class gw{constructor(e){var t,n;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=gw.nextInstanceID,gw.nextInstanceID+=1,this.instanceID>0&&Ab()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const r=Object.assign(Object.assign({},pw),e);if(this.logDebugMessages=!!r.debug,"function"==typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new sw({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=Ub(r.fetch),this.lock=r.lock||mw,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:Ab()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=hw:this.lock=mw,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:zb()?this.storage=lw:(this.memoryStorage={},this.storage=uw(this.memoryStorage)):(this.memoryStorage={},this.storage=uw(this.memoryStorage)),Ab()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(ok){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",ok)}null===(n=this.broadcastChannel)||void 0===n||n.addEventListener("message",(async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)}))}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${sb}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,(async()=>await this._initialize())))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},n=new URL(e);if(n.hash&&"#"===n.hash[0])try{new URLSearchParams(n.hash.substring(1)).forEach(((e,n)=>{t[n]=e}))}catch(ok){}return n.searchParams.forEach(((e,n)=>{t[n]=e})),t}(window.location.href);let n="none";if(this._isImplicitGrantCallback(t)?n="implicit":await this._isPKCECallback(t)&&(n="pkce"),Ab()&&this.detectSessionInUrl&&"none"!==n){const{data:r,error:a}=await this._getSessionFromURL(t,n);if(a){if(this._debug("#_initialize()","error detecting session from URL",a),function(e){return mb(e)&&"AuthImplicitGrantRedirectError"===e.name}(a)){const t=null===(e=a.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:a}}return await this._removeSession(),{error:a}}const{session:o,redirectType:i}=r;return this._debug("#_initialize()","detected session in URL",o,"redirect type",i),await this._saveSession(o),setTimeout((async()=>{"recovery"===i?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)}),0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return mb(t)?{error:t}:{error:new yb("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,n,r;try{const a=await Zb(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(n=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==n?n:{},gotrue_meta_security:{captcha_token:null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken}},xform:ew}),{data:o,error:i}=a;if(i||!o)return{data:{user:null,session:null},error:i};const s=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(a){if(mb(a))return{data:{user:null,session:null},error:a};throw a}}async signUp(e){var t,n,r;try{let a;if("email"in e){const{email:n,password:r,options:o}=e;let i=null,s=null;"pkce"===this.flowType&&([i,s]=await Kb(this.storage,this.storageKey)),a=await Zb(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==o?void 0:o.emailRedirectTo,body:{email:n,password:r,data:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken},code_challenge:i,code_challenge_method:s},xform:ew})}else{if(!("phone"in e))throw new _b("You must provide either an email or phone number and a password");{const{phone:t,password:o,options:i}=e;a=await Zb(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:o,data:null!==(n=null==i?void 0:i.data)&&void 0!==n?n:{},channel:null!==(r=null==i?void 0:i.channel)&&void 0!==r?r:"sms",gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:ew})}}const{data:o,error:i}=a;if(i||!o)return{data:{user:null,session:null},error:i};const s=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(a){if(mb(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithPassword(e){try{let t;if("email"in e){const{email:n,password:r,options:a}=e;t=await Zb(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:n,password:r,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:tw})}else{if(!("phone"in e))throw new _b("You must provide either an email or phone number and a password");{const{phone:n,password:r,options:a}=e;t=await Zb(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:n,password:r,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:tw})}}const{data:n,error:r}=t;return r?{data:{user:null,session:null},error:r}:n&&n.session&&n.user?(n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign({user:n.user,session:n.session},n.weak_password?{weakPassword:n.weak_password}:null),error:r}):{data:{user:null,session:null},error:new wb}}catch(t){if(mb(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,n,r,a;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(n=e.options)||void 0===n?void 0:n.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:null===(a=e.options)||void 0===a?void 0:a.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,(async()=>this._exchangeCodeForSession(e)))}async _exchangeCodeForSession(e){const t=await Fb(this.storage,`${this.storageKey}-code-verifier`),[n,r]=(null!=t?t:"").split("/");try{const{data:t,error:a}=await Zb(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:n},xform:ew});if(await Bb(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=r?r:null}),error:a}):{data:{user:null,session:null,redirectType:null},error:new wb}}catch(a){if(mb(a))return{data:{user:null,session:null,redirectType:null},error:a};throw a}}async signInWithIdToken(e){try{const{options:t,provider:n,token:r,access_token:a,nonce:o}=e,i=await Zb(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:n,id_token:r,access_token:a,nonce:o,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:ew}),{data:s,error:l}=i;return l?{data:{user:null,session:null},error:l}:s&&s.session&&s.user?(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:s,error:l}):{data:{user:null,session:null},error:new wb}}catch(t){if(mb(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,n,r,a,o;try{if("email"in e){const{email:r,options:a}=e;let o=null,i=null;"pkce"===this.flowType&&([o,i]=await Kb(this.storage,this.storageKey));const{error:s}=await Zb(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:{},create_user:null===(n=null==a?void 0:a.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken},code_challenge:o,code_challenge_method:i},redirectTo:null==a?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:s}}if("phone"in e){const{phone:t,options:n}=e,{data:i,error:s}=await Zb(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(r=null==n?void 0:n.data)&&void 0!==r?r:{},create_user:null===(a=null==n?void 0:n.shouldCreateUser)||void 0===a||a,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},channel:null!==(o=null==n?void 0:n.channel)&&void 0!==o?o:"sms"}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:s}}throw new _b("You must provide either an email or phone number.")}catch(i){if(mb(i))return{data:{user:null,session:null},error:i};throw i}}async verifyOtp(e){var t,n;try{let r,a;"options"in e&&(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo,a=null===(n=e.options)||void 0===n?void 0:n.captchaToken);const{data:o,error:i}=await Zb(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:a}}),redirectTo:r,xform:ew});if(i)throw i;if(!o)throw new Error("An error occurred on token verification.");const s=o.session,l=o.user;return(null==s?void 0:s.access_token)&&(await this._saveSession(s),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(r){if(mb(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,n,r;try{let a=null,o=null;return"pkce"===this.flowType&&([a,o]=await Kb(this.storage,this.storageKey)),await Zb(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(n=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==n?n:void 0}),(null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:a,code_challenge_method:o}),headers:this.headers,xform:rw})}catch(a){if(mb(a))return{data:null,error:a};throw a}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._reauthenticate()))}async _reauthenticate(){try{return await this._useSession((async e=>{const{data:{session:t},error:n}=e;if(n)throw n;if(!t)throw new bb;const{error:r}=await Zb(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}}))}catch(e){if(mb(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:n,type:r,options:a}=e,{error:o}=await Zb(this.fetch,"POST",t,{headers:this.headers,body:{email:n,type:r,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},redirectTo:null==a?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:n,type:r,options:a}=e,{data:o,error:i}=await Zb(this.fetch,"POST",t,{headers:this.headers,body:{phone:n,type:r,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:i}}throw new _b("You must provide either an email or phone number and a type")}catch(t){if(mb(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,(async()=>this._useSession((async e=>e))))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),n=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await n}catch(ok){}})()),n}return await this.lock(`lock:${this.storageKey}`,e,(async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(ok){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await Fb(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const n=!!e.expires_at&&1e3*e.expires_at-Date.now()<ub;if(this._debug("#__loadSession()",`session has${n?"":" not"} expired`,"expires_at",e.expires_at),!n){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,n,r)=>(t||"user"!==n||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,n,r))})}return{data:{session:e},error:null}}const{session:r,error:a}=await this._callRefreshToken(e.refresh_token);return a?{data:{session:null},error:a}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,(async()=>await this._getUser()))}async _getUser(e){try{return e?await Zb(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:nw}):await this._useSession((async e=>{var t,n,r;const{data:a,error:o}=e;if(o)throw o;return(null===(t=a.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await Zb(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(r=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==r?r:void 0,xform:nw}):{data:{user:null},error:new bb}}))}catch(t){if(mb(t))return function(e){return mb(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await Bb(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._updateUser(e,t)))}async _updateUser(e,t={}){try{return await this._useSession((async n=>{const{data:r,error:a}=n;if(a)throw a;if(!r.session)throw new bb;const o=r.session;let i=null,s=null;"pkce"===this.flowType&&null!=e.email&&([i,s]=await Kb(this.storage,this.storageKey));const{data:l,error:u}=await Zb(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:i,code_challenge_method:s}),jwt:o.access_token,xform:nw});if(u)throw u;return o.user=l.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}}))}catch(n){if(mb(n))return{data:{user:null},error:n};throw n}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._setSession(e)))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new bb;const t=Date.now()/1e3;let n=t,r=!0,a=null;const{payload:o}=Vb(e.access_token);if(o.exp&&(n=o.exp,r=n<=t),r){const{session:t,error:n}=await this._callRefreshToken(e.refresh_token);if(n)return{data:{user:null,session:null},error:n};if(!t)return{data:{user:null,session:null},error:null};a=t}else{const{data:r,error:o}=await this._getUser(e.access_token);if(o)throw o;a={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:n-t,expires_at:n},await this._saveSession(a),await this._notifyAllSubscribers("SIGNED_IN",a)}return{data:{user:a.user,session:a},error:null}}catch(t){if(mb(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._refreshSession(e)))}async _refreshSession(e){try{return await this._useSession((async t=>{var n;if(!e){const{data:r,error:a}=t;if(a)throw a;e=null!==(n=r.session)&&void 0!==n?n:void 0}if(!(null==e?void 0:e.refresh_token))throw new bb;const{session:r,error:a}=await this._callRefreshToken(e.refresh_token);return a?{data:{user:null,session:null},error:a}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}))}catch(t){if(mb(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!Ab())throw new kb("No browser detected.");if(e.error||e.error_description||e.error_code)throw new kb(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new Sb("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new kb("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Sb("No code detected.");const{data:t,error:n}=await this._exchangeCodeForSession(e.code);if(n)throw n;const r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:n,provider_refresh_token:r,access_token:a,refresh_token:o,expires_in:i,expires_at:s,token_type:l}=e;if(!(a&&i&&o&&l))throw new kb("No session defined in URL");const u=Math.round(Date.now()/1e3),c=parseInt(i);let d=u+c;s&&(d=parseInt(s));const f=d-u;1e3*f<=lb&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${f}s, should have been closer to ${c}s`);const h=d-c;u-h>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",h,d,u):u-h<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",h,d,u);const{data:p,error:m}=await this._getUser(a);if(m)throw m;const g={provider_token:n,provider_refresh_token:r,access_token:a,expires_in:c,expires_at:d,refresh_token:o,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:g,redirectType:e.type},error:null}}catch(n){if(mb(n))return{data:{session:null,redirectType:null},error:n};throw n}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await Fb(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._signOut(e)))}async _signOut({scope:e}={scope:"global"}){return await this._useSession((async t=>{var n;const{data:r,error:a}=t;if(a)return{error:a};const o=null===(n=r.session)||void 0===n?void 0:n.access_token;if(o){const{error:t}=await this.admin.signOut(o,e);if(t&&(!function(e){return mb(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await Bb(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})),n={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,n),(async()=>{await this.initializePromise,await this._acquireLock(-1,(async()=>{this._emitInitialSession(t)}))})(),{data:{subscription:n}}}async _emitInitialSession(e){return await this._useSession((async t=>{var n,r;try{const{data:{session:r},error:a}=t;if(a)throw a;await(null===(n=this.stateChangeEmitters.get(e))||void 0===n?void 0:n.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(a){await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",a),console.error(a)}}))}async resetPasswordForEmail(e,t={}){let n=null,r=null;"pkce"===this.flowType&&([n,r]=await Kb(this.storage,this.storageKey,!0));try{return await Zb(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:n,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(a){if(mb(a))return{data:null,error:a};throw a}}async getUserIdentities(){var e;try{const{data:t,error:n}=await this.getUser();if(n)throw n;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(mb(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:n,error:r}=await this._useSession((async t=>{var n,r,a,o,i;const{data:s,error:l}=t;if(l)throw l;const u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(n=e.options)||void 0===n?void 0:n.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(a=e.options)||void 0===a?void 0:a.queryParams,skipBrowserRedirect:!0});return await Zb(this.fetch,"GET",u,{headers:this.headers,jwt:null!==(i=null===(o=s.session)||void 0===o?void 0:o.access_token)&&void 0!==i?i:void 0})}));if(r)throw r;return Ab()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==n?void 0:n.url),{data:{provider:e.provider,url:null==n?void 0:n.url},error:null}}catch(n){if(mb(n))return{data:{provider:e.provider,url:null},error:n};throw n}}async unlinkIdentity(e){try{return await this._useSession((async t=>{var n,r;const{data:a,error:o}=t;if(o)throw o;return await Zb(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(r=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==r?r:void 0})}))}catch(t){if(mb(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const n=Date.now();return await function(e,t){return new Promise(((n,r)=>{(async()=>{for(let a=0;a<1/0;a++)try{const r=await e(a);if(!t(a,null,r))return void n(r)}catch(ok){if(!t(a,ok))return void r(ok)}})()}))}((async n=>(n>0&&await async function(e){return await new Promise((t=>{setTimeout((()=>t(null)),e)}))}(200*Math.pow(2,n-1)),this._debug(t,"refreshing attempt",n),await Zb(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:ew}))),((e,t)=>{const r=200*Math.pow(2,e);return t&&xb(t)&&Date.now()+r-n<lb}))}catch(n){if(this._debug(t,"error",n),mb(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const n=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",n),Ab()&&!t.skipBrowserRedirect&&window.location.assign(n),{data:{provider:e,url:n},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const n=await Fb(this.storage,this.storageKey);if(this._debug(t,"session from storage",n),!this._isValidSession(n))return this._debug(t,"session is not valid"),void(null!==n&&await this._removeSession());const r=1e3*(null!==(e=n.expires_at)&&void 0!==e?e:1/0)-Date.now()<ub;if(this._debug(t,`session has${r?"":" not"} expired with margin of 90000s`),r){if(this.autoRefreshToken&&n.refresh_token){const{error:e}=await this._callRefreshToken(n.refresh_token);e&&(console.error(e),xb(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){return this._debug(t,"error",n),void console.error(n)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,n;if(!e)throw new bb;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new qb;const{data:t,error:n}=await this._refreshAccessToken(e);if(n)throw n;if(!t.session)throw new bb;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(a){if(this._debug(r,"error",a),mb(a)){const e={session:null,error:a};return xb(a)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(n=this.refreshingDeferred)||void 0===n||n.reject(a),a}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,n=!0){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${n}`);try{this.broadcastChannel&&n&&this.broadcastChannel.postMessage({event:e,session:t});const r=[],a=Array.from(this.stateChangeEmitters.values()).map((async n=>{try{await n.callback(e,t)}catch(ok){r.push(ok)}}));if(await Promise.all(a),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await Mb(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await Bb(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&Ab()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(ok){console.error("removing visibilitychange callback failed",ok)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval((()=>this._autoRefreshTokenTick()),lb);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout((async()=>{await this.initializePromise,await this._autoRefreshTokenTick()}),0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,(async()=>{try{const e=Date.now();try{return await this._useSession((async t=>{const{data:{session:n}}=t;if(!n||!n.refresh_token||!n.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const r=Math.floor((1e3*n.expires_at-e)/lb);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),r<=3&&await this._callRefreshToken(n.refresh_token)}))}catch(ok){console.error("Auto refresh tick failed with error. This is likely a transient error.",ok)}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(ok){if(!(ok.isAcquireTimeout||ok instanceof dw))throw ok;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Ab()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,(async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")})))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,n){const r=[`provider=${encodeURIComponent(t)}`];if((null==n?void 0:n.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(n.redirectTo)}`),(null==n?void 0:n.scopes)&&r.push(`scopes=${encodeURIComponent(n.scopes)}`),"pkce"===this.flowType){const[e,t]=await Kb(this.storage,this.storageKey),n=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(n.toString())}if(null==n?void 0:n.queryParams){const e=new URLSearchParams(n.queryParams);r.push(e.toString())}return(null==n?void 0:n.skipBrowserRedirect)&&r.push(`skip_http_redirect=${n.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession((async t=>{var n;const{data:r,error:a}=t;return a?{data:null,error:a}:await Zb(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(n=null==r?void 0:r.session)||void 0===n?void 0:n.access_token})}))}catch(t){if(mb(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession((async t=>{var n,r;const{data:a,error:o}=t;if(o)return{data:null,error:o};const i=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:s,error:l}=await Zb(this.fetch,"POST",`${this.url}/factors`,{body:i,headers:this.headers,jwt:null===(n=null==a?void 0:a.session)||void 0===n?void 0:n.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(r=null==s?void 0:s.totp)||void 0===r?void 0:r.qr_code)&&(s.totp.qr_code=`data:image/svg+xml;utf-8,${s.totp.qr_code}`),{data:s,error:null})}))}catch(t){if(mb(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async t=>{var n;const{data:r,error:a}=t;if(a)return{data:null,error:a};const{data:o,error:i}=await Zb(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(n=null==r?void 0:r.session)||void 0===n?void 0:n.access_token});return i?{data:null,error:i}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:i})}))}catch(t){if(mb(t))return{data:null,error:t};throw t}}))}async _challenge(e){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async t=>{var n;const{data:r,error:a}=t;return a?{data:null,error:a}:await Zb(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(n=null==r?void 0:r.session)||void 0===n?void 0:n.access_token})}))}catch(t){if(mb(t))return{data:null,error:t};throw t}}))}async _challengeAndVerify(e){const{data:t,error:n}=await this._challenge({factorId:e.factorId});return n?{data:null,error:n}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const n=(null==e?void 0:e.factors)||[],r=n.filter((e=>"totp"===e.factor_type&&"verified"===e.status)),a=n.filter((e=>"phone"===e.factor_type&&"verified"===e.status));return{data:{all:n,totp:r,phone:a},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,(async()=>await this._useSession((async e=>{var t,n;const{data:{session:r},error:a}=e;if(a)return{data:null,error:a};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Vb(r.access_token);let i=null;o.aal&&(i=o.aal);let s=i;(null!==(n=null===(t=r.user.factors)||void 0===t?void 0:t.filter((e=>"verified"===e.status)))&&void 0!==n?n:[]).length>0&&(s="aal2");return{data:{currentLevel:i,nextLevel:s,currentAuthenticationMethods:o.amr||[]},error:null}}))))}async fetchJwk(e,t={keys:[]}){let n=t.keys.find((t=>t.kid===e));if(n)return n;if(n=this.jwks.keys.find((t=>t.kid===e)),n&&this.jwks_cached_at+6e5>Date.now())return n;const{data:r,error:a}=await Zb(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(a)throw a;if(!r.keys||0===r.keys.length)throw new Tb("JWKS is empty");if(this.jwks=r,this.jwks_cached_at=Date.now(),n=r.keys.find((t=>t.kid===e)),!n)throw new Tb("No matching signing key found in JWKS");return n}async getClaims(e,t={keys:[]}){try{let n=e;if(!n){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};n=e.session.access_token}const{header:r,payload:a,signature:o,raw:{header:i,payload:s}}=Vb(n);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(a.exp),!r.kid||"HS256"===r.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(n);if(e)throw e;return{data:{claims:a,header:r,signature:o},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(r.alg),u=await this.fetchJwk(r.kid,t),c=await crypto.subtle.importKey("jwk",u,l,!0,["verify"]);if(!(await crypto.subtle.verify(l,c,o,$b(`${i}.${s}`))))throw new Tb("Invalid JWT signature");return{data:{claims:a,header:r,signature:o},error:null}}catch(n){if(mb(n))return{data:null,error:n};throw n}}}gw.nextInstanceID=0;const yw=gw;class vw extends yw{constructor(e){super(e)}}var bw=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,o){function i(e){try{l(r.next(e))}catch(ok){o(ok)}}function s(e){try{l(r.throw(e))}catch(ok){o(ok)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))};class ww{constructor(e,t,n){var r,a,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const i=e.replace(/\/$/,"");this.realtimeUrl=`${i}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${i}/auth/v1`,this.storageUrl=`${i}/storage/v1`,this.functionsUrl=`${i}/functions/v1`;const s=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,l=function(e,t){const{db:n,auth:r,realtime:a,global:o}=e,{db:i,auth:s,realtime:l,global:u}=t,c={db:Object.assign(Object.assign({},i),n),auth:Object.assign(Object.assign({},s),r),realtime:Object.assign(Object.assign({},l),a),global:Object.assign(Object.assign({},u),o),accessToken:()=>ib(this,void 0,void 0,(function*(){return""}))};return e.accessToken?c.accessToken=e.accessToken:delete c.accessToken,c}(null!=n?n:{},{db:tb,realtime:rb,auth:Object.assign(Object.assign({},nb),{storageKey:s}),global:eb});this.storageKey=null!==(r=l.auth.storageKey)&&void 0!==r?r:"",this.headers=null!==(a=l.global.headers)&&void 0!==a?a:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(o=l.auth)&&void 0!==o?o:{},this.headers,l.global.fetch),this.fetch=ob(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new My(`${i}/rest/v1`,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new Yg(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Xv(this.storageUrl,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},n={}){return this.rest.rpc(e,t,n)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return bw(this,void 0,void 0,(function*(){if(this.accessToken)return yield this.accessToken();const{data:n}=yield this.auth.getSession();return null!==(t=null===(e=n.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null}))}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:n,storage:r,storageKey:a,flowType:o,lock:i,debug:s},l,u){const c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new vw({url:this.authUrl,headers:Object.assign(Object.assign({},c),l),storageKey:a,autoRefreshToken:e,persistSession:t,detectSessionInUrl:n,storage:r,flowType:o,lock:i,debug:s,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new Cv(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange(((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)}))}_handleTokenChanged(e,t,n){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===n?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=n}}var _w;const kw=new ww("https://yjghsqedyrriieadxzwa.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlqZ2hzcWVkeXJyaWllYWR4endhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3MTAwMDQsImV4cCI6MjA2MTI4NjAwNH0.WycQks-ERCoGEI8xxgcMg3H57fXPovwsQdk2a-x2yh0",_w),Sw="app_cache_version",Ew="1.0.0",xw="app_cache_",jw=e=>`${xw}${e}`,Tw=()=>localStorage.getItem(Sw)===Ew,Pw=()=>{localStorage.setItem(Sw,Ew)},Cw=(e,t,n=36e5)=>{try{Tw()||(Nw(),Pw());const r=jw(e),a=(new Date).getTime(),o={data:t,expiration:a+n,timestamp:a};return localStorage.setItem(r,JSON.stringify(o)),!0}catch(r){return console.error("Error al guardar en caché:",r),!1}},Ow=e=>{try{if(!Tw())return Nw(),Pw(),null;const t=jw(e),n=localStorage.getItem(t);if(!n)return null;const r=JSON.parse(n);return(new Date).getTime()>r.expiration?(localStorage.removeItem(t),null):r.data}catch(t){return console.error("Error al obtener de caché:",t),null}},Nw=()=>{try{const e=Object.keys(localStorage);return e.filter((e=>e.startsWith(xw))).forEach((e=>localStorage.removeItem(e))),!0}catch(e){return console.error("Error al limpiar caché:",e),!1}},Rw=Cw,Iw=Ow,Lw={"auth/invalid-email":"El correo electrónico no es válido.","auth/user-disabled":"Esta cuenta ha sido deshabilitada.","auth/user-not-found":"No existe una cuenta con este correo electrónico.","auth/wrong-password":"La contraseña es incorrecta.","auth/email-already-in-use":"Este correo electrónico ya está en uso.","auth/weak-password":"La contraseña es demasiado débil.",42501:"No tienes permisos para realizar esta operación. Verifica tus permisos de acceso.",23503:"Error de referencia: No se puede eliminar o modificar porque hay registros relacionados.",23505:"Ya existe un registro con estos datos. No se permiten duplicados.","22P02":"Formato de datos inválido. Verifica que los datos ingresados sean correctos.",23502:"Falta un valor requerido. Por favor, completa todos los campos obligatorios.","connection-error":"Error de conexión. Verifica tu conexión a internet.","timeout-error":"La operación ha tardado demasiado tiempo. Inténtalo de nuevo más tarde.","unknown-error":"Ha ocurrido un error inesperado. Por favor, inténtalo de nuevo más tarde.","institution-not-found":"La institución no existe o ha sido eliminada.","institution-name-required":"El nombre de la institución es obligatorio.","institution-name-duplicate":"Ya existe una institución con este nombre.","institution-in-use":"No se puede eliminar la institución porque está siendo utilizada por psicólogos o pacientes.","psychologist-not-found":"El psicólogo no existe o ha sido eliminado.","psychologist-email-required":"El correo electrónico del psicólogo es obligatorio.","psychologist-email-duplicate":"Ya existe un psicólogo con este correo electrónico.","psychologist-in-use":"No se puede eliminar el psicólogo porque tiene pacientes asignados.","psychologist-auth-failed":"No se pudo crear la cuenta de usuario para el psicólogo.","psychologist-institution-required":"La institución del psicólogo es obligatoria.","patient-not-found":"El paciente no existe o ha sido eliminado.","patient-name-required":"El nombre del paciente es obligatorio.","patient-birthdate-required":"La fecha de nacimiento del paciente es obligatoria.","patient-birthdate-invalid":"La fecha de nacimiento del paciente no es válida.","patient-gender-required":"El género del paciente es obligatorio.","patient-institution-required":"La institución del paciente es obligatoria."},$w=(e,t="",n="")=>{if(!e)return"Ha ocurrido un error desconocido.";const r=e.code||e.error&&e.error.code||"";let a="";if(e.message?a=e.message:e.error&&e.error.message?a=e.error.message:"string"==typeof e&&(a=e),r&&Lw[r])return Lw[r];if(t&&n){const e="institución"===n||"usuario"===n?"la":"el",r="institución"===n?"ella":"él",o="institución"===n?"una":"un";if("institución"===n){if(a.includes("nombre")&&a.includes("null"))return"El nombre de la institución es obligatorio.";if(a.includes("duplicate")&&a.includes("nombre"))return"Ya existe una institución con este nombre.";if("eliminar"===t&&a.includes("foreign key"))return"No se puede eliminar la institución porque está siendo utilizada por psicólogos o pacientes."}else if("psicólogo"===n){if(a.includes("email")&&a.includes("null"))return"El correo electrónico del psicólogo es obligatorio.";if(a.includes("duplicate")&&a.includes("email"))return"Ya existe un psicólogo con este correo electrónico.";if(a.includes("institucion_id")&&a.includes("null"))return"La institución del psicólogo es obligatoria.";if("eliminar"===t&&a.includes("foreign key"))return"No se puede eliminar el psicólogo porque tiene pacientes asignados.";if("crear"===t&&a.includes("auth"))return"No se pudo crear la cuenta de usuario para el psicólogo."}else if("paciente"===n){if(a.includes("nombre")&&a.includes("null"))return"El nombre del paciente es obligatorio.";if(a.includes("fecha_nacimiento")&&a.includes("null"))return"La fecha de nacimiento del paciente es obligatoria.";if(a.includes("genero")&&a.includes("null"))return"El género del paciente es obligatorio.";if(a.includes("institucion_id")&&a.includes("null"))return"La institución del paciente es obligatoria."}if("crear"===t)return a.includes("duplicate key")?`Ya existe ${o} ${n} con estos datos.`:a.includes("violates foreign key constraint")?`No se puede crear ${e} ${n} porque hace referencia a datos que no existen.`:`Error al crear ${e} ${n}: ${a}`;if("actualizar"===t)return a.includes("duplicate key")?`Ya existe ${o} ${n} con estos datos.`:a.includes("violates foreign key constraint")?`No se puede actualizar ${e} ${n} porque hace referencia a datos que no existen.`:`Error al actualizar ${e} ${n}: ${a}`;if("eliminar"===t)return a.includes("violates foreign key constraint")?`No se puede eliminar ${e} ${n} porque hay registros que dependen de ${r}.`:`Error al eliminar ${e} ${n}: ${a}`}return a||"Ha ocurrido un error inesperado. Por favor, inténtalo de nuevo más tarde."},Aw=(e,t="",n="",r={})=>{const a=$w(e,t,n);yh.error(a,{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0,...r}),console.error(`Error (${t} ${n}):`,e)},Dw=(e,t={})=>{yh.success(e,{position:"top-right",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0,...t})},zw=(e,t)=>{switch(e){case"instituciones":return(e=>{const t={};e.nombre?e.nombre.length<3?t.nombre="El nombre debe tener al menos 3 caracteres":e.nombre.length>100&&(t.nombre="El nombre no puede exceder los 100 caracteres"):t.nombre="El nombre es obligatorio",e.direccion&&e.direccion.length>200&&(t.direccion="La dirección no puede exceder los 200 caracteres"),e.telefono&&(/^[0-9+\-\s()]{7,20}$/.test(e.telefono)||(t.telefono="El teléfono debe tener un formato válido"));return{isValid:0===Object.keys(t).length,errors:t}})(t);case"psicologos":return(e=>{const t={};e.nombre?e.nombre.length<2?t.nombre="El nombre debe tener al menos 2 caracteres":e.nombre.length>50&&(t.nombre="El nombre no puede exceder los 50 caracteres"):t.nombre="El nombre es obligatorio",e.apellidos?e.apellidos.length<2?t.apellidos="Los apellidos deben tener al menos 2 caracteres":e.apellidos.length>50&&(t.apellidos="Los apellidos no pueden exceder los 50 caracteres"):t.apellidos="Los apellidos son obligatorios",e.email?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||(t.email="El email debe tener un formato válido"):t.email="El email es obligatorio";e.documento_identidad?e.documento_identidad.length<5?t.documento_identidad="El documento de identidad debe tener al menos 5 caracteres":e.documento_identidad.length>20&&(t.documento_identidad="El documento de identidad no puede exceder los 20 caracteres"):t.documento_identidad="El documento de identidad es obligatorio",e.telefono&&(/^[0-9+\-\s()]{7,20}$/.test(e.telefono)||(t.telefono="El teléfono debe tener un formato válido"));return e.institucion_id||(t.institucion_id="La institución es obligatoria"),e.usuario_id||(t.usuario_id="El usuario es obligatorio"),{isValid:0===Object.keys(t).length,errors:t}})(t);case"pacientes":return(e=>{const t={};if(e.nombre?e.nombre.length<3?t.nombre="El nombre debe tener al menos 3 caracteres":e.nombre.length>100&&(t.nombre="El nombre no puede exceder los 100 caracteres"):t.nombre="El nombre es obligatorio",e.fecha_nacimiento){const n=new Date(e.fecha_nacimiento),r=new Date;if(isNaN(n.getTime()))t.fecha_nacimiento="La fecha de nacimiento no es válida";else if(n>r)t.fecha_nacimiento="La fecha de nacimiento no puede ser en el futuro";else{let e=r.getFullYear()-n.getFullYear();const a=r.getMonth()-n.getMonth();(a<0||0===a&&r.getDate()<n.getDate())&&e--,e<0?t.fecha_nacimiento="La fecha de nacimiento no puede ser en el futuro":e>120&&(t.fecha_nacimiento="La edad no puede ser mayor a 120 años")}}else t.fecha_nacimiento="La fecha de nacimiento es obligatoria";return e.genero?["Masculino","Femenino","Otro","No especificado"].includes(e.genero)||(t.genero="El género debe ser uno de los valores permitidos"):t.genero="El género es obligatorio",e.institucion_id||(t.institucion_id="La institución es obligatoria"),e.notas&&e.notas.length>500&&(t.notas="Las notas no pueden exceder los 500 caracteres"),{isValid:0===Object.keys(t).length,errors:t}})(t);default:return{isValid:!0,errors:{}}}},Uw="institutions",Mw="psychologists",Fw="patients",Bw="pending_operations",qw=18e5,Vw=864e5,Ww=e=>"string"==typeof e&&e.startsWith("temp-"),Hw=()=>`temp-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,Kw=async()=>{try{const{data:{user:e},error:t}=await kw.auth.getUser();if(t)throw t;return{user:e,error:null}}catch(e){return console.error("[EnhancedService] Error al obtener usuario actual:",e),{user:null,error:e}}},Jw=e=>{const t=Iw(Bw)||[];t.push({...e,timestamp:Date.now()}),Rw(Bw,t,Vw)},Qw=(e,t,n)=>{const r=(Iw(Bw)||[]).filter((r=>!(r.type===e&&r.entity===t&&(r.id===n||r.data&&r.data.id===n))));Rw(Bw,r,Vw)},Gw=async(e="nombre",t="asc")=>{try{const n=Iw(Uw);console.log("[EnhancedService] Obteniendo instituciones de Supabase...");const{data:r,error:a}=await kw.from("instituciones").select("*").order(e,{ascending:"asc"===t});if(a){if(console.warn("[EnhancedService] Error al obtener instituciones de Supabase:",a),n){console.log("[EnhancedService] Usando datos de caché para instituciones.");const r=[...n].sort(((n,r)=>n[e]<r[e]?"asc"===t?-1:1:n[e]>r[e]?"asc"===t?1:-1:0));return{data:r,error:null}}return{data:[],error:{message:$w(a,"obtener","instituciones"),original:a}}}return r?(console.log("[EnhancedService] Instituciones obtenidas con éxito."),Rw(Uw,r,qw),{data:r,error:null}):{data:[],error:null}}catch(n){console.error("[EnhancedService] Error inesperado al obtener instituciones:",n);const e=Iw(Uw);return e?(console.log("[EnhancedService] Usando datos de caché para instituciones debido a error."),{data:e,error:{message:$w(n,"obtener","instituciones"),original:n}}):{data:[],error:{message:$w(n,"obtener","instituciones"),original:n}}}},Yw=async(e="nombre",t="asc")=>{try{const n=Iw(Mw);console.log("[EnhancedService] Obteniendo psicólogos de Supabase...");const{data:r,error:a}=await kw.from("psicologos").select("\n        *,\n        instituciones:institucion_id (id, nombre),\n        usuarios:usuario_id (email)\n      ").order(e,{ascending:"asc"===t});if(a){if(console.warn("[EnhancedService] Error al obtener psicólogos de Supabase:",a),n){console.log("[EnhancedService] Usando datos de caché para psicólogos.");const r=[...n].sort(((n,r)=>n[e]<r[e]?"asc"===t?-1:1:n[e]>r[e]?"asc"===t?1:-1:0));return{data:r,error:null,isOffline:!0}}return{data:[],error:{message:$w(a,"obtener","psicólogos"),original:a}}}return r?(console.log("[EnhancedService] Psicólogos obtenidos con éxito."),Rw(Mw,r,qw),{data:r,error:null}):{data:[],error:null}}catch(n){console.error("[EnhancedService] Error inesperado al obtener psicólogos:",n);const e=Iw(Mw);return e?(console.log("[EnhancedService] Usando datos de caché para psicólogos debido a error."),{data:e,error:{message:$w(n,"obtener","psicólogos"),original:n},isOffline:!0}):{data:[],error:{message:$w(n,"obtener","psicólogos"),original:n}}}},Xw=async(e="nombre",t="asc")=>{try{const n=Iw(Fw);console.log("[EnhancedService] Obteniendo pacientes de Supabase...");const{data:r,error:a}=await kw.from("pacientes").select("\n        *,\n        instituciones:institucion_id (id, nombre),\n        psicologos:psicologo_id (id, nombre, apellidos)\n      ").order(e,{ascending:"asc"===t});if(a){if(console.warn("[EnhancedService] Error al obtener pacientes de Supabase:",a),n){console.log("[EnhancedService] Usando datos de caché para pacientes.");const r=[...n].sort(((n,r)=>n[e]<r[e]?"asc"===t?-1:1:n[e]>r[e]?"asc"===t?1:-1:0));return{data:r,error:null,isOffline:!0}}return{data:[],error:{message:$w(a,"obtener","pacientes"),original:a}}}if(r){console.log("[EnhancedService] Pacientes obtenidos con éxito.");const e=r.map((e=>{if(e.fecha_nacimiento){const t=new Date,n=new Date(e.fecha_nacimiento);let r=t.getFullYear()-n.getFullYear();const a=t.getMonth()-n.getMonth();return(a<0||0===a&&t.getDate()<n.getDate())&&r--,{...e,edad:r}}return e}));return Rw(Fw,e,qw),{data:e,error:null}}return{data:[],error:null}}catch(n){console.error("[EnhancedService] Error inesperado al obtener pacientes:",n);const e=Iw(Fw);return e?(console.log("[EnhancedService] Usando datos de caché para pacientes debido a error."),{data:e,error:{message:$w(n,"obtener","pacientes"),original:n},isOffline:!0}):{data:[],error:{message:$w(n,"obtener","pacientes"),original:n}}}},Zw={getInstitutions:Gw,createInstitution:async e=>{try{const n=zw("instituciones",e);if(!n.isValid)return{data:null,error:{message:"Datos de institución inválidos",validationErrors:n.errors,original:new Error("Validation failed")}};const{user:r,error:a}=await Kw();if(a||!r)throw a||new Error("Usuario no autenticado para crear institución");const o={...e,created_by:r.id,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()};try{console.log("[EnhancedService] Intentando insertar institución:",o);const{data:e,error:t}=await kw.from("instituciones").insert([o]).select().single();if(t)throw console.error("[EnhancedService] Error Supabase al insertar institución:",t),t;if(e){console.log("[EnhancedService] Institución insertada con éxito:",e);const t=Iw(Uw)||[];return Rw(Uw,[...t,e],qw),{data:e,error:null}}throw new Error("No se devolvieron datos después de la inserción.")}catch(t){console.warn("[EnhancedService] Error al insertar institución en Supabase, guardando localmente:",t);const e=Hw(),n={...o,id:e,is_temporary:!0},r=Iw(Uw)||[];return Rw(Uw,[...r,n],qw),Jw({type:"CREATE",entity:"instituciones",data:o,tempId:e}),{data:n,error:null,isOffline:!0}}}catch(n){return console.error("[EnhancedService] Error general al crear institución:",n),{data:null,error:{message:$w(n,"crear","institución"),original:n}}}},updateInstitution:async(e,t)=>{try{console.log(`[EnhancedService] Intentando actualizar institución ID: ${e}`,t);const r={...t,updated_at:(new Date).toISOString()};if(delete r.id,delete r.created_at,delete r.created_by,Ww(e)){console.log(`[EnhancedService] Actualizando institución temporal ID: ${e}`);const t=(Iw(Uw)||[]).map((t=>t.id===e?{...t,...r}:t));Rw(Uw,t,qw);const n=(Iw(Bw)||[]).map((t=>"CREATE"===t.type&&"instituciones"===t.entity&&t.tempId===e?{...t,data:{...t.data,...r}}:t));Rw(Bw,n,Vw);return{data:t.find((t=>t.id===e)),error:null,isOffline:!0}}try{const{data:t,error:n}=await kw.from("instituciones").update(r).eq("id",e).select().single();if(n)throw console.error(`[EnhancedService] Error Supabase al actualizar institución ${e}:`,n),n;if(t){console.log(`[EnhancedService] Institución ${e} actualizada con éxito:`,t);const n=(Iw(Uw)||[]).map((n=>n.id===e?t:n));return Rw(Uw,n,qw),{data:t,error:null}}throw new Error("No se devolvieron datos después de la actualización.")}catch(n){console.warn(`[EnhancedService] Error al actualizar institución ${e} en Supabase, guardando localmente:`,n);const t=(Iw(Uw)||[]).map((t=>t.id===e?{...t,...r,is_pending_update:!0}:t));Rw(Uw,t,qw),Jw({type:"UPDATE",entity:"instituciones",id:e,data:r});return{data:t.find((t=>t.id===e)),error:null,isOffline:!0}}}catch(r){return console.error(`[EnhancedService] Error general al actualizar institución ${e}:`,r),{data:null,error:{message:$w(r,"actualizar","institución"),original:r}}}},deleteInstitution:async e=>{try{console.log(`[EnhancedService] Intentando eliminar institución ID: ${e}`);const n=Iw(Uw)||[],r=n.find((t=>t.id===e));if(Ww(e)){console.log(`[EnhancedService] Eliminando institución temporal ID: ${e}`);const t=n.filter((t=>t.id!==e));return Rw(Uw,t,qw),Qw("CREATE","instituciones",e),{data:{success:!0},error:null}}try{const{error:t}=await kw.from("instituciones").delete().eq("id",e);if(t)throw console.error(`[EnhancedService] Error Supabase al eliminar institución ${e}:`,t),t;console.log(`[EnhancedService] Institución ${e} eliminada con éxito.`);const r=n.filter((t=>t.id!==e));return Rw(Uw,r,qw),{data:{success:!0},error:null}}catch(t){console.warn(`[EnhancedService] Error al eliminar institución ${e} en Supabase, guardando localmente:`,t);const a=n.map((t=>t.id===e?{...t,is_pending_delete:!0}:t));return Rw(Uw,a,qw),Jw({type:"DELETE",entity:"instituciones",id:e,data:r}),{data:{success:!0},error:null,isOffline:!0}}}catch(n){return console.error(`[EnhancedService] Error general al eliminar institución ${e}:`,n),{data:null,error:{message:$w(n,"eliminar","institución"),original:n}}}},getPsychologists:Yw,createPsychologist:async e=>{try{const n=zw("psicologos",e);if(!n.isValid)return{data:null,error:{message:"Datos de psicólogo inválidos",validationErrors:n.errors,original:new Error("Validation failed")}};const{user:r,error:a}=await Kw();if(a||!r)throw a||new Error("Usuario no autenticado para crear psicólogo");const o={...e,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()};try{console.log("[EnhancedService] Intentando insertar psicólogo:",o);const{data:e,error:t}=await kw.from("psicologos").insert([o]).select().single();if(t)throw console.error("[EnhancedService] Error Supabase al insertar psicólogo:",t),"23505"===t.code?console.error("[EnhancedService] Error: El email o usuario_id ya existe."):"23503"===t.code&&console.error("[EnhancedService] Error: La institución especificada no existe."),t;if(e){console.log("[EnhancedService] Psicólogo insertado con éxito:",e);const t=Iw(Mw)||[];return Rw(Mw,[...t,e],qw),{data:e,error:null}}throw new Error("No se devolvieron datos después de la inserción.")}catch(t){console.warn("[EnhancedService] Error al insertar psicólogo en Supabase, guardando localmente:",t);const e=Hw(),n={...o,id:e,is_temporary:!0},r=Iw(Mw)||[];return Rw(Mw,[...r,n],qw),Jw({type:"CREATE",entity:"psicologos",data:o,tempId:e}),{data:n,error:null,isOffline:!0}}}catch(n){return console.error("[EnhancedService] Error general al crear psicólogo:",n),{data:null,error:{message:$w(n,"crear","psicólogo"),original:n}}}},updatePsychologist:async(e,t)=>{try{console.log(`[EnhancedService] Intentando actualizar psicólogo ID: ${e}`,t);const r=zw("psicologos",t);if(!r.isValid)return{data:null,error:{message:"Datos de psicólogo inválidos",validationErrors:r.errors,original:new Error("Validation failed")}};const a={...t,updated_at:(new Date).toISOString()};if(delete a.id,delete a.created_at,delete a.usuario_id,delete a.email,Ww(e)){console.log(`[EnhancedService] Actualizando psicólogo temporal ID: ${e}`);const t=(Iw(Mw)||[]).map((t=>t.id===e?{...t,...a}:t));Rw(Mw,t,qw);const n=(Iw(Bw)||[]).map((t=>"CREATE"===t.type&&"psicologos"===t.entity&&t.tempId===e?{...t,data:{...t.data,...a}}:t));Rw(Bw,n,Vw);return{data:t.find((t=>t.id===e)),error:null,isOffline:!0}}try{const{data:t,error:n}=await kw.from("psicologos").update(a).eq("id",e).select().single();if(n)throw console.error(`[EnhancedService] Error Supabase al actualizar psicólogo ${e}:`,n),n;if(t){console.log(`[EnhancedService] Psicólogo ${e} actualizado con éxito:`,t);const n=(Iw(Mw)||[]).map((n=>n.id===e?t:n));return Rw(Mw,n,qw),{data:t,error:null}}throw new Error("No se devolvieron datos después de la actualización.")}catch(n){console.warn(`[EnhancedService] Error al actualizar psicólogo ${e} en Supabase, guardando localmente:`,n);const t=(Iw(Mw)||[]).map((t=>t.id===e?{...t,...a,is_pending_update:!0}:t));Rw(Mw,t,qw),Jw({type:"UPDATE",entity:"psicologos",id:e,data:a});return{data:t.find((t=>t.id===e)),error:null,isOffline:!0}}}catch(r){return console.error(`[EnhancedService] Error general al actualizar psicólogo ${e}:`,r),{data:null,error:{message:$w(r,"actualizar","psicólogo"),original:r}}}},deletePsychologist:async e=>{try{console.log(`[EnhancedService] Intentando eliminar psicólogo ID: ${e}`);const n=Iw(Mw)||[],r=n.find((t=>t.id===e));if(Ww(e)){console.log(`[EnhancedService] Eliminando psicólogo temporal ID: ${e}`);const t=n.filter((t=>t.id!==e));return Rw(Mw,t,qw),Qw("CREATE","psicologos",e),{data:{success:!0},error:null}}try{const{error:t}=await kw.from("psicologos").delete().eq("id",e);if(t)throw console.error(`[EnhancedService] Error Supabase al eliminar psicólogo ${e}:`,t),t;console.log(`[EnhancedService] Psicólogo ${e} eliminado con éxito.`);const r=n.filter((t=>t.id!==e));return Rw(Mw,r,qw),{data:{success:!0},error:null}}catch(t){console.warn(`[EnhancedService] Error al eliminar psicólogo ${e} en Supabase, guardando localmente:`,t);const a=n.map((t=>t.id===e?{...t,is_pending_delete:!0}:t));return Rw(Mw,a,qw),Jw({type:"DELETE",entity:"psicologos",id:e,data:r}),{data:{success:!0},error:null,isOffline:!0}}}catch(n){return console.error(`[EnhancedService] Error general al eliminar psicólogo ${e}:`,n),{data:null,error:{message:$w(n,"eliminar","psicólogo"),original:n}}}},getPatients:Xw,createPatient:async e=>{try{const n=zw("pacientes",e);if(!n.isValid)return{data:null,error:{message:"Datos de paciente inválidos",validationErrors:n.errors,original:new Error("Validation failed")}};const{user:r,error:a}=await Kw();if(a||!r)throw a||new Error("Usuario no autenticado para crear paciente");const o={...e,creado_por:r.id,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()};try{console.log("[EnhancedService] Intentando insertar paciente:",o);const{data:e,error:t}=await kw.from("pacientes").insert([o]).select().single();if(t)throw console.error("[EnhancedService] Error Supabase al insertar paciente:",t),"23503"===t.code&&console.error("[EnhancedService] Error: La institución o psicólogo especificado no existe."),t;if(e){console.log("[EnhancedService] Paciente insertado con éxito:",e);let t=e;if(e.fecha_nacimiento){const n=new Date,r=new Date(e.fecha_nacimiento);let a=n.getFullYear()-r.getFullYear();const o=n.getMonth()-r.getMonth();(o<0||0===o&&n.getDate()<r.getDate())&&a--,t={...e,edad:a}}const n=Iw(Fw)||[];return Rw(Fw,[...n,t],qw),{data:t,error:null}}throw new Error("No se devolvieron datos después de la inserción.")}catch(t){console.warn("[EnhancedService] Error al insertar paciente en Supabase, guardando localmente:",t);const e=Hw();let n={...o,id:e,is_temporary:!0};if(o.fecha_nacimiento){const e=new Date,t=new Date(o.fecha_nacimiento);let r=e.getFullYear()-t.getFullYear();const a=e.getMonth()-t.getMonth();(a<0||0===a&&e.getDate()<t.getDate())&&r--,n.edad=r}const r=Iw(Fw)||[];return Rw(Fw,[...r,n],qw),Jw({type:"CREATE",entity:"pacientes",data:o,tempId:e}),{data:n,error:null,isOffline:!0}}}catch(n){return console.error("[EnhancedService] Error general al crear paciente:",n),{data:null,error:{message:$w(n,"crear","paciente"),original:n}}}},updatePatient:async(e,t)=>{try{console.log(`[EnhancedService] Intentando actualizar paciente ID: ${e}`,t);const r=zw("pacientes",t);if(!r.isValid)return{data:null,error:{message:"Datos de paciente inválidos",validationErrors:r.errors,original:new Error("Validation failed")}};const a={...t,updated_at:(new Date).toISOString()};if(delete a.id,delete a.created_at,delete a.creado_por,Ww(e)){if(console.log(`[EnhancedService] Actualizando paciente temporal ID: ${e}`),a.fecha_nacimiento){const e=new Date,t=new Date(a.fecha_nacimiento);let n=e.getFullYear()-t.getFullYear();const r=e.getMonth()-t.getMonth();(r<0||0===r&&e.getDate()<t.getDate())&&n--,a.edad=n}const t=(Iw(Fw)||[]).map((t=>t.id===e?{...t,...a}:t));Rw(Fw,t,qw);const n=(Iw(Bw)||[]).map((t=>"CREATE"===t.type&&"pacientes"===t.entity&&t.tempId===e?{...t,data:{...t.data,...a}}:t));Rw(Bw,n,Vw);return{data:t.find((t=>t.id===e)),error:null,isOffline:!0}}try{const{data:t,error:n}=await kw.from("pacientes").update(a).eq("id",e).select().single();if(n)throw console.error(`[EnhancedService] Error Supabase al actualizar paciente ${e}:`,n),n;if(t){console.log(`[EnhancedService] Paciente ${e} actualizado con éxito:`,t);let n=t;if(t.fecha_nacimiento){const e=new Date,r=new Date(t.fecha_nacimiento);let a=e.getFullYear()-r.getFullYear();const o=e.getMonth()-r.getMonth();(o<0||0===o&&e.getDate()<r.getDate())&&a--,n={...t,edad:a}}const r=(Iw(Fw)||[]).map((t=>t.id===e?n:t));return Rw(Fw,r,qw),{data:n,error:null}}throw new Error("No se devolvieron datos después de la actualización.")}catch(n){if(console.warn(`[EnhancedService] Error al actualizar paciente ${e} en Supabase, guardando localmente:`,n),a.fecha_nacimiento){const e=new Date,t=new Date(a.fecha_nacimiento);let n=e.getFullYear()-t.getFullYear();const r=e.getMonth()-t.getMonth();(r<0||0===r&&e.getDate()<t.getDate())&&n--,a.edad=n}const t=(Iw(Fw)||[]).map((t=>t.id===e?{...t,...a,is_pending_update:!0}:t));Rw(Fw,t,qw),Jw({type:"UPDATE",entity:"pacientes",id:e,data:a});return{data:t.find((t=>t.id===e)),error:null,isOffline:!0}}}catch(r){return console.error(`[EnhancedService] Error general al actualizar paciente ${e}:`,r),{data:null,error:{message:$w(r,"actualizar","paciente"),original:r}}}},deletePatient:async e=>{try{console.log(`[EnhancedService] Intentando eliminar paciente ID: ${e}`);const n=Iw(Fw)||[],r=n.find((t=>t.id===e));if(Ww(e)){console.log(`[EnhancedService] Eliminando paciente temporal ID: ${e}`);const t=n.filter((t=>t.id!==e));return Rw(Fw,t,qw),Qw("CREATE","pacientes",e),{data:{success:!0},error:null}}try{const{error:t}=await kw.from("pacientes").delete().eq("id",e);if(t)throw console.error(`[EnhancedService] Error Supabase al eliminar paciente ${e}:`,t),t;console.log(`[EnhancedService] Paciente ${e} eliminado con éxito.`);const r=n.filter((t=>t.id!==e));return Rw(Fw,r,qw),{data:{success:!0},error:null}}catch(t){console.warn(`[EnhancedService] Error al eliminar paciente ${e} en Supabase, guardando localmente:`,t);const a=n.map((t=>t.id===e?{...t,is_pending_delete:!0}:t));return Rw(Fw,a,qw),Jw({type:"DELETE",entity:"pacientes",id:e,data:r}),{data:{success:!0},error:null,isOffline:!0}}}catch(n){return console.error(`[EnhancedService] Error general al eliminar paciente ${e}:`,n),{data:null,error:{message:$w(n,"eliminar","paciente"),original:n}}}},syncPendingOperations:async()=>{const e=Iw(Bw)||[];if(0===e.length)return console.log("[EnhancedService] No hay operaciones pendientes para sincronizar."),{success:!0,syncedCount:0,errors:[]};console.log(`[EnhancedService] Sincronizando ${e.length} operaciones pendientes...`);const t={success:!0,syncedCount:0,errors:[]},n=[...e].sort(((e,t)=>{const n={CREATE:1,UPDATE:2,DELETE:3},r=n[e.type]-n[t.type];if(0!==r)return r;if("CREATE"===e.type||"UPDATE"===e.type){const n={instituciones:1,psicologos:2,pacientes:3};return n[e.entity]-n[t.entity]}if("DELETE"===e.type){const n={pacientes:1,psicologos:2,instituciones:3};return n[e.entity]-n[t.entity]}return 0}));for(const a of n)try{let e=!1;switch(a.type){case"CREATE":if("instituciones"===a.entity){const{error:n}=await kw.from("instituciones").insert([a.data]).select();e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"crear","institución"),original:n}})}else if("psicologos"===a.entity){const{error:n}=await kw.from("psicologos").insert([a.data]).select();e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"crear","psicólogo"),original:n}})}else if("pacientes"===a.entity){const{error:n}=await kw.from("pacientes").insert([a.data]).select();e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"crear","paciente"),original:n}})}break;case"UPDATE":if("instituciones"===a.entity){const{error:n}=await kw.from("instituciones").update(a.data).eq("id",a.id);e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"actualizar","institución"),original:n}})}else if("psicologos"===a.entity){const{error:n}=await kw.from("psicologos").update(a.data).eq("id",a.id);e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"actualizar","psicólogo"),original:n}})}else if("pacientes"===a.entity){const{error:n}=await kw.from("pacientes").update(a.data).eq("id",a.id);e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"actualizar","paciente"),original:n}})}break;case"DELETE":if("instituciones"===a.entity){const{error:n}=await kw.from("instituciones").delete().eq("id",a.id);e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"eliminar","institución"),original:n}})}else if("psicologos"===a.entity){const{error:n}=await kw.from("psicologos").delete().eq("id",a.id);e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"eliminar","psicólogo"),original:n}})}else if("pacientes"===a.entity){const{error:n}=await kw.from("pacientes").delete().eq("id",a.id);e=!n,n&&t.errors.push({operation:a,error:{message:$w(n,"eliminar","paciente"),original:n}})}}e?t.syncedCount++:t.success=!1}catch(r){console.error("[EnhancedService] Error al sincronizar operación:",a,r),t.success=!1,t.errors.push({operation:a,error:{message:$w(r),original:r}})}if(t.syncedCount>0){const n=e.filter(((n,r)=>!t.errors.some((t=>t.operation===e[r]))));Rw(Bw,n,Vw),await Promise.all([Gw(),Yw(),Xw()]),Rw("last_sync_attempt",Date.now(),Vw)}return console.log(`[EnhancedService] Sincronización completada. Éxito: ${t.success}, Sincronizadas: ${t.syncedCount}, Errores: ${t.errors.length}`),t},getSyncStatus:()=>{const e=Iw(Bw)||[],t={total:e.length,byType:{CREATE:0,UPDATE:0,DELETE:0},byEntity:{instituciones:0,psicologos:0,pacientes:0}};return e.forEach((e=>{t.byType[e.type]=(t.byType[e.type]||0)+1,t.byEntity[e.entity]=(t.byEntity[e.entity]||0)+1})),{pendingCount:e.length,lastSyncAttempt:Iw("last_sync_attempt")||null,counts:t,operations:e.map((e=>{let t="";return e.data&&("instituciones"===e.entity&&e.data.nombre?t=e.data.nombre:"psicologos"===e.entity&&e.data.nombre?t=`${e.data.nombre} ${e.data.apellidos||""}`:"pacientes"===e.entity&&e.data.nombre&&(t=e.data.nombre)),{type:e.type,entity:e.entity,id:e.id||e.data&&e.data.id||e.tempId,name:t.trim(),timestamp:e.timestamp}}))}},isTemporaryId:Ww},e_=()=>{const[e,t]=V.useState(null),[n,r]=V.useState(!1),[a,o]=V.useState(!1),i=()=>{const e=Zw.getSyncStatus();t(e)};V.useEffect((()=>{i();const e=setInterval(i,3e4);return()=>clearInterval(e)}),[]);return e&&0!==e.pendingCount?ee.jsxs("div",{className:"fixed bottom-4 right-4 z-50",children:[ee.jsxs("button",{onClick:()=>o(!a),className:"flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg",title:"Operaciones pendientes de sincronización",children:[ee.jsx("i",{className:"fas fa-sync-alt mr-2"}),ee.jsx("span",{className:"font-bold",children:e.pendingCount})]}),a&&ee.jsxs("div",{className:"absolute bottom-16 right-0 bg-white rounded-lg shadow-xl p-4 w-80",children:[ee.jsxs("div",{className:"flex justify-between items-center mb-3",children:[ee.jsx("h3",{className:"font-bold text-gray-800",children:"Operaciones pendientes"}),ee.jsx("button",{onClick:()=>o(!1),className:"text-gray-500 hover:text-gray-700",children:ee.jsx("i",{className:"fas fa-times"})})]}),ee.jsxs("div",{className:"mb-3",children:[ee.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[ee.jsx("span",{className:"text-gray-600",children:"Total:"}),ee.jsx("span",{className:"font-medium",children:e.pendingCount})]}),ee.jsx("div",{className:"text-xs text-gray-500 mb-2",children:Object.entries(e.counts.byType).map((([e,t])=>t>0&&ee.jsxs("div",{className:"flex justify-between",children:[ee.jsxs("span",{children:[e,":"]}),ee.jsx("span",{children:t})]},e)))}),ee.jsx("div",{className:"text-xs text-gray-500",children:Object.entries(e.counts.byEntity).map((([e,t])=>t>0&&ee.jsxs("div",{className:"flex justify-between",children:[ee.jsxs("span",{children:[e,":"]}),ee.jsx("span",{children:t})]},e)))})]}),ee.jsx("div",{className:"max-h-40 overflow-y-auto mb-3",children:ee.jsxs("ul",{className:"text-xs space-y-1",children:[e.operations.slice(0,5).map(((e,t)=>ee.jsxs("li",{className:"p-1 border-b border-gray-100",children:[ee.jsxs("div",{className:"flex items-center",children:[ee.jsx("span",{className:"inline-block w-2 h-2 rounded-full mr-2 "+("CREATE"===e.type?"bg-green-500":"UPDATE"===e.type?"bg-blue-500":"bg-red-500")}),ee.jsx("span",{className:"font-medium",children:e.type}),ee.jsx("span",{className:"mx-1",children:"·"}),ee.jsx("span",{children:e.entity})]}),ee.jsx("div",{className:"ml-4 text-gray-500 truncate",children:e.name||e.id})]},t))),e.operations.length>5&&ee.jsxs("li",{className:"text-center text-gray-500",children:["+",e.operations.length-5," más..."]})]})}),ee.jsx("button",{onClick:async()=>{if(!n){r(!0);try{const e=await Zw.syncPendingOperations();e.success?Dw(`Sincronización completada. ${e.syncedCount} operaciones sincronizadas.`):yh.warning(`Sincronización parcial. ${e.syncedCount} operaciones sincronizadas, ${e.errors.length} errores.`),i()}catch(e){Aw(e,"sincronizar","operaciones")}finally{r(!1)}}},disabled:n,className:"w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center justify-center",children:n?ee.jsxs(ee.Fragment,{children:[ee.jsx("i",{className:"fas fa-circle-notch fa-spin mr-2"}),"Sincronizando..."]}):ee.jsxs(ee.Fragment,{children:[ee.jsx("i",{className:"fas fa-sync-alt mr-2"}),"Sincronizar ahora"]})})]})]}):null},t_=()=>{const[e,t]=V.useState(!0),[n,r]=V.useState(!1),a=Bd((e=>e.auth.user)),o=Uf(),i=dp(),s=V.useRef(null);return V.useEffect((()=>{const e=e=>{s.current&&!s.current.contains(e.target)&&r(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[]),ee.jsxs("div",{className:"min-h-screen bg-gray-50 flex",children:[ee.jsx(Fg,{isOpen:e,toggleSidebar:()=>{t(!e)}}),ee.jsxs("div",{className:"flex-1 transition-all duration-300 ease-in-out\n                    "+(e?"ml-64":"ml-[70px]"),children:[ee.jsx("header",{className:"bg-white shadow-sm",children:ee.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:ee.jsxs("div",{className:"flex justify-between h-16 items-center",children:[ee.jsx("div",{className:"flex items-center",children:ee.jsx("h1",{className:"text-xl font-bold text-blue-600",children:"BAT-7"})}),ee.jsx("div",{className:"flex items-center relative",ref:s,children:a&&ee.jsxs(ee.Fragment,{children:[ee.jsxs("div",{className:"flex items-center space-x-3 cursor-pointer",onClick:()=>{r(!n)},children:[ee.jsxs("div",{className:"flex flex-col items-end",children:[ee.jsx("span",{className:"text-sm font-medium text-gray-800",children:a.name||"Usuario"}),ee.jsxs("div",{className:"flex items-center",children:[ee.jsxs("span",{className:"text-xs text-gray-500 mr-2",children:["Rol: ",ee.jsx("span",{className:"font-medium capitalize "+("admin"===a.role?"text-red-600":"professional"===a.role?"text-blue-600":"text-green-600"),children:"admin"===a.role?"Administrador":"professional"===a.role?"Profesional":"student"===a.role?"Estudiante":a.role||"Usuario"})]}),ee.jsxs("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800",children:[ee.jsx("span",{className:"h-1.5 w-1.5 rounded-full bg-green-500 mr-1"}),"Activo"]})]})]}),ee.jsx("div",{className:"w-10 h-10 rounded-full flex items-center justify-center "+("admin"===a.role?"bg-red-100":"professional"===a.role?"bg-blue-100":"bg-green-100"),children:ee.jsx("span",{className:"text-sm font-medium "+("admin"===a.role?"text-red-600":"professional"===a.role?"text-blue-600":"text-green-600"),children:a.name?a.name.charAt(0).toUpperCase():"U"})}),ee.jsx("i",{className:`fas fa-chevron-${n?"up":"down"} text-gray-400 ml-1`})]}),n&&ee.jsxs("div",{className:"absolute right-0 top-full mt-2 w-48 bg-white rounded-md shadow-lg z-10 py-1",children:[ee.jsxs("div",{className:"px-4 py-2 border-b border-gray-100",children:[ee.jsx("p",{className:"text-sm font-medium text-gray-900",children:a.name}),ee.jsx("p",{className:"text-xs text-gray-500",children:a.email})]}),ee.jsxs("a",{href:`/${a.role}/profile`,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[ee.jsx("i",{className:"fas fa-user mr-2"})," Mi Perfil"]}),ee.jsxs("a",{href:"/settings",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[ee.jsx("i",{className:"fas fa-cog mr-2"})," Configuración"]}),ee.jsx("div",{className:"border-t border-gray-100 my-1"}),ee.jsxs("button",{onClick:()=>{window.confirm("¿Está seguro que desea cerrar sesión?")&&(o(zg()),i("/login"))},className:"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[ee.jsx("i",{className:"fas fa-sign-out-alt mr-2"})," Cerrar Sesión"]})]})]})})]})})}),ee.jsx("main",{className:"py-10",children:ee.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[ee.jsx(Bg,{}),ee.jsx(qg,{children:ee.jsx(xp,{})})]})}),ee.jsx("footer",{className:"bg-white border-t border-gray-200 py-8",children:ee.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:ee.jsxs("p",{className:"text-center text-gray-500 text-sm",children:["© ",(new Date).getFullYear()," BAT-7 Evaluaciones. Todos los derechos reservados."]})})}),ee.jsx(e_,{})]})]})},n_=({fullScreen:e=!1,message:t="Cargando..."})=>{const n=e?"fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50":"flex flex-col items-center justify-center py-8";return ee.jsx("div",{className:n,children:ee.jsxs("div",{className:"flex flex-col items-center",children:[ee.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),t&&ee.jsx("p",{className:"text-gray-600",children:t})]})})};class r_ extends V.Component{constructor(e){super(e),t(this,"handleReset",(()=>{this.setState({hasError:!1,error:null,errorInfo:null}),window.location.href="/"})),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error capturado por boundary:",e,t),this.setState({errorInfo:t})}render(){var e;return this.state.hasError?ee.jsx("div",{className:"container mx-auto py-12 px-4",children:ee.jsxs("div",{className:"bg-white shadow-md rounded-md overflow-hidden",children:[ee.jsx("div",{className:"bg-red-600 text-white px-4 py-2",children:ee.jsx("h2",{className:"text-xl font-medium",children:"Algo salió mal"})}),ee.jsxs("div",{className:"p-4",children:[ee.jsxs("div",{className:"bg-red-50 p-4 rounded-md mb-4",children:[ee.jsx("p",{className:"text-red-700 mb-2",children:"Se ha producido un error al cargar este componente."}),ee.jsxs("details",{className:"text-sm",children:[ee.jsx("summary",{className:"cursor-pointer text-red-500 font-medium mb-2",children:"Detalles técnicos"}),ee.jsxs("div",{className:"p-3 bg-gray-800 text-gray-200 rounded overflow-auto",children:[ee.jsx("p",{className:"whitespace-pre-wrap",children:null==(e=this.state.error)?void 0:e.toString()}),this.state.errorInfo&&ee.jsx("p",{className:"whitespace-pre-wrap mt-2",children:this.state.errorInfo.componentStack})]})]})]}),ee.jsx("div",{className:"flex justify-center",children:ee.jsx("button",{onClick:this.handleReset,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Volver al inicio"})})]})]})}):this.props.children}}const a_=()=>{const e=dp(),{user:t}=Bd((e=>e.auth));return V.useEffect((()=>{if(t)switch(t.role){case"admin":e("/admin/dashboard",{replace:!0});break;case"professional":e("/professional/dashboard",{replace:!0});break;default:e("/student/dashboard",{replace:!0})}else e("/login",{replace:!0})}),[t,e]),ee.jsxs("div",{className:"flex flex-col justify-center items-center h-screen bg-gray-50",children:[ee.jsx("div",{className:"w-16 h-16 border-t-4 border-b-4 border-blue-500 rounded-full animate-spin mb-4"}),ee.jsx("h2",{className:"text-xl font-semibold text-gray-700",children:"Redirigiendo"}),ee.jsx("p",{className:"text-gray-500 mt-2",children:"Por favor espere un momento..."})]})},o_=V.lazy((()=>Bp((()=>import("./PageLoader-32912604.js")),[]))),i_=e=>t=>ee.jsx(r_,{children:ee.jsx(e,{...t})}),s_=e=>t=>ee.jsx(r_,{children:ee.jsx(V.Suspense,{fallback:ee.jsx(o_,{}),children:ee.jsx(e,{...t})})}),l_=s_(V.lazy((()=>Bp((()=>import("./Login-f6be07a8.js")),[])))),u_=s_(V.lazy((()=>Bp((()=>import("./Register-b8ff472f.js")),[])))),c_=s_(V.lazy((()=>Bp((()=>import("./Dashboard-e15c3c32.js")),[])))),d_=i_(V.lazy((()=>Bp((()=>import("./Candidates-b57ee93d.js")),["assets/Candidates-b57ee93d.js","assets/Card-54419bd4.js","assets/Button-9c521291.js","assets/useToast-cda9a5e1.js"])))),f_=i_(V.lazy((()=>Bp((()=>import("./VerbalInfo-3d484624.js")),["assets/VerbalInfo-3d484624.js","assets/Card-54419bd4.js","assets/Button-9c521291.js"])))),h_=s_(V.lazy((()=>Bp((()=>import("./Users-77d38882.js")),["assets/Users-77d38882.js","assets/Card-54419bd4.js"])))),p_=s_(V.lazy((()=>Bp((()=>import("./Institutions-6f791b10.js")),["assets/Institutions-6f791b10.js","assets/Card-54419bd4.js"])))),m_=s_(V.lazy((()=>Bp((()=>import("./Reports-1e7f5957.js")),["assets/Reports-1e7f5957.js","assets/Card-54419bd4.js"])))),g_=s_(V.lazy((()=>Bp((()=>import("./Patients-d1d66e8b.js")),["assets/Patients-d1d66e8b.js","assets/Card-54419bd4.js","assets/Button-9c521291.js","assets/Modal-035dd70d.js"])))),y_=s_(V.lazy((()=>Bp((()=>import("./Administration-cbfbdb6e.js")),["assets/Administration-cbfbdb6e.js","assets/Card-54419bd4.js","assets/Button-9c521291.js","assets/Modal-035dd70d.js"])))),v_=s_(V.lazy((()=>Bp((()=>import("./TestPage-17564ea0.js")),["assets/TestPage-17564ea0.js","assets/Card-54419bd4.js","assets/Button-9c521291.js"])))),b_=s_(V.lazy((()=>Bp((()=>import("./Students-3d966307.js")),["assets/Students-3d966307.js","assets/Card-54419bd4.js"])))),w_=s_(V.lazy((()=>Bp((()=>import("./Tests-272f6637.js")),["assets/Tests-272f6637.js","assets/Card-54419bd4.js"])))),__=s_(V.lazy((()=>Bp((()=>import("./Reports-2d5b7ff3.js")),["assets/Reports-2d5b7ff3.js","assets/Card-54419bd4.js"])))),k_=s_(V.lazy((()=>Bp((()=>import("./Patients-4ed993c7.js")),[])))),S_=s_(V.lazy((()=>Bp((()=>import("./Tests-3f31ac98.js")),[])))),E_=s_(V.lazy((()=>Bp((()=>import("./Results-89872f5c.js")),["assets/Results-89872f5c.js","assets/Card-54419bd4.js","assets/Button-9c521291.js","assets/useToast-cda9a5e1.js"])))),x_=s_(V.lazy((()=>Bp((()=>import("./Patients-c17f6f91.js")),["assets/Patients-c17f6f91.js","assets/Card-54419bd4.js"])))),j_=s_(V.lazy((()=>Bp((()=>import("./Instructions-74de233b.js")),["assets/Instructions-74de233b.js","assets/Card-54419bd4.js","assets/Button-9c521291.js","assets/useToast-cda9a5e1.js"])))),T_=s_(V.lazy((()=>Bp((()=>import("./Verbal-3c165ad9.js")),["assets/Verbal-3c165ad9.js","assets/Card-54419bd4.js","assets/Button-9c521291.js","assets/useToast-cda9a5e1.js"])))),P_=s_(V.lazy((()=>Bp((()=>import("./Espacial-b501bd13.js")),["assets/Espacial-b501bd13.js","assets/Card-54419bd4.js","assets/Button-9c521291.js"])))),C_=s_(V.lazy((()=>Bp((()=>import("./Atencion-a2c746e4.js")),["assets/Atencion-a2c746e4.js","assets/Card-54419bd4.js","assets/Button-9c521291.js"])))),O_=s_(V.lazy((()=>Bp((()=>import("./Razonamiento-d95dad9d.js")),["assets/Razonamiento-d95dad9d.js","assets/Card-54419bd4.js","assets/Button-9c521291.js"])))),N_=s_(V.lazy((()=>Bp((()=>import("./Numerico-5312d030.js")),["assets/Numerico-5312d030.js","assets/Card-54419bd4.js","assets/Button-9c521291.js"])))),R_=s_(V.lazy((()=>Bp((()=>import("./Mecanico-6750c701.js")),["assets/Mecanico-6750c701.js","assets/Card-54419bd4.js","assets/Button-9c521291.js"])))),I_=s_(V.lazy((()=>Bp((()=>import("./Ortografia-2be702bc.js")),["assets/Ortografia-2be702bc.js","assets/Card-54419bd4.js","assets/Button-9c521291.js"])))),L_=s_(V.lazy((()=>Bp((()=>import("./Results-23bd0c8e.js")),["assets/Results-23bd0c8e.js","assets/Card-54419bd4.js","assets/Button-9c521291.js","assets/useToast-cda9a5e1.js"])))),$_=()=>ee.jsx(r_,{children:ee.jsx(V.Suspense,{fallback:ee.jsx(n_,{}),children:ee.jsxs(Pp,{children:[ee.jsx(jp,{path:"/login",element:ee.jsx(l_,{})}),ee.jsx(jp,{path:"/register",element:ee.jsx(u_,{})}),ee.jsx(jp,{path:"/info/verbal",element:ee.jsx(f_,{})}),ee.jsxs(jp,{path:"/",element:ee.jsx(qp,{children:ee.jsx(t_,{})}),children:[ee.jsx(jp,{index:!0,element:ee.jsx(a_,{})}),ee.jsxs(jp,{path:"admin",children:[ee.jsx(jp,{index:!0,element:ee.jsx(Ep,{to:"/admin/dashboard",replace:!0})}),ee.jsx(jp,{path:"dashboard",element:ee.jsx(Vp,{children:ee.jsx(c_,{})})}),ee.jsx(jp,{path:"users",element:ee.jsx(Vp,{children:ee.jsx(h_,{})})}),ee.jsx(jp,{path:"institutions",element:ee.jsx(Vp,{children:ee.jsx(p_,{})})}),ee.jsx(jp,{path:"reports",element:ee.jsx(Vp,{children:ee.jsx(m_,{})})}),ee.jsx(jp,{path:"patients",element:ee.jsx(Vp,{children:ee.jsx(g_,{})})}),ee.jsx(jp,{path:"administration",element:ee.jsx(Vp,{children:ee.jsx(y_,{})})}),ee.jsx(jp,{path:"tests",element:ee.jsx(Vp,{children:ee.jsx(v_,{})})})]}),ee.jsxs(jp,{path:"professional",children:[ee.jsx(jp,{index:!0,element:ee.jsx(Ep,{to:"/professional/dashboard",replace:!0})}),ee.jsx(jp,{path:"dashboard",element:ee.jsx(Wp,{children:ee.jsx(c_,{})})}),ee.jsx(jp,{path:"students",element:ee.jsx(Wp,{children:ee.jsx(b_,{})})}),ee.jsx(jp,{path:"tests",element:ee.jsx(Wp,{children:ee.jsx(w_,{})})}),ee.jsx(jp,{path:"reports",element:ee.jsx(Wp,{children:ee.jsx(__,{})})}),ee.jsx(jp,{path:"candidates",element:ee.jsx(Wp,{children:ee.jsx(d_,{})})}),ee.jsx(jp,{path:"patients",element:ee.jsx(Wp,{children:ee.jsx(k_,{})})})]}),ee.jsxs(jp,{path:"student",children:[ee.jsx(jp,{index:!0,element:ee.jsx(Ep,{to:"/student/dashboard",replace:!0})}),ee.jsx(jp,{path:"dashboard",element:ee.jsx(Hp,{children:ee.jsx(c_,{})})}),ee.jsx(jp,{path:"tests",element:ee.jsx(Hp,{children:ee.jsx(S_,{})})}),ee.jsx(jp,{path:"results",element:ee.jsx(Hp,{children:ee.jsx(E_,{})})}),ee.jsx(jp,{path:"patients",element:ee.jsx(Hp,{children:ee.jsx(x_,{})})})]}),ee.jsxs(jp,{path:"test",children:[ee.jsx(jp,{index:!0,element:ee.jsx(Ep,{to:"/student/tests",replace:!0})}),ee.jsx(jp,{path:"instructions/:testId",element:ee.jsx(j_,{})}),ee.jsx(jp,{path:"verbal",element:ee.jsx(T_,{})}),ee.jsx(jp,{path:"espacial",element:ee.jsx(P_,{})}),ee.jsx(jp,{path:"atencion",element:ee.jsx(C_,{})}),ee.jsx(jp,{path:"razonamiento",element:ee.jsx(O_,{})}),ee.jsx(jp,{path:"numerico",element:ee.jsx(N_,{})}),ee.jsx(jp,{path:"mecanico",element:ee.jsx(R_,{})}),ee.jsx(jp,{path:"ortografia",element:ee.jsx(I_,{})}),ee.jsx(jp,{path:"results/:applicationId",element:ee.jsx(L_,{})})]})]}),ee.jsx(jp,{path:"*",element:ee.jsx(Ep,{to:"/login",replace:!0})})]})})}),A_=V.createContext(),D_=({children:e})=>{const t=dp(),[n,r]=V.useState(null),[a,o]=V.useState(!0);V.useEffect((()=>{(async()=>{try{const{data:{session:e},error:t}=await kw.auth.getSession();if(t)throw t;if(null==e?void 0:e.user){const{data:t,error:n}=await kw.from("usuarios").select("*").eq("id",e.user.id).single();if(console.log("[AuthContext] Fetched userData on initial load:",t),n)throw n;r({...e.user,...t})}}catch(e){console.error("Error al verificar autenticación:",e)}finally{o(!1)}})();const{data:{subscription:e}}=kw.auth.onAuthStateChange((async(e,t)=>{if(null==t?void 0:t.user){const{data:e}=await kw.from("usuarios").select("*").eq("id",t.user.id).single();console.log("[AuthContext] Fetched userData on auth state change:",e),r({...t.user,...e})}else r(null)}));return()=>null==e?void 0:e.unsubscribe()}),[]);const i={user:n,loading:a,login:async({email:e,password:t},n=!1)=>{try{o(!0);const{data:n,error:a}=await kw.auth.signInWithPassword({email:e,password:t});if(a)throw a;const{data:i}=await kw.from("usuarios").select("*").eq("id",n.user.id).single();return r({...n.user,...i}),{success:!0}}catch(a){return{success:!1,message:a.message||"Error al iniciar sesión"}}finally{o(!1)}},register:async({email:e,password:t,...n})=>{var r;try{o(!0);const{data:a,error:i}=await kw.auth.signUp({email:e,password:t,options:{data:{...n}}});if(i)throw i;const{error:s}=await kw.from("usuarios").insert([{id:null==(r=a.user)?void 0:r.id,tipo_usuario:"Candidato",...n}]);if(s)throw s;return{success:!0}}catch(a){return{success:!1,message:a.message||"Error al registrarse"}}finally{o(!1)}},logout:async()=>{try{o(!0);const{error:e}=await kw.auth.signOut();if(e)throw e;r(null),t("/login")}catch(e){console.error("Error al cerrar sesión:",e)}finally{o(!1)}},isAuthenticated:!!n,isAdmin:"Administrador"===(null==n?void 0:n.tipo_usuario)};return ee.jsx(A_.Provider,{value:i,children:e})},z_=V.createContext(),U_=({children:e})=>{const t={position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0},n=(e,n="info",r=5e3)=>{const a={...t,autoClose:r};switch(n){case"success":return yh.success(e,a);case"error":return yh.error(e,a);case"warning":return yh.warning(e,a);case"info":return yh.info(e,a);default:return yh(e,a)}},r={addToast:n,removeToast:e=>{e&&yh.dismiss(e)},showSuccess:(e,t)=>n(e,"success",t),showError:(e,t)=>n(e,"error",t),showWarning:(e,t)=>n(e,"warning",t),showInfo:(e,t)=>n(e,"info",t),toast:yh};return ee.jsx(z_.Provider,{value:r,children:e})},M_=()=>{const e=V.useContext(z_);if(!e)throw new Error("useToast debe ser usado dentro de un ToastProvider");return e};function F_(){return ee.jsx(Ip,{children:ee.jsxs(D_,{children:[" ",ee.jsx(U_,{children:ee.jsx($_,{})})]})})}var B_,q_;globalThis&&globalThis.__generator,globalThis&&globalThis.__spreadArray,(q_=B_||(B_={})).uninitialized="uninitialized",q_.pending="pending",q_.fulfilled="fulfilled",q_.rejected="rejected";var V_,W_,H_=wg("__rtkq/focused"),K_=wg("__rtkq/unfocused"),J_=wg("__rtkq/online"),Q_=wg("__rtkq/offline"),G_=!1;(W_=V_||(V_={})).query="query",W_.mutation="mutation",B_.uninitialized,"function"==typeof queueMicrotask&&queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis);const Y_={currentTest:null,testResults:null,answeredQuestions:{},timeRemaining:0,testStarted:!1,testCompleted:!1,loading:!1,error:null},X_=function(e){var t,n=Eg(),r=e||{},a=r.reducer,o=void 0===a?void 0:a,i=r.middleware,s=void 0===i?n():i,l=r.devTools,u=void 0===l||l,c=r.preloadedState,d=void 0===c?void 0:c,f=r.enhancers,h=void 0===f?void 0:f;if("function"==typeof o)t=o;else{if(!function(e){if("object"!=typeof e||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n}(o))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');t=eg(o)}var p=s;"function"==typeof p&&(p=p(n));var m=ng.apply(void 0,p),g=tg;u&&(g=bg(yg({trace:!1},"object"==typeof u&&u)));var y=new kg(m),v=y;return Array.isArray(h)?v=ug([m],h):"function"==typeof h&&(v=h(y)),Zm(t,d,g.apply(void 0,v))}({reducer:{auth:Mg,test:jg({name:"test",initialState:Y_,reducers:{setCurrentTest:(e,t)=>{var n;e.currentTest=t.payload,e.answeredQuestions={},e.testStarted=!1,e.testCompleted=!1,e.timeRemaining=(null==(n=t.payload)?void 0:n.duration)?60*t.payload.duration:0},startTest:e=>{e.testStarted=!0},updateTimeRemaining:(e,t)=>{e.timeRemaining=t.payload},answerQuestion:(e,t)=>{const{questionId:n,answerId:r}=t.payload;e.answeredQuestions[n]=r},completeTest:e=>{e.testCompleted=!0},setTestResults:(e,t)=>{e.testResults=t.payload},setLoading:(e,t)=>{e.loading=t.payload},setError:(e,t)=>{e.error=t.payload},resetTestState:e=>Y_}}).reducer},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}}),devTools:!1});var Z_,ek,tk,nk,rk,ak;Z_=X_.dispatch,ek?ek(Z_,{onFocus:H_,onFocusLost:K_,onOffline:Q_,onOnline:J_}):(tk=function(){return Z_(H_())},nk=function(){return Z_(J_())},rk=function(){return Z_(Q_())},ak=function(){"visible"===window.document.visibilityState?tk():Z_(K_())},G_||"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",ak,!1),window.addEventListener("focus",tk,!1),window.addEventListener("online",nk,!1),window.addEventListener("offline",rk,!1),G_=!0)),X_.getState,X_.dispatch;te.createRoot(document.getElementById("root")).render(ee.jsx(W.StrictMode,{children:ee.jsxs($f,{store:X_,children:[ee.jsx(F_,{}),ee.jsx(sh,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light"})]})}));export{Ap as L,yh as Q,W as R,Uf as a,Bd as b,hp as c,M_ as d,Zw as e,Dw as f,a as g,Aw as h,ee as j,$g as l,V as r,kw as s,dp as u};
//# sourceMappingURL=index-165d7974.js.map
