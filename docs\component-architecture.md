# Documentación de Arquitectura de Componentes

## Introducción

Esta documentación describe la nueva arquitectura de componentes implementada en el sistema de gestión psicológica. La arquitectura está diseñada para mejorar la mantenibilidad, reutilización y escalabilidad del código.

## Estructura General

La nueva arquitectura utiliza un enfoque modular basado en componentes genéricos y configuraciones específicas. La estructura de directorios es la siguiente:

```
src/
└── components/
    └── tabs/
        ├── common/
        │   ├── EntityTab.jsx       # Componente genérico base
        │   └── entityUtils.js      # Utilidades compartidas
        ├── institutions/
        │   ├── InstitutionsTab.jsx # Componente específico
        │   └── InstitutionsConfig.jsx # Configuración específica
        ├── patients/
        │   ├── PatientsTab.jsx
        │   └── PatientsConfig.jsx
        └── psychologists/
            ├── PsychologistsTab.jsx
            └── PsychologistsConfig.jsx
```

## Componentes Base

### EntityTab

`EntityTab` es el componente base que proporciona toda la funcionalidad común para la gestión de entidades:

- Carga de datos
- Ordenamiento
- Filtrado
- Búsqueda
- Operaciones CRUD (crear, leer, actualizar, eliminar)
- Gestión de modales para formularios
- Manejo de estados de carga y errores

El componente recibe a través de props toda la configuración necesaria para personalizarlo para cada tipo de entidad.

### entityUtils

Este archivo contiene funciones de utilidad compartidas entre todos los componentes:

- `calculateAge`: Calcula la edad a partir de una fecha de nacimiento
- `getGenderIcon`: Determina el icono de género con colores adecuados
- `getInstitutionIcon`: Determina el icono según el tipo de institución
- `renderChip`: Renderiza un chip para mostrar información con un fondo coloreado
- `renderGenderChip`: Renderiza un chip específico para el campo de género

## Componentes Específicos

Cada tipo de entidad tiene dos archivos principales:

### [Entidad]Tab.jsx

Este componente es un wrapper ligero que utiliza `EntityTab` con la configuración específica para la entidad. También puede gestionar estados adicionales específicos para esa entidad.

### [Entidad]Config.jsx

Este archivo contiene toda la configuración específica para la entidad:

- `getColumns`: Define las columnas para la tabla
- `getFormFields`: Define los campos del formulario
- `getFilters`: Define los filtros disponibles
- `getInitialFormValues`: Proporciona valores iniciales para el formulario
- `getFormValues`: Obtiene valores del formulario para una entidad existente
- `filter[Entidad]`: Función para filtrar entidades según criterios específicos
- `[entidad]Services`: Funciones para operaciones CRUD específicas

## Flujo de Datos

1. El componente específico (`InstitutionsTab`, `PatientsTab`, etc.) se carga y puede cargar datos adicionales necesarios.
2. Se utiliza `EntityTab` pasando toda la configuración necesaria.
3. `EntityTab` maneja la carga de datos principales, renderizado y operaciones.
4. Cuando se necesita lógica específica (como filtrado personalizado), se utilizan las funciones definidas en la configuración.

## Ventajas de la Nueva Arquitectura

- **Reducción de Código Duplicado**: La lógica común está en `EntityTab`.
- **Mantenibilidad**: Los cambios en la lógica común se hacen en un solo lugar.
- **Escalabilidad**: Añadir nuevas entidades es simple, solo se necesita crear los archivos de configuración.
- **Separación de Responsabilidades**: La lógica de UI está separada de la configuración específica.
- **Testabilidad**: Las funciones de utilidad y configuración son fáciles de probar de forma aislada.

## Ejemplos de Uso

### Añadir una Nueva Entidad

Para añadir una nueva entidad (por ejemplo, "Evaluaciones"), seguir estos pasos:

1. Crear carpeta `src/components/tabs/evaluations/`
2. Crear `EvaluationsConfig.jsx` con la configuración específica
3. Crear `EvaluationsTab.jsx` utilizando `EntityTab`
4. Importar y utilizar en la página correspondiente

### Modificar Comportamiento Común

Para modificar un comportamiento común a todas las entidades:

1. Actualizar `EntityTab.jsx` con la nueva funcionalidad
2. Todos los componentes que lo utilizan automáticamente obtendrán la mejora

## Consideraciones para el Futuro

- **Gestión de Estado Global**: Considerar Redux o Context API para gestión de estado entre componentes.
- **Caché de Datos**: Implementar estrategias de caché para mejorar rendimiento.
- **Validación de Formularios**: Integrar biblioteca de validación como Formik o Yup.
- **Internacionalización**: Preparar la estructura para soportar múltiples idiomas.
