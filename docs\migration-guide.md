# Guía de Migración a la Nueva Arquitectura

## Introducción

Esta guía detalla los pasos necesarios para migrar completamente la aplicación a la nueva arquitectura de componentes. La migración se puede realizar gradualmente, permitiendo que las versiones antiguas y nuevas coexistan durante el proceso.

## Plan de Migración

### Fase 1: Preparación (Completada)

✅ Creación de la estructura base:
- `EntityTab.jsx` - Componente genérico base
- `entityUtils.js` - Utilidades compartidas
- Componentes y configuraciones específicos para Instituciones, Pacientes y Psicólogos

### Fase 2: Integración Inicial

1. **Importación en Páginas Existentes**

   Modificar las páginas que actualmente usan los componentes antiguos para usar los nuevos:

   ```jsx
   // Antes
   import InstitutionsTab from '../old/path/InstitutionsTab';

   // Después
   import InstitutionsTab from '../components/tabs/institutions/InstitutionsTab';
   ```

2. **Pruebas de Integración**

   Verificar que los nuevos componentes funcionen correctamente dentro de la aplicación:
   - Comprobar que todas las funcionalidades existentes siguen funcionando
   - Verificar la integración con el sistema de autenticación
   - Probar la navegación entre secciones

### Fase 3: Extender la Arquitectura

1. **Migrar Componentes Adicionales**

   Aplicar el mismo patrón a otros componentes de la aplicación:
   - Tests (pruebas psicológicas)
   - Reports (informes)
   - Results (resultados)

   Para cada nuevo componente:
   1. Crear carpeta correspondiente en `src/components/tabs/`
   2. Implementar archivo de configuración (`[Entity]Config.jsx`)
   3. Implementar componente wrapper (`[Entity]Tab.jsx`)

2. **Componentes Comunes Adicionales**

   Considerar la creación de más componentes genéricos para patrones recurrentes:
   - `EntityForm.jsx` - Componente para formularios comunes
   - `EntityReport.jsx` - Componente para reportes/informes
   - `EntityVisualization.jsx` - Componente para visualizaciones

### Fase 4: Refactorización de Servicios

1. **Reorganizar Servicios**

   Aplicar un enfoque similar a los servicios de API:

   ```
   src/
   └── services/
       ├── common/
       │   └── baseApiService.js     # Funcionalidad base para API
       ├── institutions/
       │   └── institutionsService.js
       ├── patients/
       │   └── patientsService.js
       └── psychologists/
           └── psychologistsService.js
   ```

2. **Implementar Gestión de Errores Consistente**

   Crear un sistema de manejo de errores uniforme para todas las llamadas API.

### Fase 5: Optimizaciones

1. **Implementar Caché**

   Añadir sistema de caché para mejorar rendimiento:
   
   ```javascript
   // En baseApiService.js
   const fetchWithCache = (key, fetchFunction, ttl = 5 * 60 * 1000) => {
     const cached = getFromCache(key);
     if (cached && Date.now() - cached.timestamp < ttl) {
       return Promise.resolve(cached.data);
     }
     
     return fetchFunction().then(data => {
       saveToCache(key, data);
       return data;
     });
   };
   ```

2. **Implementar Lazy Loading**

   Cargar componentes solo cuando se necesiten:

   ```jsx
   // En App.jsx o router
   const InstitutionsPage = React.lazy(() => import('./pages/InstitutionsPage'));
   ```

## Pruebas

Para cada componente migrado:

1. **Pruebas Unitarias**
   - Probar funciones de utilidad
   - Probar funciones de configuración
   - Probar renderizado básico

2. **Pruebas de Integración**
   - Probar interacción con API
   - Probar interacción entre componentes
   - Probar navegación

3. **Pruebas E2E**
   - Probar flujos completos
   - Probar casos de uso reales

## Rollback Plan

En caso de encontrar problemas críticos:

1. Mantener la versión antigua de los componentes (renombrados con sufijo `Old`)
2. Implementar interruptor en configuración para cambiar entre versiones
3. Preparar scripts para restaurar la versión anterior

## Calendario de Migración

| Semana | Tarea | Responsable |
|--------|-------|-------------|
| 1 | Integración de componentes base | Equipo de Frontend |
| 2 | Pruebas de integración inicial | QA + Frontend |
| 3 | Migración de componentes adicionales | Frontend |
| 4 | Refactorización de servicios | Backend + Frontend |
| 5 | Optimizaciones y pruebas finales | Todo el equipo |
| 6 | Despliegue a producción | DevOps |

## Conclusión

Esta migración representa una mejora significativa en la arquitectura de la aplicación. Aunque requiere un esfuerzo inicial, los beneficios a largo plazo en términos de mantenibilidad y escalabilidad compensan ampliamente esta inversión.

Los desarrolladores deben revisar la documentación completa en `docs/component-architecture.md` para entender completamente el nuevo enfoque antes de comenzar la migración.
