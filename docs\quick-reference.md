# Guía de Referencia Rápida para la Nueva Arquitectura

## Componentes Principales

### EntityTab

Componente genérico para gestión de entidades.

```jsx
import EntityTab from '../common/EntityTab';

<EntityTab
  entityName="Entidad"
  entityNamePlural="entidades"
  fetchEntities={entityServices.fetchEntities}
  createEntity={entityServices.createEntity}
  updateEntity={entityServices.updateEntity}
  deleteEntity={entityServices.deleteEntity}
  columns={getColumns()}
  formFields={getFormFields()}
  filters={getFilters()}
  getInitialFormValues={getInitialFormValues}
  getFormValues={getFormValues}
  filterEntities={filterEntities}
  isAdmin={isAdmin}
/>
```

#### Props

| Prop | Tipo | Descripción |
|------|------|-------------|
| entityName | string | Nombre singular de la entidad |
| entityNamePlural | string | Nombre plural de la entidad |
| fetchEntities | function | Función para obtener entidades |
| createEntity | function | Función para crear entidad |
| updateEntity | function | Función para actualizar entidad |
| deleteEntity | function | Función para eliminar entidad |
| columns | array | Configuración de columnas para la tabla |
| formFields | array | Configuración de campos para el formulario |
| filters | array | Configuración de filtros |
| getInitialFormValues | function | Función para obtener valores iniciales del formulario |
| getFormValues | function | Función para obtener valores del formulario para una entidad existente |
| filterEntities | function | Función para filtrar entidades (opcional) |
| isAdmin | boolean | Indica si el usuario es administrador |
| onBeforeModalOpen | function | Callback antes de abrir modal (opcional) |
| onAfterModalClose | function | Callback después de cerrar modal (opcional) |

### entityUtils

Utilidades compartidas para los componentes.

```jsx
import { 
  calculateAge, 
  getGenderIcon, 
  getInstitutionIcon, 
  renderChip, 
  renderGenderChip 
} from '../common/entityUtils';
```

## Patrones Comunes

### Crear una Nueva Entidad

1. Crear carpeta para la entidad:

```
src/components/tabs/myEntity/
```

2. Crear archivo de configuración:

```jsx
// MyEntityConfig.jsx
import React from 'react';
import enhancedSupabaseService from '../../../services/enhancedSupabaseService';

// Configuración de columnas
export const getColumns = () => [...];

// Configuración de campos para el formulario
export const getFormFields = () => [...];

// Configuración de filtros
export const getFilters = () => [...];

// Valores iniciales del formulario
export const getInitialFormValues = () => ({...});

// Obtener valores del formulario para una entidad existente
export const getFormValues = (entity) => ({...});

// Función para filtrar entidades
export const filterEntities = (entities, searchTerm, filterValues) => {...};

// Funciones de servicio
export const entityServices = {
  fetchEntities: async (sortField, sortDirection) => {...},
  createEntity: async (data) => {...},
  updateEntity: async (id, data) => {...},
  deleteEntity: async (id) => {...}
};
```

3. Crear componente que use EntityTab:

```jsx
// MyEntityTab.jsx
import React from 'react';
import EntityTab from '../common/EntityTab';
import { 
  getColumns, 
  getFormFields, 
  getFilters, 
  getInitialFormValues, 
  getFormValues, 
  filterEntities,
  entityServices
} from './MyEntityConfig';

const MyEntityTab = ({ isAdmin }) => {
  return (
    <EntityTab
      entityName="Mi Entidad"
      entityNamePlural="mis entidades"
      fetchEntities={entityServices.fetchEntities}
      createEntity={entityServices.createEntity}
      updateEntity={entityServices.updateEntity}
      deleteEntity={entityServices.deleteEntity}
      columns={getColumns()}
      formFields={getFormFields()}
      filters={getFilters()}
      getInitialFormValues={getInitialFormValues}
      getFormValues={getFormValues}
      filterEntities={filterEntities}
      isAdmin={isAdmin}
    />
  );
};

export default MyEntityTab;
```

### Definir Columnas

```jsx
export const getColumns = () => [
  {
    field: 'nombre',          // Campo en el objeto de datos
    header: 'Nombre',         // Texto del encabezado
    sortable: true,           // ¿Se puede ordenar?
    highlight: true,          // ¿Destacar esta columna?
    render: (value, row) => { // Función de renderizado personalizado (opcional)
      return <div>{value}</div>;
    }
  },
  // Más columnas...
];
```

### Definir Campos de Formulario

```jsx
export const getFormFields = () => [
  // Sección
  {
    id: 'section_datos',
    type: 'section',
    label: 'Datos Generales',
    width: 'full'
  },
  // Campo de texto
  {
    id: 'nombre',
    type: 'text',
    label: 'Nombre',
    placeholder: 'Ej. Juan Pérez',
    width: 'half',
    validation: {
      required: false
    }
  },
  // Campo select
  {
    id: 'tipo',
    type: 'select',
    label: 'Tipo',
    placeholder: 'Seleccione un tipo',
    width: 'half',
    options: [
      { value: 'tipo1', label: 'Tipo 1' },
      { value: 'tipo2', label: 'Tipo 2' }
    ]
  },
  // Más campos...
];
```

### Definir Filtros

```jsx
export const getFilters = () => [
  {
    id: 'tipo',
    type: 'select',
    label: 'Tipo',
    placeholder: 'Todos los tipos',
    options: [
      { value: '', label: 'Todos los tipos' },
      { value: 'tipo1', label: 'Tipo 1' },
      { value: 'tipo2', label: 'Tipo 2' }
    ]
  },
  // Más filtros...
];
```

## Estado y Dependencias

Si necesitas manejar estados adicionales o dependencias, puedes hacerlo en el componente específico:

```jsx
const MyComplexEntityTab = ({ isAdmin }) => {
  // Estados adicionales
  const [dependencies, setDependencies] = useState([]);
  
  // Efectos para cargar dependencias
  useEffect(() => {
    const loadDependencies = async () => {
      const result = await enhancedSupabaseService.getDependencies();
      setDependencies(result.data || []);
    };
    
    loadDependencies();
  }, []);
  
  return (
    <EntityTab
      // Props básicos...
      
      // Pasar dependencias a las funciones de configuración
      columns={getColumns(dependencies)}
      formFields={getFormFields(dependencies)}
      
      // Callback para actualizar estado local
      onBeforeModalOpen={(entity) => {
        // Actualizar estado adicional antes de abrir modal
      }}
    />
  );
};
```

## Servicios de API

Los servicios para cada entidad deben seguir un patrón consistente:

```javascript
export const entityServices = {
  fetchEntities: async (sortField, sortDirection) => {
    return await enhancedSupabaseService.getEntities(sortField, sortDirection);
  },
  createEntity: async (data) => {
    return await enhancedSupabaseService.createEntity(data);
  },
  updateEntity: async (id, data) => {
    return await enhancedSupabaseService.updateEntity(id, data);
  },
  deleteEntity: async (id) => {
    return await enhancedSupabaseService.deleteEntity(id);
  }
};
```

## Manejo de Errores

El componente `EntityTab` incluye manejo de errores básico. Si necesitas gestión de errores personalizada:

```jsx
const customServices = {
  ...entityServices,
  createEntity: async (data) => {
    try {
      return await entityServices.createEntity(data);
    } catch (error) {
      // Lógica personalizada de manejo de error
      if (error.code === 'DUPLICATE_KEY') {
        toast.error("Este elemento ya existe");
      }
      throw error; // Re-lanzar para que EntityTab pueda manejarlo
    }
  }
};
```

## Estilos y Personalización

Para personalizar la apariencia de los componentes, utiliza la función `render` en la configuración de columnas:

```jsx
{
  field: 'estado',
  header: 'Estado',
  sortable: true,
  render: (value) => {
    let color;
    switch (value) {
      case 'activo': color = '#e6ffec'; break;
      case 'inactivo': color = '#ffecec'; break;
      default: color = '#e6f0ff';
    }
    
    return renderChip(value, color);
  }
}
```

## Debugging

Para depurar problemas con los componentes:

1. Verifica las props pasadas a `EntityTab`
2. Inspecciona las respuestas de los servicios en la consola
3. Añade `console.log` temporales en las funciones de configuración

```jsx
export const filterEntities = (entities, searchTerm, filterValues) => {
  console.log('Filtering entities:', { entities, searchTerm, filterValues });
  // Lógica de filtrado...
};
```
