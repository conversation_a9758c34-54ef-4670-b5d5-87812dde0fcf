# Automatización de Operaciones con Supabase

Este documento explica cómo utilizar el sistema de automatización de operaciones con Supabase implementado en la aplicación.

## Introducción

El sistema de automatización permite ejecutar operaciones con Supabase de forma automática, sin necesidad de hacer clic en botones o interactuar manualmente con la interfaz de usuario. Esto es especialmente útil para:

- Ejecutar operaciones en lote
- Automatizar pruebas
- Integrar con herramientas externas
- Ejecutar operaciones programadas

## Cómo Funciona

El sistema de automatización se implementa a través de un componente React llamado `SupabaseAutomation` que se monta en la aplicación y expone una API global a través del objeto `window.supabaseAutomation`.

Las operaciones se agregan a una cola y se ejecutan secuencialmente, mostrando notificaciones de éxito o error según corresponda.

## API de Automatización

El objeto `window.supabaseAutomation` expone los siguientes métodos:

### Instituciones

```javascript
// Crear una nueva institución
window.supabaseAutomation.createInstitution({
  nombre: 'Nombre de la institución',
  direccion: 'Dirección de la institución',
  telefono: '123456789'
});

// Actualizar una institución existente
window.supabaseAutomation.updateInstitution('id-de-la-institucion', {
  nombre: 'Nuevo nombre',
  direccion: 'Nueva dirección',
  telefono: 'Nuevo teléfono'
});

// Eliminar una institución
window.supabaseAutomation.deleteInstitution('id-de-la-institucion');
```

### Psicólogos

```javascript
// Crear un nuevo psicólogo
window.supabaseAutomation.createPsychologist({
  nombre: 'Nombre del psicólogo',
  apellidos: 'Apellidos del psicólogo',
  email: '<EMAIL>',
  institucion_id: 'id-de-la-institucion'
});

// Actualizar un psicólogo existente
window.supabaseAutomation.updatePsychologist('id-del-psicologo', {
  nombre: 'Nuevo nombre',
  apellidos: 'Nuevos apellidos',
  email: '<EMAIL>'
});

// Eliminar un psicólogo
window.supabaseAutomation.deletePsychologist('id-del-psicologo');
```

### Pacientes

```javascript
// Crear un nuevo paciente
window.supabaseAutomation.createPatient({
  nombre: 'Nombre del paciente',
  apellidos: 'Apellidos del paciente',
  fecha_nacimiento: '2000-01-01',
  genero: 'Masculino',
  institucion_id: 'id-de-la-institucion',
  psicologo_id: 'id-del-psicologo'
});

// Actualizar un paciente existente
window.supabaseAutomation.updatePatient('id-del-paciente', {
  nombre: 'Nuevo nombre',
  apellidos: 'Nuevos apellidos',
  genero: 'Femenino'
});

// Eliminar un paciente
window.supabaseAutomation.deletePatient('id-del-paciente');
```

### Operaciones Personalizadas

También es posible ejecutar operaciones personalizadas:

```javascript
window.supabaseAutomation.executeCustomOperation({
  execute: async () => {
    // Código personalizado para ejecutar
    const { error } = await supabase
      .from('mi_tabla')
      .insert([{ campo1: 'valor1', campo2: 'valor2' }]);
    
    if (error) throw error;
  },
  successMessage: 'Operación completada correctamente',
  errorMessage: 'Error al ejecutar la operación'
});
```

## Ejemplo de Uso desde la Consola del Navegador

Puedes ejecutar operaciones directamente desde la consola del navegador:

```javascript
// Crear una nueva institución
window.supabaseAutomation.createInstitution({
  nombre: 'Universidad Nacional',
  direccion: 'Calle Principal 123',
  telefono: '************'
});

// Verificar que se haya creado correctamente
// (esperar un momento para que se complete la operación)
setTimeout(() => {
  const { data, error } = await supabase
    .from('instituciones')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(1);
  
  console.log('Última institución creada:', data[0]);
}, 2000);
```

## Integración con Componentes React

Los componentes React pueden utilizar la API de automatización para ejecutar operaciones sin necesidad de interactuar con la interfaz de usuario:

```jsx
import React, { useEffect } from 'react';

const MyComponent = () => {
  useEffect(() => {
    // Ejecutar una operación al montar el componente
    if (window.supabaseAutomation) {
      window.supabaseAutomation.createInstitution({
        nombre: 'Institución Automática',
        direccion: 'Dirección Automática',
        telefono: '123456789'
      });
    }
  }, []);

  return <div>Mi Componente</div>;
};
```

## Consideraciones Importantes

1. **Autenticación**: Las operaciones se ejecutan con el token de autenticación del usuario actual, por lo que es necesario estar autenticado para que funcionen correctamente.

2. **Permisos**: Las operaciones están sujetas a las políticas de Row Level Security (RLS) de Supabase, por lo que solo se ejecutarán si el usuario tiene los permisos necesarios.

3. **Errores**: Los errores se muestran como notificaciones toast y se registran en la consola del navegador.

4. **Cola de Operaciones**: Las operaciones se ejecutan secuencialmente, por lo que si hay muchas operaciones en la cola, puede haber un retraso en su ejecución.

## Solución de Problemas

Si las operaciones no se ejecutan correctamente, verifica lo siguiente:

1. **Autenticación**: Asegúrate de estar autenticado en la aplicación.

2. **Permisos**: Verifica que el usuario tenga los permisos necesarios para ejecutar la operación.

3. **Errores en la Consola**: Revisa la consola del navegador para ver si hay errores.

4. **Datos Correctos**: Asegúrate de que los datos que estás enviando son correctos y cumplen con las restricciones de la base de datos.

5. **Componente Montado**: Asegúrate de que el componente `SupabaseAutomation` esté montado en la aplicación.
