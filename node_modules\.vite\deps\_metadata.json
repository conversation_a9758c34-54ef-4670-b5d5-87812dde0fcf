{"hash": "096896fa", "browserHash": "4841073a", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5beb9c0d", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "ada5ef35", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d3a675f1", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "67299e21", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ced6ce8f", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "6d0b068f", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "17644e33", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/react-toastify.esm.mjs", "file": "react-toastify.js", "fileHash": "5cea0b50", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "17f0270f", "needsInterop": false}, "@reduxjs/toolkit/query": {"src": "../../@reduxjs/toolkit/dist/query/rtk-query.modern.mjs", "file": "@reduxjs_toolkit_query.js", "fileHash": "dc2c6830", "needsInterop": false}, "react-icons/fa": {"src": "../../react-icons/fa/index.esm.js", "file": "react-icons_fa.js", "fileHash": "d1263229", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "f6626e56", "needsInterop": false}, "react-error-boundary": {"src": "../../react-error-boundary/dist/react-error-boundary.development.js", "file": "react-error-boundary.js", "fileHash": "f9ea32a3", "needsInterop": false}}, "chunks": {"chunk-V4E6RLXT": {"file": "chunk-V4E6RLXT.js"}, "browser-7ILX4ERV": {"file": "browser-7ILX4ERV.js"}, "chunk-FMEW3YNC": {"file": "chunk-FMEW3YNC.js"}, "browser-FYKLWRQN": {"file": "browser-FYKLWRQN.js"}, "chunk-V5LT2MCF": {"file": "chunk-V5LT2MCF.js"}, "chunk-GHX6QOSA": {"file": "chunk-GHX6QOSA.js"}, "chunk-2GTGKKMZ": {"file": "chunk-2GTGKKMZ.js"}}}