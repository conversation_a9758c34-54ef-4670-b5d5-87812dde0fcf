{"name": "react-error-boundary", "version": "6.0.0", "type": "module", "description": "Simple reusable React error boundary component", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bvaughn/react-error-boundary"}, "exports": {".": {"types": "./dist/react-error-boundary.js", "development": "./dist/react-error-boundary.development.js", "default": "./dist/react-error-boundary.js"}, "./package.json": "./package.json"}, "imports": {"#is-development": {"development": "./src/env-conditions/development.ts", "default": "./src/env-conditions/production.ts"}}, "types": "dist/react-error-boundary.d.ts", "files": ["dist"], "sideEffects": false, "dependencies": {"@babel/runtime": "^7.12.5"}, "devDependencies": {"@babel/preset-env": "^7.22.5", "@babel/preset-typescript": "^7.21.5", "@preconstruct/cli": "^2.8.12", "@types/assert": "^1.5.10", "@types/react": "^18.3.17", "@types/react-dom": "^18", "assert": "^2.0.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "globals": "^15.11.0", "prettier": "^3.0.1", "react": "^18", "react-dom": "^18", "rimraf": "^6.0.1", "vitest": "^3.1.2", "typescript": "^5.8.3", "typescript-eslint": "^8.18.0"}, "peerDependencies": {"react": ">=16.13.1"}, "preconstruct": {"exports": {"importConditionDefaultExport": "default"}, "___experimentalFlags_WILL_CHANGE_IN_PATCH": {"distInRoot": true, "importsConditions": true, "typeModule": true}}, "scripts": {"clear": "pnpm clear:builds & pnpm clear:node_modules", "clear:builds": "rimraf ./dist", "clear:node_modules": "rimraf ./node_modules", "prerelease": "preconstruct build", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "vitest --environment=jsdom --watch=false", "test:watch": "vitest --environment=jsdom --watch", "typescript": "tsc --noEmit", "typescript:watch": "tsc --noEmit --watch"}}