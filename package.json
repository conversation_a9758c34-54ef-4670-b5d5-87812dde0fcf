{"name": "sistema-gestion-psicologica", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.0", "@supabase/supabase-js": "^2.33.1", "dotenv": "^17.2.0", "pg": "^8.16.3", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-icons": "^4.10.1", "react-redux": "^9.2.0", "react-router-dom": "^6.15.0", "react-toastify": "^9.1.3", "recharts": "^2.15.3", "yup": "^1.6.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.28", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}