#!/usr/bin/env node

/**
 * Script para configurar la base de datos de Supabase
 * Ejecuta el esquema y los datos de ejemplo
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://ydglduxhgwajqdseqzpy.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;

if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ Error: SUPABASE_SERVICE_KEY no está configurado');
  console.log('💡 Obtén tu service key desde: https://app.supabase.com/project/ydglduxhgwajqdseqzpy/settings/api');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

/**
 * Ejecutar SQL desde archivo
 */
async function executeSQLFile(filePath, description) {
  try {
    console.log(`📄 Ejecutando ${description}...`);
    
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // Dividir en statements individuales (separados por ;)
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    let successCount = 0;
    let errorCount = 0;

    for (const statement of statements) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql_query: statement });
        
        if (error) {
          // Intentar ejecutar directamente si RPC falla
          const { error: directError } = await supabase
            .from('_temp_sql_execution')
            .select('*')
            .limit(0);
          
          if (directError && directError.message.includes('does not exist')) {
            // Crear función temporal para ejecutar SQL
            await createTempSQLFunction();
            const { error: retryError } = await supabase.rpc('exec_sql', { sql_query: statement });
            
            if (retryError) {
              console.warn(`⚠️  Warning: ${retryError.message}`);
              errorCount++;
            } else {
              successCount++;
            }
          } else {
            console.warn(`⚠️  Warning: ${error.message}`);
            errorCount++;
          }
        } else {
          successCount++;
        }
      } catch (err) {
        console.warn(`⚠️  Warning: ${err.message}`);
        errorCount++;
      }
    }

    console.log(`✅ ${description} completado: ${successCount} éxitos, ${errorCount} advertencias`);
    return { success: true, successCount, errorCount };
    
  } catch (error) {
    console.error(`❌ Error ejecutando ${description}:`, error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Crear función temporal para ejecutar SQL
 */
async function createTempSQLFunction() {
  const createFunctionSQL = `
    CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
    RETURNS text
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      EXECUTE sql_query;
      RETURN 'OK';
    EXCEPTION
      WHEN OTHERS THEN
        RETURN SQLERRM;
    END;
    $$;
  `;

  try {
    // Intentar crear la función usando una consulta directa
    const { error } = await supabase.rpc('exec_sql', { sql_query: createFunctionSQL });
    if (error) {
      console.log('📝 Creando función auxiliar para ejecutar SQL...');
    }
  } catch (err) {
    console.log('📝 Función auxiliar ya existe o se creará automáticamente');
  }
}

/**
 * Verificar conexión a Supabase
 */
async function verifyConnection() {
  try {
    console.log('🔗 Verificando conexión a Supabase...');
    
    const { data, error } = await supabase
      .from('_temp_connection_test')
      .select('*')
      .limit(1);

    if (error && !error.message.includes('does not exist')) {
      throw error;
    }

    console.log('✅ Conexión a Supabase establecida correctamente');
    return true;
  } catch (error) {
    console.error('❌ Error conectando a Supabase:', error.message);
    return false;
  }
}

/**
 * Verificar si las tablas ya existen
 */
async function checkExistingTables() {
  try {
    const { data, error } = await supabase
      .from('usuarios')
      .select('id')
      .limit(1);

    if (!error) {
      return true; // Las tablas ya existen
    }

    return false; // Las tablas no existen
  } catch (error) {
    return false; // Las tablas no existen
  }
}

/**
 * Función principal
 */
async function main() {
  console.log('🚀 Iniciando configuración de base de datos BAT-7\n');

  // Verificar conexión
  const connected = await verifyConnection();
  if (!connected) {
    process.exit(1);
  }

  // Verificar si las tablas ya existen
  const tablesExist = await checkExistingTables();
  if (tablesExist) {
    console.log('📋 Las tablas ya existen en la base de datos');
    
    const readline = await import('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      rl.question('¿Deseas continuar y sobrescribir los datos? (y/N): ', resolve);
    });
    
    rl.close();

    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('❌ Operación cancelada');
      process.exit(0);
    }
  }

  // Rutas de archivos SQL
  const schemaPath = path.join(__dirname, '../database/schema.sql');
  const seedDataPath = path.join(__dirname, '../database/seed_data.sql');

  // Verificar que los archivos existen
  if (!fs.existsSync(schemaPath)) {
    console.error('❌ Error: No se encontró el archivo schema.sql');
    process.exit(1);
  }

  if (!fs.existsSync(seedDataPath)) {
    console.error('❌ Error: No se encontró el archivo seed_data.sql');
    process.exit(1);
  }

  console.log('📋 Archivos SQL encontrados\n');

  // Ejecutar esquema
  const schemaResult = await executeSQLFile(schemaPath, 'esquema de base de datos');
  if (!schemaResult.success) {
    console.error('❌ Error crítico ejecutando esquema');
    process.exit(1);
  }

  console.log('');

  // Ejecutar datos de ejemplo
  const seedResult = await executeSQLFile(seedDataPath, 'datos de ejemplo');
  if (!seedResult.success) {
    console.warn('⚠️  Advertencia: Error ejecutando datos de ejemplo, pero el esquema se creó correctamente');
  }

  console.log('\n🎉 ¡Configuración de base de datos completada!');
  console.log('\n📊 Resumen:');
  console.log(`   • Esquema: ${schemaResult.successCount} operaciones exitosas`);
  console.log(`   • Datos: ${seedResult.successCount || 0} operaciones exitosas`);
  
  console.log('\n🔗 Próximos pasos:');
  console.log('   1. Verifica que las tablas se crearon en Supabase Dashboard');
  console.log('   2. Configura las políticas RLS si es necesario');
  console.log('   3. Ejecuta la aplicación con: npm run dev');
  
  console.log('\n📝 Usuarios de ejemplo creados:');
  console.log('   • <EMAIL> (Administrador)');
  console.log('   • <EMAIL> (Psicólogo)');
  console.log('   • <EMAIL> (Candidato)');
}

// Ejecutar script
main().catch(error => {
  console.error('❌ Error fatal:', error);
  process.exit(1);
});
