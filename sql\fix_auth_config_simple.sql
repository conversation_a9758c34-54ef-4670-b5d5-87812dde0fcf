-- Habilitar autoconfirmación de emails para desarrollo
UPDATE auth.config
SET value = 'true'
WHERE parameter = 'mailer.autoconfirm';

-- Configurar URL del sitio
UPDATE auth.config
SET value = 'http://localhost:3000'
WHERE parameter = 'site_url';

-- Permitir inicios de sesión sin verificación de email (solo para desarrollo)
UPDATE auth.config
SET value = 'true'
WHERE parameter = 'mailer.allow_unverified_email_sign_ins';

-- Configurar tiempo de expiración del token JWT (1 día)
UPDATE auth.config
SET value = '86400'
WHERE parameter = 'jwt.exp';

-- Configurar longitud mínima de contraseña
UPDATE auth.config
SET value = '6'
WHERE parameter = 'password.min_length';