@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animaciones personalizadas */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  70% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Estilo para el contenedor de modales */
#modal-root {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
}

#modal-root > div {
  pointer-events: auto;
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .animate-modalFadeIn {
    animation: modalFadeIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }
}

@layer base {
  html {
    font-family: "Inter var", system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-700 hover:bg-secondary-200 focus:ring-secondary-500;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }

  .form-input {
    @apply w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  /* Estilos para la barra lateral */
  .sidebar {
    @apply bg-[#121940] text-[#a4b1cd] h-screen flex flex-col overflow-hidden transition-all duration-300 ease-in-out;
  }

  .sidebar.collapsed .sidebar-header h1,
  .sidebar.collapsed .sidebar-section h2,
  .sidebar.collapsed .menu-list a span,
  .sidebar.collapsed .logout-btn span,
  .sidebar.collapsed .star {
    @apply hidden;
  }

  .sidebar.collapsed .menu-list a i,
  .sidebar.collapsed .logout-btn i {
    @apply mr-0;
  }

  .sidebar-header {
    @apply p-5 flex justify-between items-center border-b border-white border-opacity-10;
  }

  .sidebar-section {
    @apply py-4 border-b border-white border-opacity-5;
  }

  .sidebar-section h2 {
    @apply uppercase text-xs px-5 mb-2 tracking-wider;
  }

  .menu-list {
    @apply list-none;
  }

  .menu-list li {
    @apply flex items-center justify-between p-3 px-5 transition-colors duration-200;
  }

  .menu-list li:hover {
    @apply bg-white bg-opacity-5;
  }

  .menu-list li.active {
    @apply bg-[#222b5a];
  }

  .menu-list li.active a {
    @apply text-white;
  }

  .menu-list a {
    @apply flex items-center text-[#a4b1cd] no-underline flex-grow;
  }

  .menu-list a i {
    @apply mr-3 text-base w-5 text-center;
  }

  .star {
    @apply cursor-pointer text-[#a4b1cd];
  }

  .star.active {
    @apply text-[#ffda0a];
  }

  .sidebar-footer {
    @apply mt-auto p-5 border-t border-white border-opacity-5;
  }

  .logout-btn {
    @apply flex items-center text-[#a4b1cd] no-underline;
  }

  .logout-btn i {
    @apply mr-3;
  }
}

/* Estilos específicos para los tests BAT-7 */
.test-container {
  @apply max-w-5xl mx-auto p-4;
}

.test-header {
  @apply mb-6 p-4 bg-white rounded-lg shadow-md;
}

.test-content {
  @apply bg-white rounded-lg shadow-md p-6 mb-6;
}

.test-footer {
  @apply bg-white rounded-lg shadow-md p-4;
}

.test-timer {
  @apply text-xl font-mono bg-blue-100 px-4 py-2 rounded-lg;
}

.test-timer-warning {
  @apply animate-pulse bg-red-100 text-red-600;
}
