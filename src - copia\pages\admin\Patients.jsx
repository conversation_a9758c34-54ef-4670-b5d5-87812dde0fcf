import React, { useState, useEffect } from 'react';
import supabase from '../../api/supabaseClient';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Modal } from '../../components/ui/Modal';
import { toast } from 'react-toastify';

const Patients = () => {
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPatient, setCurrentPatient] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    birth_date: '',
    gender: '',
    notes: ''
  });
  const [errors, setErrors] = useState({});

  // Cargar pacientes al montar el componente
  useEffect(() => {
    fetchPatients();
  }, []);

  // Función para obtener pacientes de Supabase
  const fetchPatients = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('pacientes')
        .select('*')
        .order('nombre', { ascending: true });

      if (error) throw error;
      setPatients(data || []);
    } catch (error) {
      console.error('Error al cargar pacientes:', error.message);
      toast.error('Error al cargar la lista de pacientes');
    } finally {
      setLoading(false);
    }
  };

  // Abrir modal para crear/editar paciente
  const openModal = (patient = null) => {
    if (patient) {
      setCurrentPatient(patient);
      setFormData({
        name: patient.nombre,
        birth_date: patient.fecha_nacimiento,
        gender: patient.genero,
        notes: patient.notas || ''
      });
    } else {
      setCurrentPatient(null);
      setFormData({
        name: '',
        birth_date: '',
        gender: '',
        notes: ''
      });
    }
    setErrors({});
    setIsModalOpen(true);
  };

  // Cerrar modal
  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Manejar cambios en el formulario
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Limpiar error del campo cuando el usuario escribe
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Validar formulario
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'El nombre es obligatorio';
    }

    if (!formData.birth_date) {
      newErrors.birth_date = 'La fecha de nacimiento es obligatoria';
    }

    if (!formData.gender) {
      newErrors.gender = 'El género es obligatorio';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Calcular edad a partir de la fecha de nacimiento
  const calculateAge = (birthDate) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  // Enviar formulario
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setLoading(true);

      // Calcular edad
      const age = calculateAge(formData.birth_date);

      // Preparar datos para guardar
      const patientData = {
        nombre: formData.name,
        fecha_nacimiento: formData.birth_date,
        genero: formData.gender,
        notas: formData.notes,
        edad: age,
        fecha_creacion: new Date().toISOString().split('T')[0]
      };

      if (currentPatient) {
        // Actualizar paciente existente
        const { error } = await supabase
          .from('pacientes')
          .update(patientData)
          .eq('id', currentPatient.id);

        if (error) throw error;
        toast.success('Paciente actualizado correctamente');
      } else {
        // Crear nuevo paciente
        const { error } = await supabase
          .from('pacientes')
          .insert([patientData]);

        if (error) throw error;
        toast.success('Paciente creado correctamente');
      }

      // Recargar lista de pacientes
      fetchPatients();
      closeModal();
    } catch (error) {
      console.error('Error al guardar paciente:', error.message);
      toast.error(currentPatient
        ? 'Error al actualizar el paciente'
        : 'Error al crear el paciente');
    } finally {
      setLoading(false);
    }
  };

  // Eliminar paciente
  const handleDelete = async (id) => {
    if (!window.confirm('¿Está seguro que desea eliminar este paciente? Esta acción no se puede deshacer.')) {
      return;
    }

    try {
      setLoading(true);
      const { error } = await supabase
        .from('pacientes')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast.success('Paciente eliminado correctamente');
      fetchPatients();
    } catch (error) {
      console.error('Error al eliminar paciente:', error.message);
      toast.error('Error al eliminar el paciente');
    } finally {
      setLoading(false);
    }
  };

  // Formatear fecha para mostrar
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('es-ES', options);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Gestión de Pacientes</h1>
          <p className="text-gray-600">Administre la información de los pacientes para evaluaciones psicométricas</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button
            variant="primary"
            onClick={() => openModal()}
            className="flex items-center"
            disabled={loading}
          >
            <i className="fas fa-plus mr-2"></i>
            Nuevo Paciente
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <h2 className="text-lg font-medium">Lista de Pacientes</h2>
        </CardHeader>
        <CardBody>
          {loading && patients.length === 0 ? (
            <div className="text-center py-4">
              <i className="fas fa-spinner fa-spin text-primary-500 text-2xl mb-2"></i>
              <p>Cargando pacientes...</p>
            </div>
          ) : patients.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-gray-500">No hay pacientes registrados</p>
              <Button
                variant="secondary"
                className="mt-2"
                onClick={() => openModal()}
              >
                Agregar Paciente
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nombre
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Edad
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Género
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha de Nacimiento
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Notas
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {patients.map((patient) => (
                    <tr key={patient.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{patient.nombre}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{patient.edad} años</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {patient.genero}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{formatDate(patient.fecha_nacimiento)}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-500 truncate max-w-xs">{patient.notas || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => openModal(patient)}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                        <button
                          onClick={() => handleDelete(patient.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <i className="fas fa-trash-alt"></i>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Modal para crear/editar paciente */}
      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={currentPatient ? 'Editar Paciente' : 'Nuevo Paciente'}
      >
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Nombre Completo *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={`form-input ${errors.name ? 'border-red-500' : ''}`}
              placeholder="Ej. Juan Pérez Gómez"
            />
            {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
          </div>

          <div className="mb-4">
            <label htmlFor="birth_date" className="block text-sm font-medium text-gray-700 mb-1">
              Fecha de Nacimiento *
            </label>
            <input
              type="date"
              id="birth_date"
              name="birth_date"
              value={formData.birth_date}
              onChange={handleChange}
              className={`form-input ${errors.birth_date ? 'border-red-500' : ''}`}
            />
            {errors.birth_date && <p className="mt-1 text-sm text-red-500">{errors.birth_date}</p>}
          </div>

          <div className="mb-4">
            <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">
              Género *
            </label>
            <select
              id="gender"
              name="gender"
              value={formData.gender}
              onChange={handleChange}
              className={`form-input ${errors.gender ? 'border-red-500' : ''}`}
            >
              <option value="">Seleccionar género</option>
              <option value="M">Masculino</option>
              <option value="F">Femenino</option>
            </select>
            {errors.gender && <p className="mt-1 text-sm text-red-500">{errors.gender}</p>}
          </div>

          <div className="mb-4">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notas
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows="3"
              className="form-input"
              placeholder="Información adicional sobre el paciente"
            ></textarea>
          </div>

          <div className="flex justify-end space-x-2 mt-6">
            <Button
              type="button"
              variant="secondary"
              onClick={closeModal}
              disabled={loading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  {currentPatient ? 'Actualizando...' : 'Guardando...'}
                </>
              ) : (
                currentPatient ? 'Actualizar' : 'Guardar'
              )}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default Patients;
