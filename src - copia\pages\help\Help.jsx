import React from 'react';

const Help = () => {
  return (
    <div className="bg-white shadow-md rounded-lg p-6">
      <h1 className="text-2xl font-bold text-center mb-6">Ayuda</h1>
      
      <div className="space-y-6">
        <section>
          <h2 className="text-xl font-semibold mb-3">Preguntas frecuentes</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-blue-600">¿Cómo puedo realizar un test?</h3>
              <p className="mt-1 text-gray-600">
                Para realizar un test, dirígete a la sección "Cuestionario" en el menú lateral. 
                Allí encontrarás todos los tests disponibles. Haz clic en el test que deseas realizar 
                y sigue las instrucciones.
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-blue-600">¿Cómo puedo ver mis resultados?</h3>
              <p className="mt-1 text-gray-600">
                Los resultados de tus tests se encuentran en la sección "Resultados" del menú lateral. 
                Allí podrás ver un historial de todos los tests que has realizado y sus respectivos resultados.
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-blue-600">¿Puedo guardar mis favoritos?</h3>
              <p className="mt-1 text-gray-600">
                Sí, puedes marcar como favorito cualquier elemento del menú haciendo clic en el ícono de estrella 
                que aparece junto a cada opción. Los elementos marcados como favoritos aparecerán en la sección 
                "Favoritos" en la parte superior del menú lateral.
              </p>
            </div>
          </div>
        </section>
        
        <section>
          <h2 className="text-xl font-semibold mb-3">Contacto</h2>
          <p className="text-gray-600">
            Si tienes alguna pregunta o problema que no se resuelve con la información proporcionada, 
            puedes contactarnos a través de los siguientes medios:
          </p>
          
          <div className="mt-3 space-y-2">
            <div className="flex items-center">
              <i className="fas fa-envelope text-blue-600 mr-2"></i>
              <span><EMAIL></span>
            </div>
            <div className="flex items-center">
              <i className="fas fa-phone text-blue-600 mr-2"></i>
              <span>+34 900 123 456</span>
            </div>
            <div className="flex items-center">
              <i className="fas fa-clock text-blue-600 mr-2"></i>
              <span>Lunes a Viernes, 9:00 - 18:00</span>
            </div>
          </div>
        </section>
        
        <section>
          <h2 className="text-xl font-semibold mb-3">Guía rápida</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border rounded-lg p-4">
              <h3 className="font-medium text-blue-600 mb-2">Navegación</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Usa el menú lateral para navegar entre secciones</li>
                <li>Marca tus secciones favoritas para acceso rápido</li>
                <li>Colapsa el menú lateral para tener más espacio</li>
              </ul>
            </div>
            
            <div className="border rounded-lg p-4">
              <h3 className="font-medium text-blue-600 mb-2">Tests</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Lee las instrucciones antes de comenzar</li>
                <li>Administra tu tiempo según las indicaciones</li>
                <li>Revisa tus respuestas antes de finalizar</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Help;
