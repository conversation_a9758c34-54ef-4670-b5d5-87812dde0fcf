// src/scripts/createTestUsers.js
import supabase from '../api/supabaseClient';

/**
 * <PERSON>ript para crear usuarios de prueba en Supabase
 *
 * Este script crea tres usuarios de prueba con diferentes roles:
 * - Administrador
 * - Profesional
 * - Estudiante
 */
const createTestUsers = async () => {
  console.log('Iniciando creación de usuarios de prueba...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'Admin123!',
      userData: {
        name: 'Administrador Test',
        role: 'admin',
        created_at: new Date().toISOString()
      }
    },
    {
      email: '<EMAIL>',
      password: 'Prof123!',
      userData: {
        name: 'Profesional Test',
        role: 'professional',
        created_at: new Date().toISOString()
      }
    },
    {
      email: '<EMAIL>',
      password: 'Estud123!',
      userData: {
        name: 'Estudiante Test',
        role: 'student',
        created_at: new Date().toISOString()
      }
    }
  ];

  for (const user of testUsers) {
    try {
      // Verificar si el usuario ya existe
      const { data: existingUsers, error: checkError } = await supabase
        .from('users')
        .select('*')
        .eq('email', user.email)
        .limit(1);

      if (checkError) {
        console.error(`Error al verificar usuario ${user.email}:`, checkError.message);
        continue;
      }

      if (existingUsers && existingUsers.length > 0) {
        console.log(`Usuario ${user.email} ya existe, omitiendo...`);
        continue;
      }

      // Crear usuario en Auth
      const { data, error } = await supabase.auth.signUp({
        email: user.email,
        password: user.password,
        options: {
          data: user.userData
        }
      });

      if (error) {
        console.error(`Error al crear usuario ${user.email}:`, error.message);
      } else {
        console.log(`Usuario ${user.email} creado con éxito:`, data);

        // Crear entrada en la tabla de usuarios personalizada si es necesario
        const { error: profileError } = await supabase
          .from('users')
          .insert([
            {
              id: data.user.id,
              email: user.email,
              name: user.userData.name,
              role: user.userData.role,
              created_at: user.userData.created_at
            }
          ]);

        if (profileError) {
          console.error(`Error al crear perfil para ${user.email}:`, profileError.message);
        } else {
          console.log(`Perfil para ${user.email} creado con éxito`);
        }
      }
    } catch (error) {
      console.error(`Error inesperado al crear usuario ${user.email}:`, error.message);
    }
  }

  console.log('Proceso de creación de usuarios de prueba completado');
};

// Ejecutar la función
createTestUsers()
  .catch(error => {
    console.error('Error en el script:', error);
  })
  .finally(() => {
    console.log('Script finalizado');
  });

export default createTestUsers;
