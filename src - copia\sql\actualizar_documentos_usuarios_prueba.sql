-- Script para actualizar los documentos de los usuarios de prueba
-- <PERSON>ste script asigna documentos a los usuarios de prueba para que puedan iniciar sesión con documento

-- Actualizar el documento del usuario administrador
UPDATE public.usuarios
SET documento = 'ADMIN001'
WHERE id IN (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
)
AND (documento IS NULL OR documento = '');

-- Actualizar el documento del usuario profesional/psicólogo
UPDATE public.usuarios
SET documento = 'PSICO001'
WHERE id IN (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
)
AND (documento IS NULL OR documento = '');

-- Actualizar el documento del usuario estudiante
UPDATE public.usuarios
SET documento = 'ESTUD001'
WHERE id IN (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
)
AND (documento IS NULL OR documento = '');

-- Verificar los documentos actualizados
SELECT 
  u.id, 
  u.documento, 
  u.nombre, 
  u.apellido, 
  u.rol, 
  u.tipo_usuario, 
  au.email
FROM 
  public.usuarios u
  JOIN auth.users au ON u.id = au.id
WHERE 
  au.email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  );
