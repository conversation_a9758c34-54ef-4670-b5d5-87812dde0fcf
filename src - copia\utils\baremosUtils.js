export const baremos = {
  "12-13": {
    V: [
      { pc: 99, pd: [30, 32] },
      { pc: 97, pd: [29, 29] },
      { pc: 95, pd: [28, 28] },
      { pc: 90, pd: [27, 27] },
      { pc: 85, pd: [26, 26] },
      { pc: 80, pd: [25, 25] }, 
      { pc: 70, pd: [24, 24] },
      { pc: 65, pd: [23, 23] },
      { pc: 55, pd: [22, 22] },
      { pc: 50, pd: [21, 21] },
      { pc: 40, pd: [20, 20] },
      { pc: 35, pd: [19, 19] },
      { pc: 25, pd: [18, 18] },
      { pc: 20, pd: [17, 17] },
      { pc: 15, pd: [16, 16] },
      { pc: 10, pd: [15, 15] },
      { pc: 5, pd: [13, 14] },
      { pc: 4, pd: [12, 12] },
      { pc: 2, pd: [11, 11] },
      { pc: 1, pd: [0, 10] },
    ],
    E: [
      { pc: 99, pd: [27, 28] },
      { pc: 96, pd: [26, 26] },
      { pc: 95, pd: [25, 25] },
      { pc: 90, pd: [24, 24] },
      { pc: 85, pd: [23, 23] },
      { pc: 80, pd: [22, 22] },
      { pc: 75, pd: [21, 21] },
      { pc: 70, pd: [20, 20] },
      { pc: 60, pd: [19, 19] },
      { pc: 45, pd: [18, 18] }, 
      { pc: 55, pd: [18, 18] }, 
      { pc: 50, pd: [17, 17] },
      { pc: 40, pd: [16, 16] },
      { pc: 35, pd: [15, 15] },
      { pc: 25, pd: [14, 14] },
      { pc: 20, pd: [13, 13] },
      { pc: 15, pd: [12, 12] },
      { pc: 10, pd: [11, 11] },
      { pc: 5, pd: [9, 10] },
      { pc: 4, pd: [8, 8] },
      { pc: 3, pd: [7, 7] },
      { pc: 2, pd: [6, 6] },
      { pc: 1, pd: [0, 5] },
    ],
    A: [
      { pc: 99, pd: [49, 80] },
      { pc: 98, pd: [48, 48] },
      { pc: 97, pd: [46, 47] },
      { pc: 96, pd: [44, 45] },
      { pc: 95, pd: [43, 43] },
      { pc: 90, pd: [39, 42] },
      { pc: 85, pd: [36, 38] },
      { pc: 80, pd: [35, 35] },
      { pc: 75, pd: [34, 34] },
      { pc: 70, pd: [33, 33] },
      { pc: 65, pd: [31, 32] },
      { pc: 60, pd: [29, 30] },
      { pc: 55, pd: [28, 28] },
      { pc: 50, pd: [27, 27] },
      { pc: 45, pd: [26, 26] },
      { pc: 40, pd: [25, 25] },
      { pc: 35, pd: [24, 24] },
      { pc: 30, pd: [23, 23] },
      { pc: 25, pd: [22, 22] },
      { pc: 20, pd: [21, 21] },
      { pc: 15, pd: [19, 20] },
      { pc: 10, pd: [17, 18] },
      { pc: 5, pd: [15, 16] },
      { pc: 4, pd: [13, 14] },
      { pc: 2, pd: [12, 12] },
      { pc: 1, pd: [0, 11] },
    ],
    CON: [
      { pc: 99, pd: [98, 100] },
      { pc: 97, pd: [96, 97] },
      { pc: 96, pd: [95, 95] },
      { pc: 95, pd: [94, 94] },
      { pc: 90, pd: [91, 93] },
      { pc: 85, pd: [89, 90] },
      { pc: 80, pd: [88, 88] },
      { pc: 75, pd: [85, 87] },
      { pc: 70, pd: [83, 84] },
      { pc: 65, pd: [82, 82] },
      { pc: 60, pd: [80, 81] },
      { pc: 55, pd: [78, 79] },
      { pc: 50, pd: [76, 77] },
      { pc: 45, pd: [74, 75] },
      { pc: 40, pd: [72, 73] },
      { pc: 35, pd: [69, 71] },
      { pc: 30, pd: [67, 68] },
      { pc: 25, pd: [64, 66] },
      { pc: 20, pd: [61, 63] },
      { pc: 15, pd: [56, 60] },
      { pc: 10, pd: [47, 55] },
      { pc: 5, pd: [36, 46] },
      { pc: 4, pd: [33, 35] },
      { pc: 3, pd: [29, 32] },
      { pc: 2, pd: [28, 28] },
      { pc: 1, pd: [0, 27] },
    ],
    R: [
      { pc: 99, pd: [29, 32] },
      { pc: 98, pd: [28, 28] },
      { pc: 96, pd: [27, 27] },
      { pc: 95, pd: [26, 26] },
      { pc: 90, pd: [25, 25] },
      { pc: 85, pd: [24, 24] },
      { pc: 80, pd: [23, 23] },
      { pc: 70, pd: [22, 22] },
      { pc: 65, pd: [21, 21] },
      { pc: 60, pd: [20, 20] },
      { pc: 50, pd: [19, 19] },
      { pc: 45, pd: [18, 18] },
      { pc: 40, pd: [17, 17] },
      { pc: 30, pd: [16, 16] },
      { pc: 25, pd: [15, 15] },
      { pc: 20, pd: [14, 14] },
      { pc: 15, pd: [13, 13] },
      { pc: 10, pd: [11, 12] },
      { pc: 5, pd: [8, 10] },
      { pc: 3, pd: [7, 7] },
      { pc: 2, pd: [6, 6] },
      { pc: 1, pd: [0, 5] },
    ],
    N: [
      { pc: 99, pd: [28, 32] },
      { pc: 98, pd: [27, 27] },
      { pc: 97, pd: [26, 26] },
      { pc: 96, pd: [25, 25] },
      { pc: 95, pd: [24, 24] },
      { pc: 90, pd: [22, 23] },
      { pc: 85, pd: [22, 22] },
      { pc: 85, pd: [20, 21] },
      { pc: 80, pd: [19, 19] },
      { pc: 75, pd: [18, 18] },
      { pc: 70, pd: [17, 17] },
      { pc: 65, pd: [16, 16] },
      { pc: 60, pd: [15, 15] },
      { pc: 55, pd: [14, 14] },
      { pc: 50, pd: [13, 13] },
      { pc: 45, pd: [12, 12] },
      { pc: 40, pd: [11, 11] },
      { pc: 35, pd: [10, 10] },
      { pc: 25, pd: [9, 9] },
      { pc: 20, pd: [8, 8] },
      { pc: 15, pd: [7, 7] },
      { pc: 10, pd: [6, 6] },
      { pc: 5, pd: [5, 5] },
      { pc: 3, pd: [4, 4] },
      { pc: 1, pd: [0, 3] },
    ],
    M: [
      { pc: 99, pd: [25, 28] },
      { pc: 96, pd: [24, 24] },
      { pc: 95, pd: [23, 23] },
      { pc: 90, pd: [22, 22] },
      { pc: 85, pd: [21, 21] },
      { pc: 80, pd: [20, 20] },
      { pc: 70, pd: [19, 19] },
      { pc: 60, pd: [18, 18] },
      { pc: 50, pd: [17, 17] },
      { pc: 45, pd: [16, 16] },
      { pc: 35, pd: [15, 15] },
      { pc: 30, pd: [14, 14] },
      { pc: 20, pd: [13, 13] },
      { pc: 15, pd: [12, 12] },
      { pc: 10, pd: [11, 11] },
      { pc: 5, pd: [10, 10] },
      { pc: 4, pd: [9, 9] },
      { pc: 3, pd: [8, 8] },
      { pc: 1, pd: [0, 7] },
    ],
    O: [
      { pc: 99, pd: [31, 32] },
      { pc: 98, pd: [30, 30] },
      { pc: 95, pd: [29, 29] },
      { pc: 90, pd: [27, 28] },
      { pc: 85, pd: [26, 26] },
      { pc: 80, pd: [25, 25] },
      { pc: 70, pd: [24, 24] },
      { pc: 65, pd: [23, 23] },
      { pc: 60, pd: [22, 22] },
      { pc: 55, pd: [21, 21] },
      { pc: 50, pd: [20, 20] },
      { pc: 45, pd: [19, 19] },
      { pc: 40, pd: [18, 18] },
      { pc: 35, pd: [17, 17] },
      { pc: 30, pd: [16, 16] },
      { pc: 25, pd: [15, 15] },
      { pc: 20, pd: [14, 14] },
      { pc: 15, pd: [13, 13] },
      { pc: 10, pd: [11, 12] },
      { pc: 5, pd: [9, 10] },
      { pc: 4, pd: [8, 8] },
      { pc: 3, pd: [7, 7] },
      { pc: 2, pd: [6, 6] },
      { pc: 1, pd: [0, 5] },
    ],
  },
  "13-14": {
    V: [
        { pc: 99, pd: [31, 32] },
        { pc: 98, pd: [30, 30] },
        { pc: 95, pd: [29, 29] },
        { pc: 90, pd: [28, 28] },
        { pc: 85, pd: [27, 27] },
        { pc: 75, pd: [26, 26] },
        { pc: 65, pd: [25, 25] },
        { pc: 60, pd: [24, 24] },
        { pc: 50, pd: [23, 23] },
        { pc: 45, pd: [22, 22] },
        { pc: 35, pd: [21, 21] },
        { pc: 30, pd: [20, 20] },
        { pc: 25, pd: [19, 19] },
        { pc: 20, pd: [18, 18] },
        { pc: 15, pd: [16, 17] },
        { pc: 10, pd: [15, 15] },
        { pc: 5, pd: [13, 14] },
        { pc: 4, pd: [12, 12] },
        { pc: 2, pd: [11, 11] },
        { pc: 1, pd: [0, 10] },
    ],
    E: [
        { pc: 99, pd: [28, 28] },
        { pc: 97, pd: [27, 27] },
        { pc: 95, pd: [26, 26] },
        { pc: 90, pd: [25, 25] },
        { pc: 85, pd: [24, 24] },
        { pc: 80, pd: [23, 23] },
        { pc: 75, pd: [22, 22] },
        { pc: 65, pd: [21, 21] },
        { pc: 60, pd: [20, 20] },
        { pc: 50, pd: [19, 19] },
        { pc: 45, pd: [18, 18] },
        { pc: 40, pd: [17, 17] },
        { pc: 35, pd: [16, 16] },
        { pc: 25, pd: [15, 15] },
        { pc: 20, pd: [14, 14] },
        { pc: 15, pd: [13, 13] },
        { pc: 10, pd: [12, 12] },
        { pc: 5, pd: [10, 11] },
        { pc: 4, pd: [9, 9] },
        { pc: 2, pd: [8, 8] },
        { pc: 1, pd: [0, 7] },
    ],
    A: [
        { pc: 99, pd: [60, 80] },
        { pc: 98, pd: [55, 59] },
        { pc: 97, pd: [51, 54] },
        { pc: 96, pd: [49, 50] },
        { pc: 95, pd: [48, 48] },
        { pc: 90, pd: [42, 47] },
        { pc: 85, pd: [39, 41] },
        { pc: 80, pd: [37, 38] },
        { pc: 75, pd: [35, 36] },
        { pc: 70, pd: [34, 34] },
        { pc: 65, pd: [33, 33] },
        { pc: 60, pd: [31, 32] },
        { pc: 55, pd: [30, 30] },
        { pc: 50, pd: [29, 29] },
        { pc: 45, pd: [28, 28] },
        { pc: 40, pd: [26, 27] },
        { pc: 35, pd: [25, 25] },
        { pc: 30, pd: [24, 24] },
        { pc: 25, pd: [23, 23] },
        { pc: 20, pd: [22, 22] },
        { pc: 15, pd: [20, 21] },
        { pc: 10, pd: [18, 19] },
        { pc: 5, pd: [14, 17] },
        { pc: 3, pd: [13, 13] },
        { pc: 2, pd: [10, 12] },
        { pc: 1, pd: [0, 9] },
    ],
    CON: [
        { pc: 99, pd: [100, 100] },
        { pc: 98, pd: [97, 99] },
        { pc: 97, pd: [96, 96] },
        { pc: 96, pd: [95, 95] },
        { pc: 95, pd: [94, 94] },
        { pc: 90, pd: [91, 93] },
        { pc: 85, pd: [89, 90] },
        { pc: 80, pd: [87, 88] },
        { pc: 75, pd: [85, 86] },
        { pc: 70, pd: [83, 84] },
        { pc: 65, pd: [82, 82] },
        { pc: 60, pd: [80, 81] },
        { pc: 55, pd: [78, 79] },
        { pc: 50, pd: [76, 77] },
        { pc: 45, pd: [75, 75] },
        { pc: 40, pd: [72, 74] },
        { pc: 35, pd: [70, 71] },
        { pc: 30, pd: [68, 69] },
        { pc: 25, pd: [66, 67] },
        { pc: 20, pd: [61, 65] },
        { pc: 15, pd: [57, 60] },
        { pc: 10, pd: [49, 56] },
        { pc: 5, pd: [37, 48] },
        { pc: 4, pd: [35, 36] },
        { pc: 3, pd: [31, 34] },
        { pc: 2, pd: [29, 30] },
        { pc: 1, pd: [0, 28] },
    ],
    R: [
        { pc: 99, pd: [30, 32] },
        { pc: 98, pd: [29, 29] },
        { pc: 95, pd: [28, 28] },
        { pc: 90, pd: [26, 27] },
        { pc: 85, pd: [25, 25] },
        { pc: 80, pd: [24, 24] },
        { pc: 70, pd: [23, 23] },
        { pc: 65, pd: [22, 22] },
        { pc: 55, pd: [21, 21] },
        { pc: 50, pd: [20, 20] },
        { pc: 45, pd: [19, 19] },
        { pc: 40, pd: [18, 18] },
        { pc: 30, pd: [17, 17] },
        { pc: 25, pd: [16, 16] },
        { pc: 20, pd: [15, 15] },
        { pc: 15, pd: [13, 14] },
        { pc: 10, pd: [11, 12] },
        { pc: 5, pd: [9, 10] },
        { pc: 3, pd: [8, 8] },
        { pc: 2, pd: [7, 7] },
        { pc: 1, pd: [0, 6] },
    ],
    N: [
        { pc: 99, pd: [29, 32] },
        { pc: 98, pd: [28, 28] },
        { pc: 97, pd: [27, 27] },
        { pc: 96, pd: [26, 26] },
        { pc: 95, pd: [25, 25] },
        { pc: 90, pd: [24, 24] },
        { pc: 85, pd: [22, 23] },
        { pc: 80, pd: [21, 21] },
        { pc: 70, pd: [19, 20] },
        { pc: 65, pd: [17, 18] },
        { pc: 50, pd: [15, 15] },
        { pc: 45, pd: [14, 14] },
        { pc: 40, pd: [13, 13] },
        { pc: 30, pd: [12, 12] },
        { pc: 25, pd: [10, 11] },
        { pc: 20, pd: [9, 9] },
        { pc: 15, pd: [8, 8] },
        { pc: 10, pd: [7, 7] },
        { pc: 5, pd: [5, 6] },
        { pc: 2, pd: [4, 4] },
        { pc: 1, pd: [0, 3] },
    ],
    M: [
        { pc: 99, pd: [26, 28] },
        { pc: 97, pd: [25, 25] },
        { pc: 95, pd: [24, 24] },
        { pc: 90, pd: [23, 23] },
        { pc: 85, pd: [22, 22] },
        { pc: 75, pd: [21, 21] },
        { pc: 70, pd: [20, 20] },
        { pc: 60, pd: [19, 19] },
        { pc: 50, pd: [18, 18] },
        { pc: 45, pd: [17, 17] },
        { pc: 40, pd: [16, 16] },
        { pc: 30, pd: [15, 15] },
        { pc: 25, pd: [14, 14] },
        { pc: 20, pd: [13, 13] },
        { pc: 15, pd: [12, 12] },
        { pc: 10, pd: [11, 11] },
        { pc: 5, pd: [10, 10] },
        { pc: 4, pd: [9, 9] },
        { pc: 3, pd: [8, 8] },
        { pc: 1, pd: [0, 7] },
    ],
    O: [
        { pc: 99, pd: [32, 32] },
        { pc: 98, pd: [31, 31] },
        { pc: 95, pd: [30, 30] },
        { pc: 90, pd: [29, 29] },
        { pc: 85, pd: [27, 28] },
        { pc: 80, pd: [26, 26] },
        { pc: 70, pd: [25, 25] },
        { pc: 65, pd: [24, 24] },
        { pc: 60, pd: [23, 23] },
        { pc: 50, pd: [22, 22] },
        { pc: 45, pd: [21, 21] },
        { pc: 40, pd: [20, 20] },
        { pc: 35, pd: [19, 19] },
        { pc: 30, pd: [18, 18] },
        { pc: 25, pd: [17, 17] },
        { pc: 20, pd: [16, 16] },
        { pc: 15, pd: [14, 15] },
        { pc: 10, pd: [13, 13] },
        { pc: 5, pd: [10, 12] },
        { pc: 3, pd: [9, 9] },
        { pc: 2, pd: [8, 8] },
        { pc: 1, pd: [0, 7] },
    ],
  },
};

export const convertirPdAPC = (pd, aptitud, edad) => {
  // Asegurarse de que aptitud sea una cadena de texto
  if (aptitud === null || typeof aptitud === 'undefined') {
    console.warn('Aptitud no definida, no se puede convertir PD a PC.');
    return null;
  }
  if (typeof aptitud !== 'string') {
    aptitud = String(aptitud);
  }
  let grupoEdad;
  if (edad === 12) { // Edad 12 usa el baremo "12-13"
    grupoEdad = "12-13";
  } else if (edad === 13 || edad === 14) { // Edades 13 y 14 usan el baremo "13-14"
    grupoEdad = "13-14";
  } else {
    console.warn(`Edad ${edad} fuera de los baremos definidos (12-14 años).`);
    return null; // O un valor por defecto o manejo de error
  }

  const baremoEdad = baremos[grupoEdad];
  if (!baremoEdad) {
    console.warn(`No se encontró baremo para el grupo de edad: ${grupoEdad}`);
    return null;
  }

  const baremoAptitud = baremoEdad[aptitud.toUpperCase()];
  if (!baremoAptitud) {
    console.warn(`No se encontró baremo para la aptitud: ${aptitud} en el grupo de edad: ${grupoEdad}`);
    return null;
  }

  for (const rango of baremoAptitud) {
    if (pd >= rango.pd[0] && pd <= rango.pd[1]) {
      return rango.pc;
    }
  }
  // Si el PD es menor que el mínimo del baremo, se asigna el PC más bajo (1)
  // O si es mayor que el máximo, el PC más alto (99), según la tabla.
  // Esta lógica asume que los rangos cubren todos los casos o se devuelve el PC más cercano.
  // Para simplificar, si no está en un rango exacto, buscamos el más cercano o devolvemos un valor por defecto.
  // La tabla original a veces tiene huecos, por lo que esta lógica es una aproximación.
  if (pd < baremoAptitud[baremoAptitud.length - 1].pd[0]) return baremoAptitud[baremoAptitud.length - 1].pc;
  if (pd > baremoAptitud[0].pd[1]) return baremoAptitud[0].pc;
  
  console.warn(`No se encontró un rango PC para el PD: ${pd} en la aptitud: ${aptitud} y edad: ${edad}`);
  return null; // O un valor por defecto si no se encuentra el rango
};