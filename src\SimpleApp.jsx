import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { SimpleAuthProvider } from './context/SimpleAuthContext';
import SimpleHome from './pages/SimpleHome';
import TestApp from './TestApp';

// Componente de login simple
const SimpleLogin = () => {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 className="text-2xl font-bold mb-6 text-center">Login Simple</h1>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Contraseña
            </label>
            <input
              type="password"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="password"
            />
          </div>
          <button className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700">
            Iniciar Sesión
          </button>
        </div>
        
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="text-sm text-gray-600 space-y-2">
            <div><strong>Rutas disponibles:</strong></div>
            <div>• <a href="/" className="text-blue-600 hover:underline">/</a> - Inicio</div>
            <div>• <a href="/simple" className="text-blue-600 hover:underline">/simple</a> - Página simple</div>
            <div>• <a href="/test" className="text-blue-600 hover:underline">/test</a> - Test React</div>
            <div>• <a href="/login" className="text-blue-600 hover:underline">/login</a> - Esta página</div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Aplicación simplificada para testing sin dependencias complejas
 */
function SimpleApp() {
  console.log('SimpleApp: Renderizando aplicación simplificada');

  return (
    <BrowserRouter>
      <SimpleAuthProvider>
        <Routes>
          <Route path="/" element={<SimpleHome />} />
          <Route path="/simple" element={<SimpleHome />} />
          <Route path="/test" element={<TestApp />} />
          <Route path="/login" element={<SimpleLogin />} />
          <Route path="*" element={
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
              <div className="text-center">
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                  Página No Encontrada
                </h1>
                <p className="text-gray-600 mb-6">
                  La ruta que buscas no existe.
                </p>
                <div className="space-x-4">
                  <a href="/" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Ir al Inicio
                  </a>
                  <a href="/simple" className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                    Página Simple
                  </a>
                </div>
              </div>
            </div>
          } />
        </Routes>
      </SimpleAuthProvider>
    </BrowserRouter>
  );
}

export default SimpleApp;
