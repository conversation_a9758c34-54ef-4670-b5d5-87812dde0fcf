import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { FaUser, <PERSON>a<PERSON>ock, <PERSON>aEye, FaEyeSlash, FaSpinner, FaUserMd, FaUserGraduate, FaUserShield, FaExclamationTriangle } from 'react-icons/fa';
import { useAuth } from '../../context/AuthContext';
import { toast } from 'react-toastify';

/**
 * Componente de formulario de login que soporta:
 * - Login con email o documento
 * - Diferentes tipos de usuarios (pacientes, psicólogos, administradores)
 */
const LoginForm = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  
  const [formData, setFormData] = useState({
    identifier: '', // Email o documento
    password: '',
    userType: 'candidato' // Tipo de usuario seleccionado
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Tipos de usuario disponibles
  const userTypes = [
    {
      value: 'candidato',
      label: 'Candidato',
      icon: FaUserGraduate,
      description: 'Acceso para realizar evaluaciones psicométricas',
      color: 'text-blue-600'
    },
    {
      value: 'psicólogo',
      label: 'Psicólogo',
      icon: FaUserMd,
      description: 'Acceso para gestionar candidatos y resultados',
      color: 'text-green-600'
    },
    {
      value: 'administrador',
      label: 'Administrador',
      icon: FaUserShield,
      description: 'Acceso completo al sistema',
      color: 'text-purple-600'
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Limpiar errores cuando el usuario empiece a escribir
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.identifier.trim()) {
      newErrors.identifier = 'Email o documento es requerido';
    }
    
    if (!formData.password) {
      newErrors.password = 'Contraseña es requerida';
    } else if (formData.password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const result = await login({
        identifier: formData.identifier,
        password: formData.password
      });

      if (result.success) {
        // Verificar que el tipo de usuario coincida con el seleccionado (esquema robusto)
        const userRole = result.user?.tipo_usuario?.toLowerCase();

        if (userRole !== formData.userType.toLowerCase()) {
          toast.warning(`Tu cuenta es de tipo "${userRole}", pero seleccionaste "${formData.userType}"`);
        }

        // Redirigir según el tipo de usuario
        switch (userRole) {
          case 'administrador':
            navigate('/');
            break;
          case 'psicólogo':
            navigate('/');
            break;
          case 'candidato':
          default:
            navigate('/');
            break;
        }

        toast.success('¡Bienvenido al sistema BAT-7!');
      } else {
        // Mostrar mensaje de error más amigable
        const friendlyMessage = getFriendlyErrorMessage(result.message);
        toast.error(friendlyMessage);
      }
    } catch (error) {
      console.error('Error en login:', error);
      const friendlyMessage = getFriendlyErrorMessage(error.message);
      toast.error(friendlyMessage);
    } finally {
      setLoading(false);
    }
  };

  // Función para convertir errores técnicos en mensajes amigables
  const getFriendlyErrorMessage = (errorMessage) => {
    if (!errorMessage) return 'Error al iniciar sesión';

    const lowerMessage = errorMessage.toLowerCase();

    if (lowerMessage.includes('invalid login credentials') ||
        lowerMessage.includes('invalid email or password')) {
      return 'El email/documento o la contraseña son incorrectos';
    }

    if (lowerMessage.includes('email not confirmed')) {
      return 'Debes confirmar tu email antes de iniciar sesión';
    }

    if (lowerMessage.includes('usuario no encontrado')) {
      return 'No se encontró un usuario con ese documento';
    }

    if (lowerMessage.includes('too many requests')) {
      return 'Demasiados intentos. Espera unos minutos antes de intentar de nuevo';
    }

    return errorMessage;
  };

  const isEmailFormat = (identifier) => {
    return identifier.includes('@');
  };

  return (
    <div className="min-h-screen flex">
      {/* Panel izquierdo - Información del sistema */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 relative overflow-hidden">
        {/* Patrón de fondo */}
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>

        <div className="relative z-10 flex flex-col justify-center px-12 py-12 text-white">
          <div className="mb-8">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                <FaUserShield className="text-2xl text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">BAT-7</h1>
                <p className="text-blue-100">Sistema de Evaluación</p>
              </div>
            </div>

            <h2 className="text-4xl font-bold mb-4 leading-tight">
              Bienvenido al Sistema de Evaluación Psicométrica
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Plataforma profesional para la administración y análisis de evaluaciones psicológicas
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center">
              <FaUserGraduate className="text-blue-200 mr-3" />
              <span className="text-blue-100">Evaluaciones para candidatos</span>
            </div>
            <div className="flex items-center">
              <FaUserMd className="text-blue-200 mr-3" />
              <span className="text-blue-100">Gestión para psicólogos</span>
            </div>
            <div className="flex items-center">
              <FaUserShield className="text-blue-200 mr-3" />
              <span className="text-blue-100">Administración completa</span>
            </div>
          </div>
        </div>
      </div>

      {/* Panel derecho - Formulario de login */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          {/* Header del formulario */}
          <div className="text-center lg:text-left">
            <div className="flex items-center justify-center lg:justify-start mb-6 lg:hidden">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <FaUserShield className="text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">BAT-7</h1>
                <p className="text-sm text-gray-600">Sistema de Evaluación</p>
              </div>
            </div>

            <h2 className="text-3xl font-bold text-gray-900">
              Iniciar Sesión
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Accede a tu cuenta para continuar
            </p>
          </div>

          {/* Selector de tipo de usuario */}
          <div className="mt-8">
            <h3 className="text-sm font-medium text-gray-700 mb-4">Tipo de Usuario</h3>
            <div className="grid grid-cols-1 gap-3">
              {userTypes.map((type) => {
                const IconComponent = type.icon;
                return (
                  <label
                    key={type.value}
                    className={`relative flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      formData.userType === type.value
                        ? 'border-amber-500 bg-amber-50 shadow-md'
                        : 'border-gray-200 hover:border-amber-300 hover:bg-gray-50'
                    }`}
                  >
                    <input
                      type="radio"
                      name="userType"
                      value={type.value}
                      checked={formData.userType === type.value}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center mr-4 ${
                      formData.userType === type.value ? 'bg-amber-100' : 'bg-gray-100'
                    }`}>
                      <IconComponent className={`h-5 w-5 ${
                        formData.userType === type.value ? 'text-amber-600' : 'text-gray-600'
                      }`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className={`font-medium ${
                        formData.userType === type.value ? 'text-amber-900' : 'text-gray-900'
                      }`}>
                        {type.label}
                      </div>
                      <div className={`text-sm ${
                        formData.userType === type.value ? 'text-amber-700' : 'text-gray-500'
                      }`}>
                        {type.description}
                      </div>
                    </div>
                    {formData.userType === type.value && (
                      <div className="flex-shrink-0">
                        <div className="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      </div>
                    )}
                  </label>
                );
              })}
            </div>
          </div>

          {/* Formulario de login */}
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Campo de email/documento */}
              <div>
                <label htmlFor="identifier" className="block text-sm font-medium text-gray-700 mb-2">
                  Email o Documento
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <FaUser className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="identifier"
                    name="identifier"
                    type="text"
                    autoComplete="username"
                    required
                    value={formData.identifier}
                    onChange={handleInputChange}
                    className={`block w-full pl-12 pr-4 py-3 border-2 rounded-xl text-gray-900 placeholder-gray-500 transition-all duration-200 ${
                      errors.identifier
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-200 focus:border-amber-500 focus:ring-amber-500'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20`}
                    placeholder={isEmailFormat(formData.identifier) ? "<EMAIL>" : "Número de documento"}
                  />
                </div>
                {errors.identifier && (
                  <p className="mt-2 text-sm text-red-600 flex items-center">
                    <FaExclamationTriangle className="h-4 w-4 mr-1" />
                    {errors.identifier}
                  </p>
                )}
              </div>

              {/* Campo de contraseña */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Contraseña
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <FaLock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`block w-full pl-12 pr-12 py-3 border-2 rounded-xl text-gray-900 placeholder-gray-500 transition-all duration-200 ${
                      errors.password
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-200 focus:border-amber-500 focus:ring-amber-500'
                    } focus:outline-none focus:ring-2 focus:ring-opacity-20`}
                    placeholder="Ingresa tu contraseña"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <FaEyeSlash className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" />
                    ) : (
                      <FaEye className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-2 text-sm text-red-600 flex items-center">
                    <FaExclamationTriangle className="h-4 w-4 mr-1" />
                    {errors.password}
                  </p>
                )}
              </div>
            </div>

            {/* Opciones adicionales */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Recordarme
                </label>
              </div>
              <div className="text-sm">
                <a href="#" className="font-medium text-amber-600 hover:text-amber-500 transition-colors">
                  ¿Olvidaste tu contraseña?
                </a>
              </div>
            </div>

            {/* Botón de submit */}
            <div>
              <button
                type="submit"
                disabled={loading}
                className={`group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white transition-all duration-200 ${
                  loading
                    ? 'bg-amber-400 cursor-not-allowed'
                    : 'bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transform hover:scale-[1.02] active:scale-[0.98]'
                }`}
              >
                {loading ? (
                  <>
                    <FaSpinner className="animate-spin h-5 w-5 mr-2" />
                    Iniciando sesión...
                  </>
                ) : (
                  <>
                    <FaUser className="h-5 w-5 mr-2" />
                    Iniciar Sesión
                  </>
                )}
              </button>
            </div>

            {/* Enlaces adicionales */}
            <div className="text-center pt-4">
              <p className="text-sm text-gray-600">
                ¿No tienes cuenta?{' '}
                <Link
                  to="/register"
                  className="font-medium text-amber-600 hover:text-amber-500 transition-colors"
                >
                  Regístrate aquí
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
