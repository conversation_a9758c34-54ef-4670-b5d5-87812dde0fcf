import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { FaUser, <PERSON>aLock, <PERSON>aEye, FaEyeSlash, FaSpinner, FaUserMd, FaUserGraduate, FaUserShield } from 'react-icons/fa';
import { useAuth } from '../../context/AuthContext';
import { toast } from 'react-toastify';

/**
 * Componente de formulario de login que soporta:
 * - Login con email o documento
 * - Diferentes tipos de usuarios (pacientes, psicólogos, administradores)
 */
const LoginForm = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  
  const [formData, setFormData] = useState({
    identifier: '', // Email o documento
    password: '',
    userType: 'estudiante' // Tipo de usuario seleccionado
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Tipos de usuario disponibles
  const userTypes = [
    {
      value: 'estudiante',
      label: 'Paciente/Estudiante',
      icon: FaUserGraduate,
      description: 'Acceso para realizar evaluaciones psicométricas',
      color: 'text-blue-600'
    },
    {
      value: 'psicologo',
      label: 'Psicólogo',
      icon: FaUserMd,
      description: 'Acceso para gestionar pacientes y resultados',
      color: 'text-green-600'
    },
    {
      value: 'administrador',
      label: 'Administrador',
      icon: FaUserShield,
      description: 'Acceso completo al sistema',
      color: 'text-purple-600'
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Limpiar errores cuando el usuario empiece a escribir
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.identifier.trim()) {
      newErrors.identifier = 'Email o documento es requerido';
    }
    
    if (!formData.password) {
      newErrors.password = 'Contraseña es requerida';
    } else if (formData.password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      const result = await login({
        identifier: formData.identifier,
        password: formData.password
      });
      
      if (result.success) {
        // Verificar que el tipo de usuario coincida con el seleccionado
        const userRole = result.user?.rol?.toLowerCase();
        
        if (userRole !== formData.userType) {
          toast.warning(`Tu cuenta es de tipo "${userRole}", pero seleccionaste "${formData.userType}"`);
        }
        
        // Redirigir según el tipo de usuario
        switch (userRole) {
          case 'administrador':
            navigate('/admin/dashboard');
            break;
          case 'psicologo':
            navigate('/psychologist/dashboard');
            break;
          case 'estudiante':
          default:
            navigate('/');
            break;
        }
      }
    } catch (error) {
      console.error('Error en login:', error);
      toast.error('Error al iniciar sesión');
    } finally {
      setLoading(false);
    }
  };

  const isEmailFormat = (identifier) => {
    return identifier.includes('@');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Iniciar Sesión
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Sistema de Evaluación Psicométrica BAT-7
          </p>
        </div>

        {/* Selector de tipo de usuario */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Tipo de Usuario</h3>
          <div className="space-y-3">
            {userTypes.map((type) => {
              const IconComponent = type.icon;
              return (
                <label
                  key={type.value}
                  className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.userType === type.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="userType"
                    value={type.value}
                    checked={formData.userType === type.value}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <IconComponent className={`h-5 w-5 ${type.color} mr-3`} />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{type.label}</div>
                    <div className="text-sm text-gray-500">{type.description}</div>
                  </div>
                </label>
              );
            })}
          </div>
        </div>

        {/* Formulario de login */}
        <form className="mt-8 space-y-6 bg-white rounded-lg shadow-md p-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Campo de email/documento */}
            <div>
              <label htmlFor="identifier" className="block text-sm font-medium text-gray-700">
                Email o Documento
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaUser className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="identifier"
                  name="identifier"
                  type="text"
                  autoComplete="username"
                  required
                  value={formData.identifier}
                  onChange={handleInputChange}
                  className={`appearance-none relative block w-full pl-10 pr-3 py-2 border ${
                    errors.identifier ? 'border-red-300' : 'border-gray-300'
                  } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                  placeholder={isEmailFormat(formData.identifier) ? "<EMAIL>" : "Número de documento"}
                />
              </div>
              {errors.identifier && (
                <p className="mt-1 text-sm text-red-600">{errors.identifier}</p>
              )}
            </div>

            {/* Campo de contraseña */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Contraseña
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`appearance-none relative block w-full pl-10 pr-10 py-2 border ${
                    errors.password ? 'border-red-300' : 'border-gray-300'
                  } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                  placeholder="Contraseña"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <FaEyeSlash className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <FaEye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password}</p>
              )}
            </div>
          </div>

          {/* Botón de submit */}
          <div>
            <button
              type="submit"
              disabled={loading}
              className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white ${
                loading
                  ? 'bg-blue-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
              } transition-colors duration-200`}
            >
              {loading ? (
                <>
                  <FaSpinner className="animate-spin h-5 w-5 mr-2" />
                  Iniciando sesión...
                </>
              ) : (
                'Iniciar Sesión'
              )}
            </button>
          </div>

          {/* Enlaces adicionales */}
          <div className="flex items-center justify-between">
            <Link
              to="/forgot-password"
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              ¿Olvidaste tu contraseña?
            </Link>
            <Link
              to="/register"
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Crear cuenta
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;
