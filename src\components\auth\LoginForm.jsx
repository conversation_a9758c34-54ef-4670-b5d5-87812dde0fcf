import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { FaUser, <PERSON>a<PERSON>ock, <PERSON>aEye, FaEyeSlash, FaSpinner, FaUserMd, FaUserGraduate, FaUserShield, FaExclamationTriangle } from 'react-icons/fa';
import { useAuth } from '../../context/AuthContext';
import { toast } from 'react-toastify';
import styles from './LoginForm.module.css';

/**
 * Componente de formulario de login que soporta:
 * - Login con email o documento
 * - Diferentes tipos de usuarios (pacientes, psicólogos, administradores)
 */
const LoginForm = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  
  const [formData, setFormData] = useState({
    identifier: '', // Email o documento
    password: '',
    userType: 'candidato' // Tipo de usuario seleccionado
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Tipos de usuario disponibles
  const userTypes = [
    {
      value: 'candidato',
      label: 'Candidato',
      icon: FaUserGraduate,
      description: 'Acceso para realizar evaluaciones psicométricas',
      color: 'text-blue-600'
    },
    {
      value: 'psicólogo',
      label: 'Psicólogo',
      icon: FaUserMd,
      description: 'Acceso para gestionar candidatos y resultados',
      color: 'text-green-600'
    },
    {
      value: 'administrador',
      label: 'Administrador',
      icon: FaUserShield,
      description: 'Acceso completo al sistema',
      color: 'text-purple-600'
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Limpiar errores cuando el usuario empiece a escribir
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.identifier.trim()) {
      newErrors.identifier = 'Email o documento es requerido';
    }
    
    if (!formData.password) {
      newErrors.password = 'Contraseña es requerida';
    } else if (formData.password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const result = await login({
        identifier: formData.identifier,
        password: formData.password
      });

      if (result.success) {
        // Verificar que el tipo de usuario coincida con el seleccionado (esquema robusto)
        const userRole = result.user?.tipo_usuario?.toLowerCase();

        if (userRole !== formData.userType.toLowerCase()) {
          toast.warning(`Tu cuenta es de tipo "${userRole}", pero seleccionaste "${formData.userType}"`);
        }

        // Redirigir según el tipo de usuario
        switch (userRole) {
          case 'administrador':
            navigate('/');
            break;
          case 'psicólogo':
            navigate('/');
            break;
          case 'candidato':
          default:
            navigate('/');
            break;
        }

        toast.success('¡Bienvenido al sistema BAT-7!');
      } else {
        // Mostrar mensaje de error más amigable
        const friendlyMessage = getFriendlyErrorMessage(result.message);
        toast.error(friendlyMessage);
      }
    } catch (error) {
      console.error('Error en login:', error);
      const friendlyMessage = getFriendlyErrorMessage(error.message);
      toast.error(friendlyMessage);
    } finally {
      setLoading(false);
    }
  };

  // Función para convertir errores técnicos en mensajes amigables
  const getFriendlyErrorMessage = (errorMessage) => {
    if (!errorMessage) return 'Error al iniciar sesión';

    const lowerMessage = errorMessage.toLowerCase();

    if (lowerMessage.includes('invalid login credentials') ||
        lowerMessage.includes('invalid email or password')) {
      return 'El email/documento o la contraseña son incorrectos';
    }

    if (lowerMessage.includes('email not confirmed')) {
      return 'Debes confirmar tu email antes de iniciar sesión';
    }

    if (lowerMessage.includes('usuario no encontrado')) {
      return 'No se encontró un usuario con ese documento';
    }

    if (lowerMessage.includes('too many requests')) {
      return 'Demasiados intentos. Espera unos minutos antes de intentar de nuevo';
    }

    return errorMessage;
  };

  const isEmailFormat = (identifier) => {
    return identifier.includes('@');
  };

  return (
    <div className="min-h-screen flex bg-gray-50">
      {/* Panel izquierdo - Información del sistema */}
      <div className={`hidden lg:flex lg:w-1/2 ${styles.backgroundGradient} relative overflow-hidden`}>
        {/* Patrón de fondo decorativo */}
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        <div
          className={`absolute inset-0 opacity-10 ${styles.backgroundPattern}`}
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}
        ></div>

        {/* Elementos decorativos adicionales */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-white bg-opacity-10 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-white bg-opacity-5 rounded-full blur-2xl"></div>

        <div className={`relative z-10 flex flex-col justify-center px-12 py-12 text-white ${styles.slideInLeft}`}>
          <div className="mb-12">
            {/* Logo y título principal */}
            <div className="flex items-center mb-8">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4 backdrop-blur-sm">
                <FaUserShield className="text-3xl text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold tracking-wide">BAT-7</h1>
                <p className="text-blue-100 text-lg">Sistema de Evaluación</p>
              </div>
            </div>

            {/* Título principal */}
            <h2 className="text-4xl font-bold mb-6 leading-tight">
              Bienvenido al Sistema de
              <br />
              <span className="text-blue-200">Evaluación Psicométrica</span>
            </h2>
            <p className="text-xl text-blue-100 mb-8 leading-relaxed">
              Plataforma profesional para la administración y análisis de evaluaciones psicológicas
            </p>
          </div>

          {/* Características del sistema */}
          <div className="space-y-6">
            <div className="flex items-center group">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4 group-hover:bg-opacity-30 transition-all duration-300">
                <FaUserGraduate className="text-blue-200" />
              </div>
              <span className="text-blue-100 text-lg">Evaluaciones para candidatos</span>
            </div>
            <div className="flex items-center group">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4 group-hover:bg-opacity-30 transition-all duration-300">
                <FaUserMd className="text-blue-200" />
              </div>
              <span className="text-blue-100 text-lg">Gestión para psicólogos</span>
            </div>
            <div className="flex items-center group">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4 group-hover:bg-opacity-30 transition-all duration-300">
                <FaUserShield className="text-blue-200" />
              </div>
              <span className="text-blue-100 text-lg">Administración completa</span>
            </div>
          </div>
        </div>
      </div>

      {/* Panel derecho - Formulario de login */}
      <div className="flex-1 flex flex-col justify-center py-8 px-4 sm:px-6 lg:px-12 xl:px-20 bg-white">
        <div className={`mx-auto w-full max-w-md lg:w-full lg:max-w-lg ${styles.fadeInUp}`}>
          {/* Header del formulario - Solo visible en móvil */}
          <div className="text-center lg:hidden mb-8">
            <div className="flex items-center justify-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                <FaUserShield className="text-white text-xl" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">BAT-7</h1>
                <p className="text-sm text-gray-600">Sistema de Evaluación</p>
              </div>
            </div>
          </div>

          {/* Título principal del formulario */}
          <div className="text-center lg:text-left mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Iniciar Sesión
            </h2>
            <p className="text-gray-600">
              Accede a tu cuenta para continuar
            </p>
          </div>

          {/* Selector de tipo de usuario */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-gray-700 mb-4 uppercase tracking-wide">Tipo de Usuario</h3>
            <div className="grid grid-cols-1 gap-3">
              {userTypes.map((type) => {
                const IconComponent = type.icon;
                const isSelected = formData.userType === type.value;
                return (
                  <label
                    key={type.value}
                    className={`relative flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-[1.02] ${
                      isSelected
                        ? 'border-amber-500 bg-gradient-to-r from-amber-50 to-orange-50 shadow-lg shadow-amber-500/20'
                        : 'border-gray-200 hover:border-amber-300 hover:bg-gray-50 hover:shadow-md'
                    }`}
                  >
                    <input
                      type="radio"
                      name="userType"
                      value={type.value}
                      checked={isSelected}
                      onChange={handleInputChange}
                      className="sr-only"
                    />

                    {/* Icono */}
                    <div className={`flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 ${
                      isSelected
                        ? 'bg-gradient-to-r from-amber-500 to-orange-500 shadow-lg'
                        : 'bg-gray-100 group-hover:bg-gray-200'
                    }`}>
                      <IconComponent className={`h-6 w-6 transition-all duration-300 ${
                        isSelected ? 'text-white' : 'text-gray-600'
                      }`} />
                    </div>

                    {/* Contenido */}
                    <div className="flex-1 min-w-0">
                      <div className={`font-semibold text-lg transition-all duration-300 ${
                        isSelected ? 'text-amber-900' : 'text-gray-900'
                      }`}>
                        {type.label}
                      </div>
                      <div className={`text-sm transition-all duration-300 ${
                        isSelected ? 'text-amber-700' : 'text-gray-500'
                      }`}>
                        {type.description}
                      </div>
                    </div>

                    {/* Indicador de selección */}
                    <div className="flex-shrink-0">
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                        isSelected
                          ? 'border-amber-500 bg-amber-500'
                          : 'border-gray-300'
                      }`}>
                        {isSelected && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                    </div>
                  </label>
                );
              })}
            </div>
          </div>

          {/* Formulario de login */}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Campo de email/documento */}
              <div>
                <label htmlFor="identifier" className="block text-sm font-semibold text-gray-700 mb-3">
                  Email o Documento
                </label>
                <div className="relative group">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <FaUser className={`h-5 w-5 transition-colors duration-200 ${
                      errors.identifier ? 'text-red-400' : 'text-gray-400 group-focus-within:text-amber-500'
                    }`} />
                  </div>
                  <input
                    id="identifier"
                    name="identifier"
                    type="text"
                    autoComplete="username"
                    required
                    value={formData.identifier}
                    onChange={handleInputChange}
                    className={`block w-full pl-12 pr-4 py-4 border-2 rounded-xl text-gray-900 placeholder-gray-400 transition-all duration-200 ${
                      errors.identifier
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                        : 'border-gray-200 focus:border-amber-500 focus:ring-amber-500 hover:border-gray-300'
                    } focus:outline-none focus:ring-4 focus:ring-opacity-20 shadow-sm focus:shadow-md`}
                    placeholder={isEmailFormat(formData.identifier) ? "<EMAIL>" : "Número de documento"}
                  />
                </div>
                {errors.identifier && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-700 flex items-center">
                      <FaExclamationTriangle className="h-4 w-4 mr-2 text-red-500" />
                      {errors.identifier}
                    </p>
                  </div>
                )}
              </div>

              {/* Campo de contraseña */}
              <div>
                <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-3">
                  Contraseña
                </label>
                <div className="relative group">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <FaLock className={`h-5 w-5 transition-colors duration-200 ${
                      errors.password ? 'text-red-400' : 'text-gray-400 group-focus-within:text-amber-500'
                    }`} />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`block w-full pl-12 pr-12 py-4 border-2 rounded-xl text-gray-900 placeholder-gray-400 transition-all duration-200 ${
                      errors.password
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                        : 'border-gray-200 focus:border-amber-500 focus:ring-amber-500 hover:border-gray-300'
                    } focus:outline-none focus:ring-4 focus:ring-opacity-20 shadow-sm focus:shadow-md`}
                    placeholder="Ingresa tu contraseña"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center group"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <FaEyeSlash className="h-5 w-5 text-gray-400 hover:text-amber-500 transition-colors duration-200" />
                    ) : (
                      <FaEye className="h-5 w-5 text-gray-400 hover:text-amber-500 transition-colors duration-200" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-700 flex items-center">
                      <FaExclamationTriangle className="h-4 w-4 mr-2 text-red-500" />
                      {errors.password}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Opciones adicionales */}
            <div className="flex items-center justify-between py-4">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded transition-colors duration-200"
                />
                <label htmlFor="remember-me" className="ml-3 block text-sm font-medium text-gray-700">
                  Recordarme
                </label>
              </div>
              <div className="text-sm">
                <a
                  href="#"
                  className="font-semibold text-amber-600 hover:text-amber-700 transition-colors duration-200 hover:underline"
                >
                  ¿Olvidaste tu contraseña?
                </a>
              </div>
            </div>

            {/* Botón de submit */}
            <div className="pt-2">
              <button
                type="submit"
                disabled={loading}
                className={`group relative w-full flex justify-center items-center py-4 px-6 border border-transparent text-base font-semibold rounded-xl text-white transition-all duration-300 ${
                  loading
                    ? 'bg-amber-400 cursor-not-allowed opacity-75'
                    : 'bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 focus:outline-none focus:ring-4 focus:ring-amber-500 focus:ring-opacity-50 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl'
                }`}
              >
                {loading ? (
                  <>
                    <FaSpinner className="animate-spin h-5 w-5 mr-3" />
                    <span>Iniciando sesión...</span>
                  </>
                ) : (
                  <>
                    <FaUser className="h-5 w-5 mr-3" />
                    <span>Iniciar Sesión</span>
                  </>
                )}
              </button>
            </div>

            {/* Separador */}
            <div className="relative py-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white text-gray-500 font-medium">¿Nuevo en BAT-7?</span>
              </div>
            </div>

            {/* Enlaces adicionales */}
            <div className="text-center">
              <p className="text-sm text-gray-600">
                ¿No tienes cuenta?{' '}
                <Link
                  to="/register"
                  className="font-semibold text-amber-600 hover:text-amber-700 transition-colors duration-200 hover:underline"
                >
                  Regístrate aquí
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
