/* Estilos adicionales para el LoginForm */

/* Animaciones personalizadas */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Clases de animación */
.fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.slideInLeft {
  animation: slideInLeft 0.8s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Mejoras para el gradiente de fondo */
.backgroundGradient {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 25%, #1d4ed8 50%, #1e40af 75%, #312e81 100%);
  position: relative;
}

.backgroundGradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* Efectos de hover mejorados */
.userTypeCard {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.userTypeCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.userTypeCard.selected {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.25);
}

/* Efectos de focus mejorados para inputs */
.inputField {
  transition: all 0.3s ease;
}

.inputField:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

/* Botón con efectos avanzados */
.submitButton {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.submitButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.submitButton:hover::before {
  left: 100%;
}

.submitButton:hover {
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

/* Mejoras de responsividad */
@media (max-width: 1024px) {
  .leftPanel {
    display: none;
  }
  
  .rightPanel {
    width: 100%;
    padding: 2rem 1rem;
  }
}

@media (max-width: 640px) {
  .rightPanel {
    padding: 1.5rem 1rem;
  }
  
  .formContainer {
    max-width: 100%;
  }
  
  .userTypeCard {
    padding: 1rem;
  }
  
  .inputField {
    padding: 0.875rem 1rem 0.875rem 3rem;
  }
  
  .submitButton {
    padding: 1rem 1.5rem;
  }
}

/* Efectos de carga */
.loadingSpinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mejoras para el patrón de fondo */
.backgroundPattern {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Efectos de glassmorphism */
.glassEffect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Mejoras para los iconos */
.iconContainer {
  transition: all 0.3s ease;
}

.iconContainer:hover {
  transform: scale(1.1);
}

/* Efectos de texto */
.gradientText {
  background: linear-gradient(135deg, #f59e0b, #f97316);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Sombras personalizadas */
.customShadow {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

.customShadowHover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(245, 158, 11, 0.1);
}

/* Mejoras para la accesibilidad */
.focusVisible:focus-visible {
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

/* Transiciones suaves para todos los elementos */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}
