import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { FaSpinner } from 'react-icons/fa';

const ProtectedRoute = ({ allowedRoles }) => {
  const { user, loading, isAuthenticated } = useAuth();

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <FaSpinner className="animate-spin text-blue-500 text-4xl" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Si se especifican roles permitidos, verificar si el usuario tiene alguno de ellos
  if (allowedRoles && allowedRoles.length > 0) {
    const userRole = user?.role?.toLowerCase() || user?.tipo_usuario?.toLowerCase() || '';
    
    const hasAllowedRole = allowedRoles.some(role => {
      const roleToCheck = role.toLowerCase();
      return userRole.includes(roleToCheck);
    });

    if (!hasAllowedRole) {
      // Redirigir a una página de acceso denegado o al inicio
      return <Navigate to="/unauthorized" replace />;
    }
  }

  return <Outlet />;
};

export default ProtectedRoute;