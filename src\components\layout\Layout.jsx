import React, { useState, useRef, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PageTransition from '../transitions/PageTransition';

// Componente Sidebar con favoritos
const Sidebar = ({ isOpen, toggleSidebar, onLogout }) => {
  const location = useLocation();

  // Inicializar favoritos desde localStorage o usar valores predeterminados
  const [favorites, setFavorites] = useState(() => {
    const savedFavorites = localStorage.getItem('sidebarFavorites');
    return savedFavorites ? JSON.parse(savedFavorites) : {
      dashboard: false,
      home: false,
      patients: false,
      tests: false,
      reports: false,
      administration: false,
      settings: false,
      help: false
    };
  });

  // Guardar favoritos en localStorage cuando cambien
  useEffect(() => {
    localStorage.setItem('sidebarFavorites', JSON.stringify(favorites));
  }, [favorites]);

  // Toggle para favoritos
  const toggleFavorite = (key, e) => {
    e.preventDefault();
    e.stopPropagation();
    setFavorites(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Verificar si una ruta está activa
  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }

    return location.pathname === path ||
           (location.pathname.startsWith(path) &&
            (location.pathname.length === path.length ||
             location.pathname[path.length] === '/'));
  };

  // Elementos del menú
  const menuItems = [
    { name: 'Inicio', path: '/home', icon: 'home', key: 'home' },
    { name: 'Pacientes', path: '/admin/patients', icon: 'user', key: 'patients' },
    { name: 'Cuestionario', path: '/student/questionnaire', icon: 'clipboard', key: 'tests' },
    { name: 'Resultados', path: '/admin/reports', icon: 'chart-bar', key: 'reports' },
    { name: 'Administración', path: '/admin/administration', icon: 'shield-alt', key: 'administration' },
    { name: 'Configuración', path: '/dashboard', icon: 'cog', key: 'settings' },
    { name: 'Ayuda', path: '/', icon: 'question-circle', key: 'help' }
  ];

  // Filtrar favoritos
  const favoriteItems = menuItems.filter(item => favorites[item.key]);

  return (
    <div className={`sidebar bg-[#121940] text-[#a4b1cd] fixed top-0 left-0 h-full z-50 transition-all duration-300 ease-in-out
                     ${isOpen ? 'w-64' : 'w-[70px]'}`}>
      <div className="sidebar-header p-5 flex justify-between items-center border-b border-opacity-10 border-white">
        {isOpen && (
          <h1 className="text-2xl font-bold text-white text-center flex-1">
            Activatu<span className="text-[#ffda0a]">mente</span>
          </h1>
        )}
        <button
          onClick={toggleSidebar}
          className="text-[#a4b1cd] cursor-pointer hover:text-white transition-colors duration-200"
        >
          <i className={`fas ${isOpen ? 'fa-chevron-left' : 'fa-chevron-right'}`}></i>
        </button>
      </div>

      {/* Sección de favoritos */}
      {favoriteItems.length > 0 && (
        <div className="sidebar-section py-2 border-b border-opacity-10 border-white">
          {isOpen && (
            <h2 className="uppercase text-xs px-5 py-2 tracking-wider font-semibold text-gray-400">
              FAVORITOS
            </h2>
          )}
          <ul className="menu-list">
            {favoriteItems.map((item) => (
              <li
                key={`fav-${item.key}`}
                className={`py-3 px-5 hover:bg-opacity-10 hover:bg-white transition-all duration-300 relative transform hover:translate-x-1
                          ${isActive(item.path) ? 'bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg' : ''}`}
              >
                <div className="flex items-center justify-between w-full">
                  <Link
                    to={item.path}
                    className={`flex items-center flex-grow transition-colors duration-200 ${isActive(item.path) ? 'text-[#ffda0a] font-semibold' : 'text-[#a4b1cd] hover:text-white'}`}
                  >
                    <i className={`fas fa-${item.icon} ${!isOpen ? '' : 'mr-3'} w-5 text-center transition-colors duration-200 ${isActive(item.path) ? 'text-[#ffda0a]' : ''}`}></i>
                    {isOpen && <span>{item.name}</span>}
                  </Link>
                  {isOpen && (
                    <span
                      className="text-[#ffda0a] cursor-pointer hover:scale-110 transition-transform duration-200"
                      onClick={(e) => toggleFavorite(item.key, e)}
                      title="Quitar de favoritos"
                    >
                      <i className="fas fa-star"></i>
                    </span>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Menú principal */}
      <div className="sidebar-content py-2">
        <ul className="menu-list">
          {menuItems.map((item) => (
            <li
              key={item.name}
              className={`py-3 px-5 hover:bg-opacity-10 hover:bg-white transition-all duration-300 relative transform hover:translate-x-1
                        ${isActive(item.path) ? 'bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg' : ''}`}
            >
              <div className="flex items-center justify-between w-full">
                <Link
                  to={item.path}
                  className={`flex items-center flex-grow transition-colors duration-200 ${isActive(item.path) ? 'text-[#ffda0a] font-semibold' : 'text-[#a4b1cd] hover:text-white'}`}
                >
                  <i className={`fas fa-${item.icon} ${!isOpen ? '' : 'mr-3'} w-5 text-center transition-colors duration-200 ${isActive(item.path) ? 'text-[#ffda0a]' : ''}`}></i>
                  {isOpen && <span>{item.name}</span>}
                </Link>
                {isOpen && (
                  <span
                    className={`cursor-pointer hover:text-[#ffda0a] hover:scale-110 transition-all duration-200 ${favorites[item.key] ? 'text-[#ffda0a]' : 'text-gray-400'}`}
                    onClick={(e) => toggleFavorite(item.key, e)}
                    title={favorites[item.key] ? "Quitar de favoritos" : "Añadir a favoritos"}
                  >
                    <i className={`${favorites[item.key] ? 'fas' : 'far'} fa-star`}></i>
                  </span>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* Cerrar Sesión */}
      <div className="mt-auto p-5 border-t border-opacity-10 border-white">
        {isOpen ? (
          <div
            className="flex items-center text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 hover:bg-red-500 hover:bg-opacity-10 rounded-lg p-2 transform hover:scale-105"
            onClick={onLogout}
          >
            <i className="fas fa-sign-out-alt mr-3"></i>
            <span>Cerrar Sesión</span>
          </div>
        ) : (
          <div
            className="flex justify-center text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 transform hover:scale-110"
            onClick={onLogout}
            title="Cerrar Sesión"
          >
            <i className="fas fa-sign-out-alt"></i>
          </div>
        )}
      </div>
    </div>
  );
};

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const userMenuRef = useRef(null);
  const navigate = useNavigate();

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleUserMenu = () => {
    setUserMenuOpen(!userMenuOpen);
  };

  // Función para cerrar sesión
  const handleLogout = () => {
    try {
      // Limpiar localStorage
      localStorage.clear();

      // Limpiar sessionStorage también
      sessionStorage.clear();

      // Mostrar mensaje de confirmación
      console.log('Sesión cerrada correctamente');

      // Redirigir a la página de login
      navigate('/login');

      // Forzar recarga de la página para limpiar cualquier estado residual
      setTimeout(() => {
        window.location.reload();
      }, 100);

    } catch (error) {
      console.error('Error al cerrar sesión:', error);
      // En caso de error, forzar navegación
      window.location.href = '/login';
    }
  };

  // Cerrar el menú de usuario cuando se hace clic fuera de él
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar con favoritos */}
      <Sidebar isOpen={sidebarOpen} toggleSidebar={toggleSidebar} onLogout={handleLogout} />

      {/* Contenido principal */}
      <div className={`flex-1 transition-all duration-300 ease-in-out
                    ${sidebarOpen ? 'ml-64' : 'ml-[70px]'}`}>
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16 items-center">
              <div className="flex items-center">
                <h1 className="text-xl font-bold text-blue-600 text-center">BAT-7</h1>
              </div>

              {/* Información del usuario */}
              <div className="flex items-center relative" ref={userMenuRef}>
                <div
                  className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg px-3 py-2 transition-colors"
                  onClick={toggleUserMenu}
                >
                  <div className="flex flex-col items-end">
                    <span className="text-sm font-medium text-gray-800">Usuario Administrador</span>
                    <span className="text-xs text-gray-500">Rol: Administrador • Activo</span>
                  </div>
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-md">
                    <i className="fas fa-user-shield"></i>
                  </div>
                  <div className="text-gray-400">
                    <i className={`fas fa-chevron-${userMenuOpen ? 'up' : 'down'} text-xs`}></i>
                  </div>
                </div>

                {/* Menú desplegable del usuario */}
                {userMenuOpen && (
                  <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 z-50 py-2">
                    {/* Header del menú */}
                    <div className="px-4 py-3 border-b border-gray-100 bg-gray-50 rounded-t-lg">
                      <div className="flex items-center space-x-3">
                        <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white">
                          <i className="fas fa-user-shield"></i>
                        </div>
                        <div>
                          <p className="text-sm font-semibold text-gray-900">Usuario Administrador</p>
                          <p className="text-xs text-gray-500"><EMAIL></p>
                          <div className="flex items-center mt-1">
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <span className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></span>
                              Activo
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Opciones del menú */}
                    <div className="py-1">
                      <Link
                        to="/profile"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <i className="fas fa-user-cog mr-3 text-gray-400"></i>
                        Mi Perfil
                      </Link>

                      <Link
                        to="/settings"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <i className="fas fa-cog mr-3 text-gray-400"></i>
                        Configuración
                      </Link>

                      <Link
                        to="/help"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <i className="fas fa-question-circle mr-3 text-gray-400"></i>
                        Ayuda
                      </Link>

                      <div className="border-t border-gray-100 my-1"></div>

                      <button
                        className="flex w-full items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200 transform hover:scale-105"
                        onClick={() => {
                          setUserMenuOpen(false);
                          handleLogout();
                        }}
                      >
                        <i className="fas fa-sign-out-alt mr-3 text-red-500"></i>
                        Cerrar Sesión
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Contenido principal */}
        <main className="py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Contenido de la página con transición */}
            <PageTransition>
              <Outlet />
            </PageTransition>
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t border-gray-200 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <p className="text-center text-gray-500 text-sm">
              &copy; {new Date().getFullYear()} BAT-7 Evaluaciones. Todos los derechos reservados.
            </p>
          </div>
        </footer>

        {/* Contenedor de notificaciones Toast */}
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </div>
    </div>
  );
};

export default Layout;