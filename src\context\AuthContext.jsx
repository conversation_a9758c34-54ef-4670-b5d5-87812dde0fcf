import React, { createContext, useState, useEffect, useContext } from 'react';
import supabase from '../api/supabaseClient';
import { FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getSessionAndProfile = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setSession(session);
        
        if (session?.user) {
          const { data: profile } = await supabase
            .from('usuarios')
            .select('*')
            .eq('id', session.user.id)
            .single();

          setUser({ ...session.user, ...profile });
        }
      } catch (error) {
        console.error('Error al obtener sesión y perfil:', error);
      } finally {
        setLoading(false);
      }
    };

    getSessionAndProfile();

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        setSession(session);
        const currentUser = session?.user ?? null;
        if (currentUser) {
          try {
            const { data: profile } = await supabase
              .from('usuarios')
              .select('*')
              .eq('id', currentUser.id)
              .single();

            setUser({ ...currentUser, ...profile });
          } catch (error) {
            console.error('Error al obtener perfil de usuario:', error);
          }
        } else {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => {
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, []);

  // Login function - soporta email o documento
  const login = async ({ email, identifier, password }, remember = false) => {
    try {
      setLoading(true);

      let loginEmail = email || identifier;

      // Si el identifier no es un email, buscar el email por documento
      if (identifier && !identifier.includes('@')) {
        const { data: userProfile, error: profileError } = await supabase
          .from('usuarios')
          .select('id')
          .eq('documento', identifier)
          .single();

        if (profileError || !userProfile) {
          throw new Error('Usuario no encontrado con ese documento');
        }

        // Obtener el email del usuario desde auth.users
        const { data: authUsers, error: authError } = await supabase
          .from('auth.users')
          .select('email')
          .eq('id', userProfile.id)
          .single();

        if (authError || !authUsers) {
          // Fallback: intentar obtener desde la API de admin (requiere permisos especiales)
          throw new Error('No se pudo obtener el email asociado al documento');
        }

        loginEmail = authUsers.email;
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email: loginEmail,
        password
      });

      if (error) throw error;

      // Obtener perfil del usuario
      const { data: profile } = await supabase
        .from('usuarios')
        .select('*')
        .eq('id', data.user.id)
        .single();

      const userWithProfile = { ...data.user, ...profile };
      setUser(userWithProfile);

      // Actualizar último acceso
      await supabase
        .from('usuarios')
        .update({ ultimo_acceso: new Date().toISOString() })
        .eq('id', data.user.id);

      toast.success('Inicio de sesión exitoso');

      return {
        success: true,
        user: userWithProfile
      };
    } catch (error) {
      console.error('Error de inicio de sesión:', error.message);
      toast.error(error.message || 'Error al iniciar sesión');
      return {
        success: false,
        message: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async ({ email, password, ...userData }) => {
    try {
      setLoading(true);
      
      // Registrar usuario en Auth
      const { data, error } = await supabase.auth.signUp({
        email,
        password
      });

      if (error) throw error;

      // Crear perfil en la tabla usuarios
      const { error: profileError } = await supabase
        .from('usuarios')
        .insert([
          {
            id: data.user.id,
            documento: userData.documento,
            nombre: userData.nombre,
            apellido: userData.apellido,
            rol: userData.rol || 'estudiante',
            activo: true,
            fecha_creacion: new Date().toISOString()
          }
        ]);

      if (profileError) throw profileError;

      toast.success('Registro exitoso');
      
      return {
        success: true,
        user: data.user,
        message: 'Registro exitoso'
      };
    } catch (error) {
      console.error('Error de registro:', error.message);
      toast.error(error.message || 'Error al registrar usuario');
      return {
        success: false,
        message: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      
      if (error) throw error;
      
      setUser(null);
      setSession(null);
      
      toast.info('Sesión cerrada correctamente');
      
      return { success: true };
    } catch (error) {
      console.error('Error al cerrar sesión:', error.message);
      toast.error(error.message || 'Error al cerrar sesión');
      return {
        success: false,
        message: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (email) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
      
      if (error) throw error;
      
      toast.success('Se ha enviado un enlace para restablecer la contraseña');
      
      return { success: true };
    } catch (error) {
      console.error('Error al restablecer contraseña:', error.message);
      toast.error(error.message || 'Error al restablecer contraseña');
      return {
        success: false,
        message: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Update password function
  const updatePassword = async (newPassword) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });
      
      if (error) throw error;
      
      toast.success('Contraseña actualizada correctamente');
      
      return { success: true };
    } catch (error) {
      console.error('Error al actualizar contraseña:', error.message);
      toast.error(error.message || 'Error al actualizar contraseña');
      return {
        success: false,
        message: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Determinar roles y permisos
  const userRole = user?.rol?.toLowerCase() || '';
  const isAdmin = userRole === 'administrador';
  const isPsychologist = userRole === 'psicologo';
  const isStudent = userRole === 'estudiante';

  const value = {
    // Authentication state
    user,
    session,
    loading,
    isAuthenticated: !!user,

    // Authentication functions
    login,
    register,
    logout,
    resetPassword,
    updatePassword,

    // Roles and permissions
    isAdmin,
    isPsychologist,
    isStudent,
    userRole,

    // User properties
    userId: user?.id,
    userEmail: user?.email,
    userDocumento: user?.documento,
    userName: user ? `${user.nombre || ''} ${user.apellido || ''}`.trim() : '',
    userFirstName: user?.nombre,
    userLastName: user?.apellido,

    // Session info
    sessionCreated: user?.fecha_creacion,
    lastAccess: user?.ultimo_acceso
  };

  return (
    <AuthContext.Provider value={value}>
      {loading ? (
        <div className="flex justify-center items-center h-screen">
          <FaSpinner className="animate-spin text-blue-500 text-4xl" />
        </div>
      ) : (
        children
      )}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

export default AuthContext;
