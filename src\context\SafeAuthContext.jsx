import React, { createContext, useState, useEffect, useContext } from 'react';
import supabase from '../api/supabaseClient';

/**
 * Contexto de autenticación seguro que maneja errores de conexión
 */
const SafeAuthContext = createContext();

export const SafeAuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [connectionError, setConnectionError] = useState(false);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('SafeAuthContext: Inicializando autenticación...');
        
        // Intentar obtener sesión con timeout
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 5000)
        );

        const { data: { session }, error } = await Promise.race([
          sessionPromise,
          timeoutPromise
        ]);

        if (error) {
          console.error('Error obteniendo sesión:', error);
          setConnectionError(true);
          setLoading(false);
          return;
        }

        console.log('SafeAuthContext: Sesión obtenida:', !!session);
        setSession(session);

        if (session?.user) {
          // Intentar obtener perfil con manejo de errores
          try {
            const { data: profile, error: profileError } = await supabase
              .from('usuarios')
              .select('*')
              .eq('auth_id', session.user.id)
              .single();

            if (profileError) {
              console.warn('No se encontró perfil, usando datos básicos:', profileError);
              // Usuario autenticado pero sin perfil en BD
              setUser({
                ...session.user,
                tipo_usuario: 'candidato',
                nombre: session.user.user_metadata?.nombre || 'Usuario',
                apellido: session.user.user_metadata?.apellido || '',
                email: session.user.email
              });
            } else {
              console.log('Perfil encontrado:', profile);
              setUser({
                ...session.user,
                ...profile
              });
            }
          } catch (profileError) {
            console.error('Error obteniendo perfil:', profileError);
            // Fallback a usuario básico
            setUser({
              ...session.user,
              tipo_usuario: 'candidato',
              nombre: session.user.user_metadata?.nombre || 'Usuario',
              apellido: session.user.user_metadata?.apellido || '',
              email: session.user.email
            });
          }
        }

        // Configurar listener de cambios de auth
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log('SafeAuthContext: Auth state changed:', event);
            setSession(session);
            
            if (session?.user) {
              // Simplificar: usar solo datos de sesión
              setUser({
                ...session.user,
                tipo_usuario: 'candidato',
                nombre: session.user.user_metadata?.nombre || 'Usuario',
                apellido: session.user.user_metadata?.apellido || '',
                email: session.user.email
              });
            } else {
              setUser(null);
            }
          }
        );

        setLoading(false);
        console.log('SafeAuthContext: Inicialización completada');

        return () => {
          subscription?.unsubscribe();
        };

      } catch (error) {
        console.error('SafeAuthContext: Error en inicialización:', error);
        setConnectionError(true);
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email, password) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error en login:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        throw error;
      }

      setUser(null);
      setSession(null);
      return { success: true };
    } catch (error) {
      console.error('Error en logout:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    session,
    loading,
    connectionError,
    login,
    logout,
    isAuthenticated: !!user,
    userRole: user?.tipo_usuario || null,
    isAdmin: user?.tipo_usuario === 'administrador',
    isPsicologo: user?.tipo_usuario === 'psicologo',
    isCandidato: user?.tipo_usuario === 'candidato'
  };

  // Mostrar error de conexión si es necesario
  if (connectionError) {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-xl font-bold text-red-600 mb-4">
            Error de Conexión
          </h1>
          <p className="text-gray-600 mb-6">
            No se pudo conectar con Supabase. Verifica tu conexión a internet y la configuración.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
          >
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <SafeAuthContext.Provider value={value}>
      {children}
    </SafeAuthContext.Provider>
  );
};

export const useSafeAuth = () => {
  const context = useContext(SafeAuthContext);
  if (!context) {
    throw new Error('useSafeAuth debe usarse dentro de SafeAuthProvider');
  }
  return context;
};

export default SafeAuthContext;
