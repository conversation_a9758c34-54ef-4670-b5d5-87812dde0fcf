import React from 'react';
import ReactDOM from 'react-dom/client';
import TestApp from './TestApp.jsx';
// import { Provider } from 'react-redux';
// import { store } from './store';
// import { AuthProvider } from './context/AuthContext';
// import App from './App.jsx';
import './index.css';

// Renderizar la aplicación de prueba mínima
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <TestApp />
  </React.StrictMode>,
);

// Versión completa (comentada temporalmente)
/*
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <AuthProvider>
        <App />
      </AuthProvider>
    </Provider>
  </React.StrictMode>,
);
*/
