import { useAuth } from '../../context/AuthContext';
import CandidatesTab from '../../components/tabs/candidates/CandidatesTab';
import LoadingFallback from '../../components/ui/LoadingFallback';

/**
 * Página de gestión de candidatos
 * Migrada de la antigua página de pacientes al esquema robusto
 */
const Candidates = () => {
  const { user, isAdmin, isPsychologist, loading } = useAuth();

  if (loading) {
    return <LoadingFallback message="Cargando..." />;
  }

  // Solo administradores y psicólogos pueden acceder
  if (!isAdmin && !isPsychologist) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600 mb-4">Acceso Denegado</h2>
        <p className="text-gray-600">
          Solo los administradores y psicólogos pueden gestionar candidatos.
        </p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Gestión de Candidatos
        </h1>
        <p className="text-gray-600">
          Administra la información de los candidatos para evaluaciones psicométricas
        </p>
      </div>

      <CandidatesTab isAdmin={isAdmin} />
    </div>
  );
};

export default Candidates;
