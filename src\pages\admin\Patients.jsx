import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNoAuth } from '../../context/NoAuthContext';
import { toast } from 'react-toastify';
import {
  FaUsers, FaSearch, FaPlus, FaTrash, FaEdit, FaSpinner,
  FaCog, FaBuilding, FaUserMd, FaFileAlt, FaDatabase
} from 'react-icons/fa';

// Services
import { patientServices } from '../../components/tabs/patients/PatientsConfig';
import supabase from '../../api/supabaseClient';

/**
 * Enhanced Patients Management Page
 * Modern design matching the administration panel
 */
const Patients = () => {
  const { user, isAdmin, isPsicologo, isCandidato, loading: authLoading } = useNoAuth();
  
  // Variables derivadas para compatibilidad
  const isPsychologist = isPsicologo;
  const isCandidate = isCandidato;

  // Main state
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Search and filtering
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);

  // Sorting
  const [sortField, setSortField] = useState('nombre');
  const [sortDirection, setSortDirection] = useState('asc');

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex items-center">
          <FaSpinner className="animate-spin text-blue-500 text-2xl mr-3" />
          <span className="text-gray-600">Cargando...</span>
        </div>
      </div>
    );
  }

  // Access control
  if (!isAdmin && !isPsychologist) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaUsers className="text-red-600 text-2xl" />
          </div>
          <h2 className="text-2xl font-bold text-red-600 mb-4">Acceso Denegado</h2>
          <p className="text-gray-600 mb-6">
            Solo los administradores y psicólogos pueden gestionar pacientes.
          </p>
          <button 
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Volver
          </button>
        </div>
      </div>
    );
  }

  // Load patients data
  const loadPatients = useCallback(async (forceRefresh = false) => {
    if (loading && !forceRefresh) return;
    
    setLoading(true);
    setError(null);

    try {
      const result = await patientServices.fetchEntities(sortField, sortDirection);
      if (result.error) throw result.error;
      
      setPatients(Array.isArray(result.data) ? result.data : []);
    } catch (error) {
      console.error('Error loading patients:', error);
      setError(error);
      toast.error('Error al cargar los pacientes');
    } finally {
      setLoading(false);
    }
  }, [sortField, sortDirection]);

  // Load data on mount and when sorting changes
  useEffect(() => {
    loadPatients();
    // Ejecutar prueba de conexión con Supabase
    testSupabaseConnection();
  }, [loadPatients]);

  // Función para probar la conexión con Supabase
  const testSupabaseConnection = async () => {
    try {
      console.log('🔍 Probando conexión con Supabase...');

      // Probar conexión básica con pacientes
      const { data: patientsCount, error: patientsCountError } = await supabase
        .from('pacientes')
        .select('*', { count: 'exact', head: true });

      if (patientsCountError) {
        console.error('❌ Error al contar pacientes:', patientsCountError);
      } else {
        console.log(`✅ Total de pacientes: ${patientsCount.count || 0}`);
      }

      // Obtener muestra de pacientes
      const { data: patients, error: patientsError } = await supabase
        .from('pacientes')
        .select('*')
        .limit(5);

      if (patientsError) {
        console.error('❌ Error al obtener pacientes:', patientsError);
      } else {
        console.log('👥 Muestra de pacientes:', patients);
      }

      // Verificar resultados de evaluaciones
      const { data: resultsCount, error: resultsError } = await supabase
        .from('resultados_evaluacion')
        .select('*', { count: 'exact', head: true });

      if (resultsError) {
        console.error('❌ Error al contar resultados:', resultsError);
      } else {
        console.log(`📋 Total de resultados de evaluación: ${resultsCount.count || 0}`);
      }

    } catch (error) {
      console.error('💥 Error inesperado:', error);
    }
  };

  // Filter and search patients
  const filteredPatients = useMemo(() => {
    return patients.filter(patient => {
      // Search filter
      const searchMatch = !searchTerm || 
        patient.nombre?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patient.apellidos?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patient.documento_identidad?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patient.email?.toLowerCase().includes(searchTerm.toLowerCase());

      return searchMatch;
    });
  }, [patients, searchTerm]);

  // Paginated patients
  const paginatedPatients = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredPatients.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredPatients, currentPage, itemsPerPage]);

  // Total pages
  const totalPages = Math.ceil(filteredPatients.length / itemsPerPage);

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page
  };

  // CRUD Operations
  const handleCreate = () => {
    toast.info('Funcionalidad de crear paciente en desarrollo');
  };

  const handleEdit = (patient) => {
    toast.info('Funcionalidad de editar paciente en desarrollo');
  };

  const handleDelete = async (patient) => {
    if (!window.confirm(`¿Está seguro de eliminar al paciente ${patient.nombre} ${patient.apellidos}?`)) {
      return;
    }

    try {
      setLoading(true);
      const result = await patientServices.deleteEntity(patient.id);
      if (result.error) throw result.error;
      
      toast.success('Paciente eliminado correctamente');
      loadPatients();
    } catch (error) {
      console.error('Error deleting patient:', error);
      toast.error('Error al eliminar el paciente');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section with Blue Background */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mr-4">
                <FaCog className="text-white text-xl" />
              </div>
              <h1 className="text-3xl font-bold">
                Panel de Administración
              </h1>
            </div>
            <p className="text-blue-100">
              Gestión centralizada de recursos de la plataforma
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 overflow-x-auto">
            <button className="flex items-center px-4 py-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300">
              <FaBuilding className="mr-2 text-orange-500" />
              Instituciones
            </button>
            <button className="flex items-center px-4 py-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300">
              <FaUserMd className="mr-2 text-gray-500" />
              Psicólogos
            </button>
            <button className="flex items-center px-4 py-4 text-sm font-medium text-blue-600 border-b-2 border-blue-600">
              <FaUsers className="mr-2 text-blue-600" />
              Pacientes
            </button>
            <button className="flex items-center px-4 py-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300">
              <FaFileAlt className="mr-2 text-gray-500" />
              Conversión PD→PC
            </button>
            <button className="flex items-center px-4 py-4 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300">
              <FaDatabase className="mr-2 text-gray-500" />
              Supabase
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Section Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Gestión de Pacientes</h2>
              <p className="text-gray-600 mt-1">
                Administre los pacientes registrados en el sistema
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {/* Search */}
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar paciente..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              {/* Add New Button */}
              {isAdmin && (
                <button
                  onClick={handleCreate}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FaPlus className="mr-2" />
                  Nuevo Paciente
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Data Table */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-blue-600 text-white">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Nombre
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Género
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Fecha de Nacimiento
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                    Notas
                  </th>
                  {isAdmin && (
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                      Acciones
                    </th>
                  )}
                </tr>
              </thead>
              
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={isAdmin ? 6 : 5} className="px-6 py-12 text-center">
                      <div className="flex items-center justify-center">
                        <FaSpinner className="animate-spin text-blue-500 text-2xl mr-3" />
                        <span className="text-gray-600">Cargando pacientes...</span>
                      </div>
                    </td>
                  </tr>
                ) : filteredPatients.length === 0 ? (
                  <tr>
                    <td colSpan={isAdmin ? 6 : 5} className="px-6 py-12 text-center text-gray-500">
                      {patients.length === 0 ? 'No hay pacientes registrados' : 'No se encontraron pacientes que coincidan con la búsqueda'}
                    </td>
                  </tr>
                ) : (
                  paginatedPatients.map((patient) => (
                    <tr key={patient.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-sm font-medium">
                              {patient.nombre?.charAt(0)?.toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {patient.nombre} {patient.apellidos}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {patient.documento_identidad}
                            </div>
                          </div>
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {patient.email || '-'}
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                        {patient.genero || '-'}
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {patient.fecha_nacimiento ? 
                          new Date(patient.fecha_nacimiento).toLocaleDateString('es-ES') : 
                          '-'
                        }
                      </td>
                      
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {patient.notas || '-'}
                      </td>
                      
                      {isAdmin && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEdit(patient)}
                              className="text-blue-600 hover:text-blue-900 transition-colors"
                              title="Editar paciente"
                            >
                              <FaEdit />
                            </button>
                            <button
                              onClick={() => handleDelete(patient)}
                              className="text-red-600 hover:text-red-900 transition-colors"
                              title="Eliminar paciente"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      )}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          © 2025 Sistema de Gestión Psicológica - Panel de Administración
        </div>
      </div>
    </div>
  );
};

export default Patients;
