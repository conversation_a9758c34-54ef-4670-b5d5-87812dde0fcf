import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaU<PERSON>, FaLock, FaSpinner } from 'react-icons/fa';

/**
 * Página de login básica sin dependencias complejas
 */
const BasicLogin = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('123456');
  const [selectedRole, setSelectedRole] = useState('administrador');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    // Simular login
    setTimeout(() => {
      // Guardar datos básicos en localStorage
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('userRole', selectedRole);
      localStorage.setItem('userEmail', email);
      
      setLoading(false);
      
      // Redirigir según el rol
      switch (selectedRole) {
        case 'administrador':
          navigate('/admin/administration');
          break;
        case 'psicologo':
          navigate('/admin/candidates');
          break;
        case 'candidato':
        default:
          navigate('/home');
          break;
      }
    }, 1000);
  };

  const handleQuickLogin = (role) => {
    setSelectedRole(role);
    setLoading(true);
    
    setTimeout(() => {
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('userRole', role);
      localStorage.setItem('userEmail', '<EMAIL>');
      
      setLoading(false);
      
      // Redirigir según el rol
      switch (role) {
        case 'administrador':
          navigate('/admin/administration');
          break;
        case 'psicologo':
          navigate('/admin/candidates');
          break;
        case 'candidato':
        default:
          navigate('/home');
          break;
      }
    }, 500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-2">BAT-7</h1>
          <h2 className="text-xl text-blue-200 mb-8">Sistema de Evaluación</h2>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-lg shadow-xl p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <div className="relative">
                <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Ingresa tu email"
                  required
                />
              </div>
            </div>

            {/* Password */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contraseña
              </label>
              <div className="relative">
                <FaLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Ingresa tu contraseña"
                  required
                />
              </div>
            </div>

            {/* Role Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipo de Usuario
              </label>
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="administrador">Administrador</option>
                <option value="psicologo">Psicólogo</option>
                <option value="candidato">Candidato</option>
              </select>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
            >
              {loading ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  Iniciando sesión...
                </>
              ) : (
                'Iniciar Sesión'
              )}
            </button>
          </form>

          {/* Quick Login Buttons */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-600 text-center mb-4">Acceso rápido:</p>
            <div className="grid grid-cols-3 gap-2">
              <button
                onClick={() => handleQuickLogin('administrador')}
                disabled={loading}
                className="px-3 py-2 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
              >
                Admin
              </button>
              <button
                onClick={() => handleQuickLogin('psicologo')}
                disabled={loading}
                className="px-3 py-2 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
              >
                Psicólogo
              </button>
              <button
                onClick={() => handleQuickLogin('candidato')}
                disabled={loading}
                className="px-3 py-2 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
              >
                Candidato
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-blue-200 text-sm">
          <p>© 2024 BAT-7 Sistema de Evaluación</p>
        </div>
      </div>
    </div>
  );
};

export default BasicLogin;
