import React from 'react';
import { Card, CardBody, CardHeader } from '../../components/ui/Card';

const Home = () => {

  return (
    <div>
      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardBody>
            <div className="w-full h-auto rounded-lg mb-6 flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 relative">
              {/* Banner con imagen del cerebro y texto BATERÍA DE APTITUDES */}
              <div className="relative w-full h-[300px] flex items-center justify-center">
                {/* Fondo con patrón tecnológico */}
                <div className="absolute inset-0 opacity-20">
                  <div className="absolute top-10 left-10 w-20 h-20 border border-blue-300 rounded-full"></div>
                  <div className="absolute top-20 right-20 w-16 h-16 border border-purple-300 rounded-full"></div>
                  <div className="absolute bottom-10 left-1/4 w-12 h-12 border border-cyan-300 rounded-full"></div>
                  <div className="absolute bottom-20 right-1/3 w-14 h-14 border border-pink-300 rounded-full"></div>
                </div>

                {/* Imagen del cerebro */}
                <div className="absolute left-20 top-1/2 transform -translate-y-1/2">
                  <div className="w-32 h-32 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center shadow-2xl">
                    <i className="fas fa-brain text-white text-4xl"></i>
                  </div>
                  {/* Líneas de conexión */}
                  <div className="absolute top-1/2 left-full w-20 h-0.5 bg-gradient-to-r from-cyan-400 to-transparent"></div>
                </div>

                {/* Texto principal */}
                <div className="text-center z-10">
                  <h1 className="text-6xl font-black text-white mb-4 tracking-wider">
                    BAT-7
                  </h1>
                  <h2 className="text-2xl font-bold text-cyan-300 mb-2 tracking-wide">
                    BATERÍA DE APTITUDES
                  </h2>
                  <p className="text-lg text-blue-200 max-w-2xl mx-auto leading-relaxed">
                    Sistema integral de evaluación psicométrica para el análisis de aptitudes cognitivas
                  </p>
                </div>

                {/* Elementos decorativos adicionales */}
                <div className="absolute right-20 top-1/2 transform -translate-y-1/2">
                  <div className="w-24 h-24 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center shadow-2xl">
                    <i className="fas fa-chart-line text-white text-3xl"></i>
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 border-b border-blue-200">
            <h3 className="text-xl font-semibold text-blue-700 text-center py-2 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Descripción
            </h3>
          </CardHeader>
          <CardBody>
            <p className="text-gray-700 mb-4 text-center leading-relaxed px-4 py-2">
              A través de esta plataforma digital, podrás realizar la prueba de forma segura y eficiente. El objetivo es obtener
              una visión integral de tus fortalezas y potencial, asegurando un proceso de selección equitativo y orientado a
              identificar a los candidatos mejor preparados para los desafíos y oportunidades que ofrece cada institución.
            </p>
          </CardBody>
        </Card>
      </div>

      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 border-b border-green-200">
            <h3 className="text-xl font-semibold text-green-700 text-center py-2 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Aptitudes evaluadas
            </h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {/* Primera fila */}
              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mb-2">
                  <span className="font-semibold">V</span>
                </div>
                <span className="font-medium">Verbal</span>
                <p className="text-sm text-gray-600 mt-1">Comprensión y razonamiento verbal</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mb-2">
                  <span className="font-semibold">E</span>
                </div>
                <span className="font-medium">Espacial</span>
                <p className="text-sm text-gray-600 mt-1">Visualización y orientación espacial</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mb-2">
                  <span className="font-semibold">A</span>
                </div>
                <span className="font-medium">Atención</span>
                <p className="text-sm text-gray-600 mt-1">Capacidad de atención sostenida</p>
              </div>

              {/* Segunda fila */}
              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600 mb-2">
                  <span className="font-semibold">R</span>
                </div>
                <span className="font-medium">Razonamiento</span>
                <p className="text-sm text-gray-600 mt-1">Pensamiento lógico y analítico</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mb-2">
                  <span className="font-semibold">N</span>
                </div>
                <span className="font-medium">Numérica</span>
                <p className="text-sm text-gray-600 mt-1">Habilidades matemáticas y cálculo</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 mb-2">
                  <span className="font-semibold">M</span>
                </div>
                <span className="font-medium">Mecánica</span>
                <p className="text-sm text-gray-600 mt-1">Comprensión de principios mecánicos</p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600 mb-2">
                  <span className="font-semibold">O</span>
                </div>
                <span className="font-medium">Ortografía</span>
                <p className="text-sm text-gray-600 mt-1">Corrección ortográfica y lingüística</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default Home;




