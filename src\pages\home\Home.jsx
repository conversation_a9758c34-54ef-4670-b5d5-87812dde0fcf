import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '../../components/ui/Card';
import { useNavigate } from 'react-router-dom';
import { FaClipboard<PERSON>heck, Fa<PERSON>sers, FaChart<PERSON>ar, FaSpinner } from 'react-icons/fa';
import supabase from '../../api/supabaseClient';
import { useNoAuth as useAuth } from '../../context/NoAuthContext';
import Breadcrumbs from '../../components/navigation/Breadcrumbs';
// import DatabaseSetupGuide from '../../components/setup/DatabaseSetupGuide';

// Componente para tarjetas de KPI
const KpiCard = ({ title, value, icon, color, loading }) => (
  <Card className="shadow-md hover:shadow-lg transition-all duration-300">
    <CardBody className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-500 text-sm font-medium">{title}</p>
          {loading ? (
            <div className="flex items-center mt-1">
              <FaSpinner className="animate-spin text-gray-400 mr-2" />
              <span className="text-gray-400">Cargando...</span>
            </div>
          ) : (
            <h3 className="text-2xl font-bold mt-1">{value}</h3>
          )}
        </div>
        <div className={`p-3 rounded-full ${color} text-white`}>
          {icon}
        </div>
      </div>
    </CardBody>
  </Card>
);

const Home = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    candidatos: 0,
    evaluaciones: 0,
    resultados: 0,
    mis_candidatos: 0,
    mis_evaluaciones: 0,
    psicologos: 0,
    instituciones: 0
  });
  const [loadingStats, setLoadingStats] = useState(true);
  const [databaseError, setDatabaseError] = useState(false);
  const { user, isAdmin, userRole, loading: authLoading } = useAuth();
  
  // Obtener estadísticas
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoadingStats(true);
        // Obtener estadísticas usando la función RPC que respeta los roles
        const { data: dashboardStats, error: statsError } = await supabase.rpc('get_dashboard_stats');

        if (statsError) {
          console.error('Error al obtener estadísticas:', statsError);

          // Verificar si es un error de función no encontrada (base de datos no configurada)
          if (statsError.message?.includes('Could not find the function') ||
              statsError.message?.includes('does not exist')) {
            setDatabaseError(true);
            return;
          }

          // Fallback a consultas directas
          try {
            const [usuariosResult] = await Promise.all([
              supabase.from('usuarios').select('id', { count: 'exact', head: true })
            ]);

            setStats({
              candidatos: usuariosResult.count || 0,
              evaluaciones: 0,
              resultados: 0,
              psicologos: 0,
              instituciones: 0
            });
          } catch (fallbackError) {
            console.error('Error en consultas fallback:', fallbackError);
            setDatabaseError(true);
            return;
          }
        } else {
          setStats(dashboardStats || {});
        }
        
      } catch (error) {
        console.error('Error al cargar estadísticas:', error);
        setDatabaseError(true);
      } finally {
        setLoadingStats(false);
      }
    };

    fetchStats();
  }, []);

  const handleRetryConnection = () => {
    setDatabaseError(false);
    setLoadingStats(true);

    // Reintentar después de un pequeño delay
    setTimeout(() => {
      fetchStats();
    }, 1000);
  };

  // Mostrar guía de configuración si hay error de base de datos
  if (databaseError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg p-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <FaSpinner className="h-6 w-6 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Base de Datos No Configurada
            </h1>
            <p className="text-gray-600 mb-6">
              Las tablas de la base de datos no existen. Necesitas configurar el esquema de Supabase.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-left">
              <h3 className="font-semibold text-blue-800 mb-2">Pasos para configurar:</h3>
              <ol className="text-sm text-blue-700 space-y-1">
                <li>1. Ve a <a href="https://app.supabase.com/project/ydglduxhgwajqdseqzpy/sql" target="_blank" rel="noopener noreferrer" className="underline">Supabase SQL Editor</a></li>
                <li>2. Ejecuta el archivo <code>database/schema.sql</code></li>
                <li>3. Ejecuta el archivo <code>database/seed_data.sql</code> (opcional)</li>
                <li>4. Haz clic en "Reintentar" abajo</li>
              </ol>
            </div>

            <div className="flex justify-center space-x-4">
              <button
                onClick={handleRetryConnection}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"
              >
                Reintentar Conexión
              </button>
              <a
                href="https://app.supabase.com/project/ydglduxhgwajqdseqzpy/sql"
                target="_blank"
                rel="noopener noreferrer"
                className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors duration-200"
              >
                Abrir SQL Editor
              </a>
            </div>

            {/* Enlace temporal para continuar sin BD */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <a
                href="/simple"
                className="text-sm text-gray-500 hover:text-gray-700 underline"
              >
                Continuar sin base de datos (modo de prueba)
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (authLoading || loadingStats) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaSpinner className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Breadcrumb */}
      <Breadcrumbs />

      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardBody>
            <div className="w-full h-auto rounded-lg mb-6 flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 relative">
              {/* Banner con imagen del cerebro y texto BATERÍA DE APTITUDES */}
              <div className="relative w-full h-[300px] flex items-center justify-center">
                {/* Fondo con patrón tecnológico */}
                <div className="absolute inset-0 opacity-20">
                  <div className="absolute top-10 left-10 w-20 h-20 border border-blue-300 rounded-full"></div>
                  <div className="absolute top-20 right-20 w-16 h-16 border border-purple-300 rounded-full"></div>
                  <div className="absolute bottom-10 left-1/4 w-12 h-12 border border-cyan-300 rounded-full"></div>
                  <div className="absolute bottom-20 right-1/3 w-14 h-14 border border-pink-300 rounded-full"></div>
                </div>

                {/* Imagen del cerebro */}
                <div className="absolute left-20 top-1/2 transform -translate-y-1/2">
                  <div className="w-32 h-32 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center shadow-2xl">
                    <i className="fas fa-brain text-white text-4xl"></i>
                  </div>
                  {/* Líneas de conexión */}
                  <div className="absolute top-1/2 left-full w-20 h-0.5 bg-gradient-to-r from-cyan-400 to-transparent"></div>
                </div>

                {/* Texto principal */}
                <div className="text-center text-white z-10">
                  <h1 className="text-6xl font-black tracking-wider mb-4">
                    <span className="bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                      BAT-7
                    </span>
                  </h1>
                  <h2 className="text-2xl font-bold tracking-widest text-blue-200">
                    BATERÍA DE APTITUDES
                  </h2>
                  <div className="mt-4 flex justify-center space-x-2">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-100"></div>
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-200"></div>
                  </div>
                </div>

                {/* Elementos decorativos del lado derecho */}
                <div className="absolute right-20 top-1/2 transform -translate-y-1/2">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-500 rounded-lg flex items-center justify-center text-white font-bold">V</div>
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-lg flex items-center justify-center text-white font-bold">E</div>
                    <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-lg flex items-center justify-center text-white font-bold">A</div>
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-lg flex items-center justify-center text-white font-bold">R</div>
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold">N</div>
                    <div className="w-12 h-12 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg flex items-center justify-center text-white font-bold">M</div>
                  </div>
                </div>
              </div>
            </div>
            {/* Título principal con gradiente y descripción */}
            <div className="text-center mb-4">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-800 to-indigo-950 bg-clip-text text-transparent">
                Sistema de Evaluación Psicométrica BAT-7
              </h1>
              <p className="text-gray-600 mt-2">
                Plataforma integral para la evaluación y seguimiento de aptitudes psicológicas
              </p>
            </div>
          </CardBody>
        </Card>
      </div>
      
      {/* KPIs - Visibles para todos los usuarios */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Resumen del Sistema</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <KpiCard
            title={userRole === 'administrador' ? "Candidatos Registrados" :
                   userRole === 'psicólogo' ? "Mis Candidatos" : "Mis Evaluaciones"}
            value={stats.candidatos || stats.mis_candidatos || stats.mis_evaluaciones || 0}
            icon={<FaUsers className="text-xl" />}
            color="bg-blue-600"
            loading={loadingStats || authLoading}
          />
          <KpiCard 
            title="Evaluaciones Realizadas" 
            value={stats.evaluaciones} 
            icon={<FaClipboardCheck className="text-xl" />} 
            color="bg-green-600" 
            loading={loadingStats || authLoading} 
          />
          <KpiCard 
            title="Resultados Procesados" 
            value={stats.resultados} 
            icon={<FaChartBar className="text-xl" />} 
            color="bg-purple-600" 
            loading={loadingStats || authLoading} 
          />
        </div>
      </div>

      {/* Sección de accesos rápidos */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Accesos Rápidos</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">

          {/* Accesos para Candidatos */}
          {userRole === 'candidato' && (
            <Card
              className="shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
              onClick={() => navigate('/test/instructions/verbal')}
            >
              <CardHeader className="bg-gradient-to-r from-blue-700 to-blue-900 text-white">
                <div className="flex items-center">
                  <FaClipboardCheck className="text-2xl mr-3" />
                  <h2 className="text-xl font-semibold">Iniciar Evaluación</h2>
                </div>
              </CardHeader>
              <CardBody>
                <p className="text-gray-600">Comienza una nueva evaluación psicométrica.</p>
              </CardBody>
            </Card>
          )}

          {/* Accesos para Administradores y Psicólogos */}
          {(userRole === 'administrador' || userRole === 'psicólogo') && (
            <>
              <Card
                className="shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                onClick={() => navigate('/admin/candidates')}
              >
                <CardHeader className="bg-gradient-to-r from-indigo-700 to-indigo-900 text-white">
                  <div className="flex items-center">
                    <FaUsers className="text-2xl mr-3" />
                    <h2 className="text-xl font-semibold">Gestionar Candidatos</h2>
                  </div>
                </CardHeader>
                <CardBody>
                  <p className="text-gray-600">Administra la información de los candidatos.</p>
                </CardBody>
              </Card>

              <Card
                className="shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                onClick={() => navigate('/results')}
              >
                <CardHeader className="bg-gradient-to-r from-purple-700 to-purple-900 text-white">
                  <div className="flex items-center">
                    <FaChartBar className="text-2xl mr-3" />
                    <h2 className="text-xl font-semibold">Ver Resultados</h2>
                  </div>
                </CardHeader>
                <CardBody>
                  <p className="text-gray-600">Visualiza y analiza los resultados de las evaluaciones.</p>
                </CardBody>
              </Card>
            </>
          )}

          {/* Accesos para Administradores únicamente */}
          {userRole === 'administrador' && (
            <Card
              className="shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
              onClick={() => navigate('/admin/diagnostics')}
            >
              <CardHeader className="bg-gradient-to-r from-red-700 to-red-900 text-white">
                <div className="flex items-center">
                  <FaChartBar className="text-2xl mr-3" />
                  <h2 className="text-xl font-semibold">Diagnósticos</h2>
                </div>
              </CardHeader>
              <CardBody>
                <p className="text-gray-600">Panel de control y diagnósticos del sistema.</p>
              </CardBody>
            </Card>
          )}
        </div>
      </div>
      
      {/* Sección adicional para administradores */}
      {userRole && (userRole.includes('admin') || userRole.includes('administrador')) && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Administración</h2>
          <Card className="shadow-md">
            <CardBody className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button 
                  onClick={() => navigate('/admin/institutions')}
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-blue-100 rounded-full mr-3">
                    <FaUsers className="text-blue-600" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-medium">Gestionar Instituciones</h3>
                    <p className="text-sm text-gray-500">Administra las instituciones registradas</p>
                  </div>
                </button>
                
                <button 
                  onClick={() => navigate('/admin/psychologists')}
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-green-100 rounded-full mr-3">
                    <FaUsers className="text-green-600" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-medium">Gestionar Psicólogos</h3>
                    <p className="text-sm text-gray-500">Administra los psicólogos del sistema</p>
                  </div>
                </button>
              </div>
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  );
};

export default Home;




