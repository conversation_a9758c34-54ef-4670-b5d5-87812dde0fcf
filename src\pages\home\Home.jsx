import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '../../components/ui/Card';
import { useNavigate } from 'react-router-dom';
import { FaClipboardCheck, FaUsers, FaChartBar, FaSpinner } from 'react-icons/fa';
import supabase from '../../api/supabaseClient';
import { useAuth } from '../../context/AuthContext';

// Componente para tarjetas de KPI
const KpiCard = ({ title, value, icon, color, loading }) => (
  <Card className="shadow-md hover:shadow-lg transition-all duration-300">
    <CardBody className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-500 text-sm font-medium">{title}</p>
          {loading ? (
            <div className="flex items-center mt-1">
              <FaSpinner className="animate-spin text-gray-400 mr-2" />
              <span className="text-gray-400">Cargando...</span>
            </div>
          ) : (
            <h3 className="text-2xl font-bold mt-1">{value}</h3>
          )}
        </div>
        <div className={`p-3 rounded-full ${color} text-white`}>
          {icon}
        </div>
      </div>
    </CardBody>
  </Card>
);

const Home = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    candidatos: 0,
    evaluaciones: 0,
    resultados: 0,
    mis_candidatos: 0,
    mis_evaluaciones: 0,
    psicologos: 0,
    instituciones: 0
  });
  const [loadingStats, setLoadingStats] = useState(true);
  const { user, isAdmin, userRole, loading: authLoading } = useAuth();
  
  // Obtener estadísticas
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoadingStats(true);
        // Obtener estadísticas usando la función RPC que respeta los roles
        const { data: dashboardStats, error: statsError } = await supabase.rpc('get_dashboard_stats');

        if (statsError) {
          console.error('Error al obtener estadísticas:', statsError);
          // Fallback a consultas directas
          const [candidatosResult, evaluacionesResult, resultadosResult] = await Promise.all([
            supabase.from('candidatos').select('id', { count: 'exact', head: true }),
            supabase.from('evaluaciones').select('id', { count: 'exact', head: true }),
            supabase.from('resultados').select('id', { count: 'exact', head: true })
          ]);

          setStats({
            candidatos: candidatosResult.count || 0,
            evaluaciones: evaluacionesResult.count || 0,
            resultados: resultadosResult.count || 0
          });
        } else {
          setStats(dashboardStats || {});
        }
        
        // Este código se eliminó ya que se maneja en el bloque anterior
      } catch (error) {
        console.error('Error al cargar estadísticas:', error);
      } finally {
        setLoadingStats(false);
      }
    };
    
    fetchStats();
  }, []);

  return (
    <div>
      <div className="mb-8">
        <Card className="shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <CardBody>
            <div className="w-full h-auto rounded-lg mb-6 flex items-center justify-center overflow-hidden">
              <img
                src="/assets/images/banner.png"
                alt="BAT-7 Evaluación de Aptitudes"
                className="w-full object-cover h-[350px]"
              />
            </div>
            {/* Título principal con gradiente y descripción */}
            <div className="text-center mb-4">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-800 to-indigo-950 bg-clip-text text-transparent">
                Sistema de Evaluación Psicométrica BAT-7
              </h1>
              <p className="text-gray-600 mt-2">
                Plataforma integral para la evaluación y seguimiento de aptitudes psicológicas
              </p>
            </div>
          </CardBody>
        </Card>
      </div>
      
      {/* KPIs - Visibles para todos los usuarios */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Resumen del Sistema</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <KpiCard
            title={userRole === 'administrador' ? "Candidatos Registrados" :
                   userRole === 'psicólogo' ? "Mis Candidatos" : "Mis Evaluaciones"}
            value={stats.candidatos || stats.mis_candidatos || stats.mis_evaluaciones || 0}
            icon={<FaUsers className="text-xl" />}
            color="bg-blue-600"
            loading={loadingStats || authLoading}
          />
          <KpiCard 
            title="Evaluaciones Realizadas" 
            value={stats.evaluaciones} 
            icon={<FaClipboardCheck className="text-xl" />} 
            color="bg-green-600" 
            loading={loadingStats || authLoading} 
          />
          <KpiCard 
            title="Resultados Procesados" 
            value={stats.resultados} 
            icon={<FaChartBar className="text-xl" />} 
            color="bg-purple-600" 
            loading={loadingStats || authLoading} 
          />
        </div>
      </div>

      {/* Sección de accesos rápidos */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Accesos Rápidos</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">

          {/* Accesos para Candidatos */}
          {userRole === 'candidato' && (
            <Card
              className="shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
              onClick={() => navigate('/test/instructions/verbal')}
            >
              <CardHeader className="bg-gradient-to-r from-blue-700 to-blue-900 text-white">
                <div className="flex items-center">
                  <FaClipboardCheck className="text-2xl mr-3" />
                  <h2 className="text-xl font-semibold">Iniciar Evaluación</h2>
                </div>
              </CardHeader>
              <CardBody>
                <p className="text-gray-600">Comienza una nueva evaluación psicométrica.</p>
              </CardBody>
            </Card>
          )}

          {/* Accesos para Administradores y Psicólogos */}
          {(userRole === 'administrador' || userRole === 'psicólogo') && (
            <>
              <Card
                className="shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                onClick={() => navigate('/admin/candidates')}
              >
                <CardHeader className="bg-gradient-to-r from-indigo-700 to-indigo-900 text-white">
                  <div className="flex items-center">
                    <FaUsers className="text-2xl mr-3" />
                    <h2 className="text-xl font-semibold">Gestionar Candidatos</h2>
                  </div>
                </CardHeader>
                <CardBody>
                  <p className="text-gray-600">Administra la información de los candidatos.</p>
                </CardBody>
              </Card>

              <Card
                className="shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                onClick={() => navigate('/results')}
              >
                <CardHeader className="bg-gradient-to-r from-purple-700 to-purple-900 text-white">
                  <div className="flex items-center">
                    <FaChartBar className="text-2xl mr-3" />
                    <h2 className="text-xl font-semibold">Ver Resultados</h2>
                  </div>
                </CardHeader>
                <CardBody>
                  <p className="text-gray-600">Visualiza y analiza los resultados de las evaluaciones.</p>
                </CardBody>
              </Card>
            </>
          )}

          {/* Accesos para Administradores únicamente */}
          {userRole === 'administrador' && (
            <Card
              className="shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
              onClick={() => navigate('/admin/diagnostics')}
            >
              <CardHeader className="bg-gradient-to-r from-red-700 to-red-900 text-white">
                <div className="flex items-center">
                  <FaChartBar className="text-2xl mr-3" />
                  <h2 className="text-xl font-semibold">Diagnósticos</h2>
                </div>
              </CardHeader>
              <CardBody>
                <p className="text-gray-600">Panel de control y diagnósticos del sistema.</p>
              </CardBody>
            </Card>
          )}
        </div>
      </div>
      
      {/* Sección adicional para administradores */}
      {userRole && (userRole.includes('admin') || userRole.includes('administrador')) && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Administración</h2>
          <Card className="shadow-md">
            <CardBody className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button 
                  onClick={() => navigate('/admin/institutions')}
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-blue-100 rounded-full mr-3">
                    <FaUsers className="text-blue-600" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-medium">Gestionar Instituciones</h3>
                    <p className="text-sm text-gray-500">Administra las instituciones registradas</p>
                  </div>
                </button>
                
                <button 
                  onClick={() => navigate('/admin/psychologists')}
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-green-100 rounded-full mr-3">
                    <FaUsers className="text-green-600" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-medium">Gestionar Psicólogos</h3>
                    <p className="text-sm text-gray-500">Administra los psicólogos del sistema</p>
                  </div>
                </button>
              </div>
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  );
};

export default Home;




