import { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import Layout from '../components/layout/Layout';
import LoadingFallback from '../components/ui/LoadingFallback';
import ProtectedRoute from '../components/auth/ProtectedRoute';

// Páginas de autenticación
const LoginPage = lazy(() => import('../pages/auth/LoginPage'));
const RegisterPage = lazy(() => import('../pages/auth/RegisterPage'));
const UnauthorizedPage = lazy(() => import('../pages/auth/UnauthorizedPage'));

// Páginas principales
const Home = lazy(() => import('../pages/home/<USER>'));
const Dashboard = lazy(() => import('../pages/dashboard/Dashboard'));

// Páginas de administración
const Administration = lazy(() => import('../pages/admin/Administration'));
const Institutions = lazy(() => import('../pages/admin/Institutions'));
const Psychologists = lazy(() => import('../pages/admin/Psychologists'));
const Diagnostics = lazy(() => import('../pages/admin/Diagnostics'));

// Páginas de candidatos (migrado de pacientes)
const Candidates = lazy(() => import('../pages/admin/Candidates'));

// Páginas de evaluaciones
const Instructions = lazy(() => import('../pages/test/Instructions'));
const VerbalTest = lazy(() => import('../pages/test/Verbal'));
const TestResults = lazy(() => import('../pages/test/Results'));

// Páginas de resultados
const Results = lazy(() => import('../pages/student/Results'));

// Componentes internos para AppRoutes
const AppRoutesInternal = () => {
  return (
    <ErrorBoundary fallback={<div>Ha ocurrido un error inesperado.</div>}>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          {/* Rutas públicas */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="/unauthorized" element={<UnauthorizedPage />} />
          
          {/* Rutas para todos los usuarios autenticados */}
          <Route element={<ProtectedRoute />}>
            <Route element={<Layout />}>
              {/* Ruta principal - Redirige a la página de inicio */}
              <Route path="/" element={<Navigate to="/home" replace />} />
              <Route path="/home" element={<Home />} />

              {/* Rutas de evaluaciones - Accesibles para todos */}
              <Route path="/test/instructions/:type" element={<Instructions />} />
              <Route path="/test/verbal" element={<VerbalTest />} />
              <Route path="/cuestionario" element={<VerbalTest />} />
            </Route>
          </Route>

          {/* Rutas para psicólogos y administradores */}
          <Route element={<ProtectedRoute allowedRoles={['psicologo', 'psicólogo', 'admin', 'administrador']} />}>
            <Route element={<Layout />}>
              {/* Gestión de candidatos */}
              <Route path="/admin/candidates" element={<Candidates />} />

              {/* Rutas de resultados */}
              <Route path="/results" element={<Results />} />
              <Route path="/results/:id" element={<Results />} />
              <Route path="/test/results/:id" element={<TestResults />} />

              {/* Informes guardados */}
              <Route path="/informes-guardados" element={<Results />} />
            </Route>
          </Route>

          {/* Rutas protegidas - solo para administradores */}
          <Route element={<ProtectedRoute allowedRoles={['admin', 'administrador']} />}>
            <Route element={<Layout />}>
              <Route path="/admin/administration" element={<Administration />} />
              <Route path="/admin/institutions" element={<Institutions />} />
              <Route path="/admin/psychologists" element={<Psychologists />} />
              <Route path="/admin/diagnostics" element={<Diagnostics />} />
              <Route path="/dashboard" element={<Dashboard />} />
            </Route>
          </Route>
          
          {/* Ruta de fallback para rutas no encontradas */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

// Componente principal de rutas
const AppRoutes = () => {
  return <AppRoutesInternal />;
};

export default AppRoutes;
