import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import Layout from '../components/layout/Layout';
import LoadingFallback from '../components/ui/LoadingFallback';
import ProtectedRoute from '../components/auth/ProtectedRoute';

// Páginas de autenticación
const LoginPage = lazy(() => import('../pages/auth/LoginPage'));
const UnauthorizedPage = lazy(() => import('../pages/auth/UnauthorizedPage'));

// Páginas principales
const Home = lazy(() => import('../pages/home/<USER>'));
const Dashboard = lazy(() => import('../pages/dashboard/Dashboard'));

// Páginas de administración
const Administration = lazy(() => import('../pages/admin/Administration'));
const Institutions = lazy(() => import('../pages/admin/Institutions'));
const Psychologists = lazy(() => import('../pages/admin/Psychologists'));

// Páginas de estudiantes/pacientes
const Patients = lazy(() => import('../pages/student/Patients'));
const PatientDetail = lazy(() => import('../pages/student/PatientDetail'));

// Páginas de evaluaciones
const TestInstructions = lazy(() => import('../pages/test/TestInstructions'));
const TestPage = lazy(() => import('../pages/test/TestPage'));
const TestResults = lazy(() => import('../pages/test/TestResults'));

// Páginas de resultados
const Results = lazy(() => import('../pages/results/Results'));

// Componentes internos para AppRoutes
const AppRoutesInternal = () => {
  return (
    <ErrorBoundary fallback={<div>Ha ocurrido un error inesperado.</div>}>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          {/* Rutas públicas */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/unauthorized" element={<UnauthorizedPage />} />
          
          {/* Rutas protegidas - requieren autenticación */}
          <Route element={<ProtectedRoute />}>
            <Route element={<Layout />}>
              {/* Ruta principal - Redirige a la página de inicio */}
              <Route path="/" element={<Navigate to="/home" replace />} />
              <Route path="/home" element={<Home />} />
              <Route path="/dashboard" element={<Dashboard />} />
              
              {/* Rutas de estudiantes/pacientes */}
              <Route path="/student/patients" element={<Patients />} />
              <Route path="/student/patients/:id" element={<PatientDetail />} />
              
              {/* Rutas de evaluaciones */}
              <Route path="/test/instructions/:type" element={<TestInstructions />} />
              <Route path="/test/:type" element={<TestPage />} />
              <Route path="/test/results/:id" element={<TestResults />} />
              
              {/* Rutas de resultados */}
              <Route path="/results" element={<Results />} />
              <Route path="/results/:id" element={<Results />} />
            </Route>
          </Route>
          
          {/* Rutas protegidas - solo para administradores */}
          <Route element={<ProtectedRoute allowedRoles={['admin', 'administrador']} />}>
            <Route element={<Layout />}>
              <Route path="/admin/administration" element={<Administration />} />
              <Route path="/admin/institutions" element={<Institutions />} />
              <Route path="/admin/psychologists" element={<Psychologists />} />
            </Route>
          </Route>
          
          {/* Ruta de fallback para rutas no encontradas */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

// Componente principal de rutas
const AppRoutes = () => {
  return <AppRoutesInternal />;
};

export default AppRoutes;
