import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';

// Importar directamente sin lazy loading para debugging
import DevNavigation from '../pages/DevNavigation';
import SimpleHome from '../pages/home/<USER>';
import SimpleLayout from '../components/layout/SimpleLayout';

// Componente de error simple
const ErrorFallback = ({ error }) => (
  <div style={{
    padding: '2rem',
    textAlign: 'center',
    backgroundColor: '#fee2e2',
    border: '1px solid #fecaca',
    borderRadius: '8px',
    margin: '2rem'
  }}>
    <h2 style={{ color: '#dc2626', marginBottom: '1rem' }}>
      Error en la aplicación
    </h2>
    <p style={{ color: '#7f1d1d', marginBottom: '1rem' }}>
      {error.message}
    </p>
    <button 
      onClick={() => window.location.reload()}
      style={{
        backgroundColor: '#dc2626',
        color: 'white',
        padding: '0.5rem 1rem',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer'
      }}
    >
      Recargar página
    </button>
  </div>
);

// Componente de loading simple
const LoadingFallback = () => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '50vh',
    fontSize: '1.125rem',
    color: '#6b7280'
  }}>
    Cargando...
  </div>
);

// Componentes de rutas simplificadas
const SimpleAppRoutes = () => {
  console.log('SimpleAppRoutes: Renderizando rutas');

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          {/* Ruta principal */}
          <Route path="/" element={<DevNavigation />} />
          
          {/* Rutas con layout */}
          <Route element={<SimpleLayout />}>
            <Route path="/home" element={<SimpleHome />} />
            
            {/* Rutas de evaluaciones - páginas simples por ahora */}
            <Route path="/test/verbal" element={
              <div style={{ padding: '2rem', textAlign: 'center' }}>
                <h1>Evaluación Verbal</h1>
                <p>Esta página está en desarrollo</p>
                <a href="/" style={{ color: '#3b82f6' }}>← Volver al inicio</a>
              </div>
            } />
            
            <Route path="/test/numerico" element={
              <div style={{ padding: '2rem', textAlign: 'center' }}>
                <h1>Evaluación Numérica</h1>
                <p>Esta página está en desarrollo</p>
                <a href="/" style={{ color: '#3b82f6' }}>← Volver al inicio</a>
              </div>
            } />
            
            <Route path="/dashboard" element={
              <div style={{ padding: '2rem', textAlign: 'center' }}>
                <h1>Dashboard</h1>
                <p>Esta página está en desarrollo</p>
                <a href="/" style={{ color: '#3b82f6' }}>← Volver al inicio</a>
              </div>
            } />
            
            <Route path="/admin/administration" element={
              <div style={{ padding: '2rem', textAlign: 'center' }}>
                <h1>Administración</h1>
                <p>Esta página está en desarrollo</p>
                <a href="/" style={{ color: '#3b82f6' }}>← Volver al inicio</a>
              </div>
            } />
          </Route>
          
          {/* Ruta de fallback */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

export default SimpleAppRoutes;
