// src/scripts/createTestUsers.js
import supabase from '../api/supabaseClient';

/**
 * Script para crear usuarios de prueba en Supabase
 * Actualizado para usar la nueva estructura de usuarios con roles:
 * - administrador
 * - psicologo
 * - estudiante
 */
const createTestUsers = async () => {
  console.log('Iniciando creación de usuarios de prueba...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'Admin123!',
      nombre: 'Administrador',
      apellido: 'Sistema',
      documento: '12345678',
      tipo_usuario: 'Administrador'
    },
    {
      email: '<EMAIL>',
      password: 'Psico123!',
      nombre: 'Dr. <PERSON>',
      apellido: '<PERSON>',
      documento: '87654321',
      tipo_usuario: 'Psicólogo'
    },
    {
      email: '<EMAIL>',
      password: 'Candidato123!',
      nombre: '<PERSON>',
      apellido: '<PERSON>',
      documento: '11223344',
      tipo_usuario: 'Candidato'
    }
  ];

  for (const user of testUsers) {
    try {
      // Verificar si el usuario ya existe por documento
      const { data: existingUsers, error: checkError } = await supabase
        .from('usuarios')
        .select('*')
        .eq('documento', user.documento)
        .limit(1);

      if (checkError) {
        console.error(`Error al verificar usuario ${user.email}:`, checkError.message);
        continue;
      }

      if (existingUsers && existingUsers.length > 0) {
        console.log(`Usuario con documento ${user.documento} ya existe, omitiendo...`);
        continue;
      }

      // Crear usuario en Auth
      const { data, error } = await supabase.auth.signUp({
        email: user.email,
        password: user.password,
        options: {
          data: {
            nombre: user.nombre,
            apellido: user.apellido
          }
        }
      });

      if (error) {
        console.error(`Error al crear usuario ${user.email}:`, error.message);
      } else {
        console.log(`Usuario ${user.email} creado con éxito en Auth`);

        // Crear entrada en la tabla usuarios
        const { error: profileError } = await supabase
          .from('usuarios')
          .insert([
            {
              id: data.user.id,
              documento: user.documento,
              nombre: user.nombre,
              apellido: user.apellido,
              tipo_usuario: user.tipo_usuario,
              activo: true,
              fecha_creacion: new Date().toISOString()
            }
          ]);

        if (profileError) {
          console.error(`Error al crear perfil para ${user.email}:`, profileError.message);
        } else {
          console.log(`✅ Usuario ${user.tipo_usuario} creado: ${user.email} (doc: ${user.documento})`);
        }
      }
    } catch (error) {
      console.error(`Error inesperado al crear usuario ${user.email}:`, error.message);
    }
  }

  console.log('✨ Proceso de creación de usuarios de prueba completado');
  console.log('\n📋 Usuarios de prueba creados:');
  console.log('👑 Administrador: <EMAIL> / Admin123! (documento: 12345678)');
  console.log('👨‍⚕️ Psicólogo: <EMAIL> / Psico123! (documento: 87654321)');
  console.log('👨‍🎓 Candidato: <EMAIL> / Candidato123! (documento: 11223344)');
};

// Función para verificar usuarios existentes
const verifyUsers = async () => {
  console.log('🔍 Verificando usuarios existentes...');

  try {
    const { data: users, error } = await supabase
      .from('usuarios')
      .select('id, documento, nombre, apellido, tipo_usuario, activo')
      .order('tipo_usuario');

    if (error) {
      console.error('Error al obtener usuarios:', error);
      return;
    }

    console.log('👥 Usuarios en la base de datos:');
    users.forEach(user => {
      console.log(`- ${user.nombre} ${user.apellido} (${user.tipo_usuario}) - Doc: ${user.documento} - Activo: ${user.activo}`);
    });

  } catch (error) {
    console.error('Error al verificar usuarios:', error);
  }
};

// Ejecutar la función
createTestUsers()
  .catch(error => {
    console.error('Error en el script:', error);
  })
  .finally(() => {
    console.log('Script finalizado');
  });

export { createTestUsers, verifyUsers };
